# Local Cache System Documentation

## Overview

This application now includes a comprehensive local cache system that maintains manager, team, and player data locally. This reduces the need for frequent API calls and provides immediate UI updates when data changes.

## Architecture

### Core Components

1. **DataCacheContext** (`src/context/DataCacheContext.tsx`)
   - Main cache management context
   - Provides cache state and update functions
   - Uses React's useReducer for state management

2. **Cache Utilities** (`src/utils/cacheUtils.ts`)
   - Helper functions for cache operations
   - Data structure definitions
   - Cache validation and update logic

3. **Cached Data Hooks** (`src/hooks/useCachedData.ts`)
   - Custom hooks that integrate React Query with the cache
   - Automatic synchronization between API and cache
   - Stale data detection and refresh logic

4. **Updated ManagerContext** (`src/context/ManagerContext.tsx`)
   - Now integrates with the cache system
   - Provides update functions for manager and team data

## Data Structure

The cache stores the following data:

```typescript
interface DataCache {
  manager: Manager | null;
  team: Team | null;
  players: {
    teamPlayers: Player[];
    transferListPlayers: TransferListPlayer[];
    scoutedPlayers: ScoutedPlayer[];
    myBidsPlayers: TransferListPlayer[];
  };
  lastUpdated: {
    manager: number;
    team: number;
    players: number;
  };
}
```

## Usage Examples

### 1. Using Cached Data in Components

```typescript
import { useManager } from '../context/ManagerContext';
import { useDataCache } from '../context/DataCacheContext';

const MyComponent = () => {
  const { manager, team, updateManager, updateTeam } = useManager();
  const { updatePlayer, findPlayer } = useDataCache();

  // Data is automatically cached and available immediately
  console.log('Manager:', manager);
  console.log('Team:', team);
};
```

### 2. Updating Player Data

```typescript
// After a successful API operation (e.g., using magic sponge)
const handleUseMagicSponge = async () => {
  try {
    const updatedPlayer = await useMagicSpongeAPI(playerId);
    
    // Update the cache immediately
    updatePlayer({
      playerId: player.playerId,
      energy: updatedPlayer.energy,
      injuredUntil: updatedPlayer.injuredUntil,
    });
    
    // Update manager's magic sponges count
    updateManager({
      magicSponges: Math.max(0, manager.magicSponges - 1),
    });
  } catch (error) {
    // Handle error
  }
};
```

### 3. Updating Manager Data

```typescript
// Update manager data in cache
updateManager({
  scoutTokens: manager.scoutTokens - 1,
  superScoutTokens: manager.superScoutTokens + 1,
});
```

### 4. Updating Team Data

```typescript
// Update team balance after a transaction
updateTeam({
  balance: team.balance - transferAmount,
});
```

### 5. Using Cached Transfer List Data

```typescript
import { useCachedTransferListPlayers } from '../hooks/useCachedData';

const TransferScreen = () => {
  const { data, isLoading, shouldRefresh } = useCachedTransferListPlayers(
    gameworldId,
    lastEvaluatedKey
  );
  
  // Data comes from cache first, API second
  const players = data?.players || [];
};
```

## Key Features

### 1. Automatic Synchronization
- Cache automatically syncs with API data when available
- React Query integration ensures data consistency
- Stale data detection with configurable refresh intervals

### 2. Immediate UI Updates
- Changes are reflected in the UI instantly
- No need to wait for API responses for visual feedback
- Optimistic updates with error handling

### 3. Cross-Component Data Sharing
- Player updates in one component are immediately visible in others
- Manager and team data is shared across the entire app
- Consistent data state throughout the application

### 4. Efficient Data Management
- Reduces API calls by serving data from cache
- Intelligent cache invalidation based on data age
- Pagination support for large data sets

## Implementation Details

### Cache Update Patterns

1. **Player Operations** (Magic Sponge, Training, etc.)
   ```typescript
   // 1. Call API
   const result = await apiOperation();
   
   // 2. Update cache immediately
   updatePlayer({
     playerId: player.playerId,
     ...result
   });
   
   // 3. Update related data if needed
   updateManager({ resourceCount: manager.resourceCount - 1 });
   ```

2. **Transfer Operations**
   ```typescript
   // Update bid history and auction data
   updatePlayer({
     playerId: player.playerId,
     bidHistory: [...existingBids, newBid],
     auctionCurrentPrice: newPrice,
   });
   ```

3. **Team Management**
   ```typescript
   // Remove player from team
   updateTeam({
     players: team.players.filter(p => p.playerId !== releasedPlayerId),
   });
   ```

### Cache Refresh Strategy

- **Manager data**: 5 minutes
- **Team data**: 5 minutes  
- **Transfer list**: 3 minutes
- **My bids**: 2 minutes
- **Scouted players**: 10 minutes

## Best Practices

1. **Always update cache after successful API operations**
2. **Use the provided hooks instead of direct React Query**
3. **Handle errors gracefully - cache updates should be in try blocks**
4. **Use the findPlayer utility to locate players across all cached arrays**
5. **Update related data when operations affect multiple entities**

## Migration Guide

### Before (Direct React Query)
```typescript
const { data: manager } = useManagerQuery();
const { data: team } = useTeam(gameworldId, teamId);
```

### After (Cached Data)
```typescript
const { manager, team, updateManager, updateTeam } = useManager();
// Data is automatically cached and update functions are available
```

## Testing

The cache system includes comprehensive error handling and logging. Check the console for cache operations:

- Cache updates are logged with details
- Stale data refresh operations are logged
- Error states are properly handled

## Performance Benefits

1. **Reduced API Calls**: Data served from cache when available
2. **Faster UI Updates**: Immediate visual feedback
3. **Better User Experience**: No loading states for cached data
4. **Offline Resilience**: App continues to work with cached data

## Future Enhancements

1. **Persistence**: Save cache to AsyncStorage for offline access
2. **Selective Refresh**: Refresh only specific data types
3. **Cache Size Management**: Automatic cleanup of old data
4. **Real-time Updates**: WebSocket integration for live data updates
