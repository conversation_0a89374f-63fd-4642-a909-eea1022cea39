import * as NavigationBar from 'expo-navigation-bar';
import { Stack } from 'expo-router';
import React, { useEffect } from 'react';
import { Image, Platform, View } from 'react-native';
import styled from 'styled-components/native';
import { BannerAd, RewardAdProvider } from '../../src/components/Ads';
import { AppLayoutGuard } from '../../src/components/auth/AppLayoutGuard';
import { AuthFlowManager } from '../../src/components/auth/AuthFlowManager';
import VictoryCelebrationModal from '../../src/components/Celebration/VictoryCelebrationModal';
import { HamburgerMenu } from '../../src/components/HamburgerMenu';
import { MailIcon } from '../../src/components/MailIcon';
import DailyRewardsModal from '../../src/components/Rewards/DailyRewardsModal';
import { DataCacheProvider } from '../../src/context/DataCacheContext';
import { ManagerProvider, useManager } from '../../src/context/ManagerContext';
import { PurchaseProvider } from '../../src/context/PurchaseContext';
import { useNotificationScheduler } from '../../src/hooks/useNotificationScheduler';
import { logger } from '../../src/utils/logger';

function NotificationInitializer() {
  const { updateNotificationScheduling } = useNotificationScheduler();

  useEffect(() => {
    // Initialize notification scheduling when the app starts
    updateNotificationScheduling();
  }, [updateNotificationScheduling]);

  return null;
}

const ContentContainer = styled.View`
  flex: 1;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
`;

const MainContent = styled.View`
  flex: 1;
`;

export default function AppLayout() {
  logger.log('AppLayout rendered');

  useEffect(() => {
    if (Platform.OS === 'android') {
      NavigationBar.setVisibilityAsync('hidden');
    }
  }, []);

  // Use AuthFlowManager to handle the authentication flow and migration
  return (
    <DataCacheProvider>
      <ManagerProvider>
        <AuthFlowManager>
          <AppLayoutGuard key="App Layout Guard">
            <PurchaseProvider>
              <RewardAdProvider>
                <NotificationInitializer />
                <ContentContainer>
                  <MainContent>
                    <Stack
                      screenOptions={{
                        headerTitleStyle: {
                          fontFamily: 'NunitoBold',
                          fontSize: 20,
                        },
                        title: '',
                        headerBackVisible: true,
                        headerLeft: () => (
                          <Image
                            source={require('../../assets/logo-short.png')}
                            style={{ height: 50, width: 160, marginBottom: 0 }}
                          />
                        ),
                        headerRight: () => (
                          <View style={{ flexDirection: 'row', alignItems: 'center' }}>
                            <MailIcon size={24} color="black" />
                            <HamburgerMenu loading={false} />
                          </View>
                        ),
                      }}
                      key="Authenticated Stack"
                    >
                      <Stack.Screen name="index" options={{ headerShown: false }} />
                    </Stack>
                  </MainContent>
                  <BannerAd />
                </ContentContainer>
                <DailyRewardsModal />
                <VictoryCelebrationModal />
              </RewardAdProvider>
            </PurchaseProvider>
          </AppLayoutGuard>
        </AuthFlowManager>
      </ManagerProvider>
    </DataCacheProvider>
  );
}
