import React, { useState } from 'react';
import { ScrollView } from 'react-native';
import styled from 'styled-components/native';
import { StyledProps } from '../../src/components/Common';
import { FeedbackModal } from '../../src/components/FeedbackModal';
import { Text } from '../../src/components/Text';

const Container = styled.View`
  flex: 1;
  background-color: ${(props: StyledProps) => props.theme.colors.background};
  padding: 20px;
`;

const Title = styled(Text)`
  font-size: 24px;
  font-family: ${(props: StyledProps) => props.theme.typography.bold};
  margin-bottom: 20px;
  text-align: center;
`;

const Description = styled(Text)`
  font-size: 16px;
  margin-bottom: 30px;
  text-align: center;
  color: ${(props: StyledProps) => props.theme.colors.text.secondary};
`;

const Button = styled.TouchableOpacity`
  background-color: ${(props: StyledProps) => props.theme.colors.primary};
  border-radius: 8px;
  padding: 16px;
  align-items: center;
  margin-bottom: 16px;
`;

const ButtonText = styled(Text)`
  color: #ffffff;
  font-size: 16px;
  font-family: ${(props: StyledProps) => props.theme.typography.bold};
`;

const InfoSection = styled.View`
  background-color: ${(props: StyledProps) => props.theme.colors.surface};
  border-radius: 8px;
  padding: 16px;
  margin-bottom: 20px;
`;

const InfoTitle = styled(Text)`
  font-size: 18px;
  font-family: ${(props: StyledProps) => props.theme.typography.bold};
  margin-bottom: 8px;
`;

const InfoText = styled(Text)`
  font-size: 14px;
  color: ${(props: StyledProps) => props.theme.colors.text.secondary};
  line-height: 20px;
`;

export default function FeedbackScreen() {
  const [isModalVisible, setIsModalVisible] = useState(false);

  return (
    <Container>
      <ScrollView showsVerticalScrollIndicator={false}>
        <Title>Send Feedback</Title>

        <Description>
          Help us improve Jumpers for Goalposts by sharing your thoughts, reporting bugs, or
          suggesting new features.
        </Description>

        <Button onPress={() => setIsModalVisible(true)}>
          <ButtonText>Open Feedback Form</ButtonText>
        </Button>

        <InfoSection>
          <InfoTitle>How to Access Feedback</InfoTitle>
          <InfoText>
            You can access the feedback form in several ways:{'\n\n'}• From the hamburger menu (top
            right){'\n'}• By shaking your device{'\n'}• Directly from this page
          </InfoText>
        </InfoSection>

        <InfoSection>
          <InfoTitle>Types of Feedback</InfoTitle>
          <InfoText>
            • Bug Report: Report issues or problems you've encountered{'\n'}• Feature Request:
            Suggest new features or improvements{'\n'}• General Feedback: Share your thoughts about
            the app
          </InfoText>
        </InfoSection>

        <InfoSection>
          <InfoTitle>What Happens Next?</InfoTitle>
          <InfoText>
            Your feedback is sent directly to our development team and helps us prioritize
            improvements and fixes. We appreciate every piece of feedback we receive!
          </InfoText>
        </InfoSection>
      </ScrollView>

      <FeedbackModal visible={isModalVisible} onClose={() => setIsModalVisible(false)} />
    </Container>
  );
}
