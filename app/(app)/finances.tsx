import { MaterialIcons } from '@expo/vector-icons';
import { Stack } from 'expo-router';
import React from 'react';
import { View } from 'react-native';
import { HamburgerMenu } from '../../src/components/HamburgerMenu';
import { MailIcon } from '../../src/components/MailIcon';
import { useManager } from '../../src/context/ManagerContext';
import FinancesScreen from '../../src/screens/FinancesScreen';
import { useTheme } from '../../src/theme/ThemeContext';
export default function Finances() {
  const { loading } = useManager();
  const { theme } = useTheme();
  return (
    <>
      <FinancesScreen />
    </>
  );
}
