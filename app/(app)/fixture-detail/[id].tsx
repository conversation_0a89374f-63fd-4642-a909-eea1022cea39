import FontAwesome6 from '@expo/vector-icons/FontAwesome6';
import { Stack, useLocalSearchParams } from 'expo-router';
import React from 'react';
import { View } from 'react-native';
import { HamburgerMenu } from '../../../src/components/HamburgerMenu';
import { MailIcon } from '../../../src/components/MailIcon';
import { useManager } from '../../../src/context/ManagerContext';
import FixtureDetailScreen from '../../../src/screens/FixtureDetailScreen';
import { useTheme } from '../../../src/theme/ThemeContext';

function FixtureDetail() {
  const { loading } = useManager();
  const { id } = useLocalSearchParams<{ id: string }>();
  const { theme } = useTheme();

  return (
    <>
      <Stack.Screen
        options={{
          title: 'MATCH DETAILS',
          headerTitleStyle: {
            fontFamily: theme.typography.bold,
            fontSize: 20,
          },
          headerLeft: () => (
            <FontAwesome6 name="futbol" size={40} color="black" style={{ paddingHorizontal: 10 }} />
          ),
          headerRight: () => (
            <View style={{ flexDirection: 'row', alignItems: 'center' }}>
              <MailIcon size={24} color="black" />
              <HamburgerMenu loading={loading} />
            </View>
          ),
        }}
      />
      <FixtureDetailScreen fixtureId={id} />
    </>
  );
}

export default FixtureDetail;
