import FontAwesome from '@expo/vector-icons/FontAwesome';
import { Stack } from 'expo-router';
import React from 'react';
import { View } from 'react-native';
import { HamburgerMenu } from '../../src/components/HamburgerMenu';
import { MailIcon } from '../../src/components/MailIcon';
import { useManager } from '../../src/context/ManagerContext';
import FixturesScreen from '../../src/screens/FixturesScreen';
import { useTheme } from '../../src/theme/ThemeContext';
export default function Fixtures() {
  const { loading } = useManager();
  const { theme } = useTheme();
  return (
    <>
      <FixturesScreen />
    </>
  );
}
