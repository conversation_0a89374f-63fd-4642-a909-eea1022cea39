import { MaterialIcons } from '@expo/vector-icons';
import { Stack } from 'expo-router';
import React from 'react';
import { View } from 'react-native';
import { HamburgerMenu } from '../../src/components/HamburgerMenu';
import { useManager } from '../../src/context/ManagerContext';
import InboxScreen from '../../src/screens/InboxScreen';
import { useTheme } from '../../src/theme/ThemeContext';
export default function Inbox() {
  const { loading } = useManager();
  const { theme } = useTheme();
  return (
    <>
      <InboxScreen />
    </>
  );
}
