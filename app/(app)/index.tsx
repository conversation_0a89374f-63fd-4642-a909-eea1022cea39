import AsyncStorage from '@react-native-async-storage/async-storage';
import { router } from 'expo-router';
import React, { useEffect, useState } from 'react';
import { ActivityIndicator, TouchableOpacity } from 'react-native';
import styled from 'styled-components/native';
import { callApi } from '../../src/api/client';
import { StyledProps } from '../../src/components/Common';
import { Text } from '../../src/components/Text';
import { LoadingContainer } from '../../src/components/TransferSharedStyles';
import { useAuth } from '../../src/context/AuthContext';
import { HAS_ONBOARDED_KEY, useManager } from '../../src/context/ManagerContext';

const Container = styled.View<StyledProps>`
  flex: 1;
  background-color: ${({ theme }) => theme.colors.background};
  padding: 20px;
  justify-content: center;
  align-items: center;
`;

export default function TeamAssignment() {
  const { logout } = useAuth();
  const { loading, manager, team } = useManager();
  const [showCancelButton, setShowCancelButton] = useState(false);

  useEffect(() => {
    // Timer to show cancel button after 7 seconds
    const timer = setTimeout(() => {
      setShowCancelButton(true);
    }, 10000);

    return () => clearTimeout(timer);
  }, []);

  useEffect(() => {
    async function checkTeamAssigned() {
      if (!loading) {
        const hasOnboarded = await AsyncStorage.getItem(HAS_ONBOARDED_KEY);
        if (manager && team) {
          if (hasOnboarded === 'true') {
            router.replace('/home');
          } else {
            router.replace('/alpha-notice');
          }
        } else {
          try {
            await callApi('/auth/signup', {
              method: 'POST',
              body: JSON.stringify({}),
            });
            if (hasOnboarded === 'true') {
              router.replace('/home');
            } else {
              router.replace('/alpha-notice');
            }
          } catch (error) {
            console.log('Error signing up:', error);
            await logout();
            //router.replace('/welcome');
          }
        }
      }
    }
    checkTeamAssigned();
  }, [loading, logout, manager, team]);

  const handleCancel = async () => {
    await logout();
    //router.replace('/welcome');
  };

  return (
    <Container>
      <LoadingContainer>
        <ActivityIndicator size="large" />
        <Text>Getting team data...</Text>
        {showCancelButton && (
          <TouchableOpacity
            onPress={handleCancel}
            style={{
              marginTop: 20,
              padding: 10,
              backgroundColor: '#ff4444',
              borderRadius: 5,
            }}
          >
            <Text style={{ color: 'white', textAlign: 'center' }}>Cancel</Text>
          </TouchableOpacity>
        )}
      </LoadingContainer>
    </Container>
  );
}
