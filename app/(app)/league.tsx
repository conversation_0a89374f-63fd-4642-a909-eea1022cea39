import FontAwesome6 from '@expo/vector-icons/FontAwesome6';
import { Stack } from 'expo-router';
import React from 'react';
import { View } from 'react-native';
import { HamburgerMenu } from '../../src/components/HamburgerMenu';
import { MailIcon } from '../../src/components/MailIcon';
import { useManager } from '../../src/context/ManagerContext';
import LeagueScreen from '../../src/screens/LeagueScreen';
import { useTheme } from '../../src/theme/ThemeContext';
export default function League() {
  const { loading } = useManager();
  const { theme } = useTheme();
  return (
    <>
      <LeagueScreen />
    </>
  );
}
