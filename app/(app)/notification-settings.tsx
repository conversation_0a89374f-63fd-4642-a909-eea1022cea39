import { FontAwesome } from '@expo/vector-icons';
import { Stack } from 'expo-router';
import React from 'react';
import { View } from 'react-native';
import { HamburgerMenu } from '../../src/components/HamburgerMenu';
import { MailIcon } from '../../src/components/MailIcon';
import { useManager } from '../../src/context/ManagerContext';
import { useOnboarding } from '../../src/hooks/useOnboarding';
import NotificationSettingsScreen from '../../src/screens/NotificationSettingsScreen';
import { useTheme } from '../../src/theme/ThemeContext';
export default function NotificationSettings() {
  const { loading } = useManager();
  const { theme } = useTheme();
  const { hasOnboarded } = useOnboarding();

  return (
    <>
      <NotificationSettingsScreen />
    </>
  );
}
