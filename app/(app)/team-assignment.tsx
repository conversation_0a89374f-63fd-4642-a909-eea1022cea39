import { FontAwesome } from '@expo/vector-icons';
import { Stack } from 'expo-router';
import React from 'react';
import { View } from 'react-native';
import { HamburgerMenu } from '../../src/components/HamburgerMenu';
import { MailIcon } from '../../src/components/MailIcon';
import { useManager } from '../../src/context/ManagerContext';
import TeamAssignmentScreen from '../../src/screens/TeamAssignmentScreen';
import { useTheme } from '../../src/theme/ThemeContext';
export default function TeamAssignment() {
  const { loading } = useManager();
  const { theme } = useTheme();
  return (
    <>
      <TeamAssignmentScreen />
    </>
  );
}
