import { MaterialIcons } from '@expo/vector-icons';
import { Stack } from 'expo-router';
import React from 'react';
import { View } from 'react-native';
import { HamburgerMenu } from '../../src/components/HamburgerMenu';
import { MailIcon } from '../../src/components/MailIcon';
import { useManager } from '../../src/context/ManagerContext';
import TeamSettingsScreen from '../../src/screens/TeamSettingsScreen';
import { useTheme } from '../../src/theme/ThemeContext';
export default function TeamSettings() {
  const { loading } = useManager();
  const { theme } = useTheme();
  return (
    <>
      <TeamSettingsScreen />
    </>
  );
}
