import { MaterialIcons } from '@expo/vector-icons';
import { Stack } from 'expo-router';
import React from 'react';
import { View } from 'react-native';
import { HamburgerMenu } from '../../src/components/HamburgerMenu';
import { MailIcon } from '../../src/components/MailIcon';
import { useManager } from '../../src/context/ManagerContext';
import TransfersScreen from '../../src/screens/TransfersScreen';
import { useTheme } from '../../src/theme/ThemeContext';

export default function Transfers() {
  const { loading } = useManager();
  const { theme } = useTheme();

  return (
    <>
      <TransfersScreen />
    </>
  );
}
