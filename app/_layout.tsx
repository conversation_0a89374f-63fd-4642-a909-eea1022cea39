import { Authenticator } from '@aws-amplify/ui-react-native';
import AsyncStorage from '@react-native-async-storage/async-storage';
import analytics from '@react-native-firebase/analytics';
import { initializeApp, ReactNativeFirebase } from '@react-native-firebase/app';
import * as Sentry from '@sentry/react-native';
import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import { isRunningInExpoGo } from 'expo';
import { useFonts } from 'expo-font';
import { router, Stack, useNavigationContainerRef, usePathname } from 'expo-router';
import React, { StrictMode, useEffect } from 'react';
import { ActivityIndicator, Platform } from 'react-native';
import 'react-native-gesture-handler';
import { GestureHandlerRootView } from 'react-native-gesture-handler';
import 'react-native-get-random-values';
import 'react-native-reanimated';
import styled from 'styled-components/native';
import { LoadingContainer } from '../src/components/TransferSharedStyles';
import { AuthProvider, useAuth } from '../src/context/AuthContext';
import { GAMEWORLD_ID_KEY, MANAGER_ID_KEY, TEAM_ID_KEY } from '../src/context/ManagerContext';
import { VersionProvider } from '../src/context/VersionContext';
import { ThemeProvider } from '../src/theme/ThemeContext';
import { logger } from '../src/utils/logger';
import FirebaseAppOptions = ReactNativeFirebase.FirebaseAppOptions;

// Construct a new integration instance. This is needed to communicate between the integration and React
export const navigationIntegration = Sentry.reactNavigationIntegration({
  enableTimeToInitialDisplay: !isRunningInExpoGo(),
});

if (!__DEV__) {
  Sentry.init({
    dsn: 'https://<EMAIL>/4509403176108112',

    // Adds more context data to events (IP address, cookies, user, etc.)
    // For more information, visit: https://docs.sentry.io/platforms/react-native/data-management/data-collected/
    sendDefaultPii: true,

    debug: false, // If `true`, Sentry will try to print out useful debugging information if something goes wrong with sending the event. Set it to `false` in production
    tracesSampleRate: 1.0, // Set tracesSampleRate to 1.0 to capture 100% of transactions for tracing. Adjusting this value in production.

    // Configure Session Replay
    replaysSessionSampleRate: 0.1,
    replaysOnErrorSampleRate: 1,
    integrations: [
      Sentry.mobileReplayIntegration(),
      Sentry.feedbackIntegration(),
      navigationIntegration,
    ],
    enableNativeFramesTracking: !isRunningInExpoGo(), // Tracks slow and frozen frames in the application

    // uncomment the line below to enable Spotlight (https://spotlightjs.com)
    // spotlight: __DEV__,
  });
  Sentry.mobileReplayIntegration({
    maskAllText: false,
    maskAllImages: false,
    maskAllVectors: false,
  });
}

export const queryClient = new QueryClient({
  defaultOptions: {
    queries: {
      staleTime: 1000 * 60 * 5, // Data is considered fresh for 5 minutes
      gcTime: 1000 * 60 * 30, // Cache is kept for 30 minutes
    },
  },
});

const StyledGestureHandlerRootView = styled(GestureHandlerRootView)`
  flex: 1;
`;

// Log every call to router.replace
const originalReplace = router.replace;
router.replace = (...args) => {
  console.log('router.replace called with:', args);
  return originalReplace(...args);
};

function InitialLayout() {
  const { isAuthenticated, isAnonymous } = useAuth();
  return (
    <Stack>
      <Stack.Protected guard={isAuthenticated || isAnonymous}>
        <Stack.Screen name="(app)" options={{ headerShown: false }} />
      </Stack.Protected>
      <Stack.Protected guard={!isAuthenticated}>
        <Stack.Screen name="welcome" options={{ headerShown: false }} />
        <Stack.Screen name="login" options={{ headerShown: false }} />
        <Stack.Screen name="signup" options={{ headerShown: false }} />
      </Stack.Protected>
    </Stack>
  );
}

function RootLayout() {
  const [fontsLoaded] = useFonts({
    Nunito: require('../assets/fonts/NunitoQuid-Regular.ttf'),
    NunitoBold: require('../assets/fonts/NunitoQuid-Bold.ttf'),
  });

  // Capture the NavigationContainer ref and register it with the integration.
  const ref = useNavigationContainerRef();
  const pathname = usePathname();
  const [firebaseInitialised, setFirebaseInitialised] = React.useState(Platform.OS !== 'web');

  useEffect(() => {
    if (ref?.current) {
      navigationIntegration.registerNavigationContainer(ref);
    }
  }, [ref]);

  useEffect(() => {
    // web requires dynamic initialization on web prior to using firebase
    if (Platform.OS === 'web') {
      const firebaseConfig: FirebaseAppOptions = {
        appId: '1:184277938840:android:e65b424f5b569976efd6ed',
        projectId: 'jumpers-for-goalposts-c1282',
        clientId: '',
        apiKey: '',
        databaseURL: '',
        storageBucket: '',
        messagingSenderId: '',
        persistence: true,
      };

      initializeApp(firebaseConfig).then(() => {
        setFirebaseInitialised(true);
      });
    }

    // Make sure we don't have any cached data
    queryClient.clear();
    AsyncStorage.multiRemove([MANAGER_ID_KEY, TEAM_ID_KEY, GAMEWORLD_ID_KEY]);
  }, []);

  // Track the location in your analytics provider here.
  useEffect(() => {
    logger.log('pathname:', pathname);
    if (!firebaseInitialised) return;
    analytics().logScreenView({
      screen_name: pathname,
    });
  }, [firebaseInitialised, pathname]);

  // Don't render until fonts are loaded
  if (!fontsLoaded) {
    logger.log('Loading fonts...');
    return (
      <LoadingContainer>
        <ActivityIndicator size="large" />
      </LoadingContainer>
    );
  }

  return (
    <StrictMode>
      <StyledGestureHandlerRootView>
        <ThemeProvider>
          <QueryClientProvider client={queryClient}>
            <VersionProvider>
              <Authenticator.Provider>
                <AuthProvider>
                  <InitialLayout key="Root Slot" />
                </AuthProvider>
              </Authenticator.Provider>
            </VersionProvider>
          </QueryClientProvider>
        </ThemeProvider>
      </StyledGestureHandlerRootView>
    </StrictMode>
  );
}

export default !__DEV__ ? Sentry.wrap(RootLayout) : RootLayout;
