# Mail Feature Documentation

## Overview

The mail feature provides an always-visible mail icon in the app header that allows players to read their messages. The icon displays a badge when there are unread messages.

## Implementation

### Components

1. **MailIcon Component** (`src/components/MailIcon.tsx`)
   - Displays a mail icon with an optional badge showing unread message count
   - <PERSON><PERSON> navigation to the inbox screen
   - Automatically calculates unread messages based on stored read status

2. **InboxScreen** (`src/screens/InboxScreen.tsx`)
   - Displays a list of messages in a clean, card-based layout
   - Automatically marks messages as read when the screen is opened
   - Shows message title, category, date, and content
   - Handles empty states and loading states

3. **useInboxMessages Hook** (`src/hooks/useInboxMessages.ts`)
   - React Query hook for fetching messages from `/inbox/messages` endpoint
   - Provides caching and automatic refetching
   - Returns typed message data

### API Integration

The feature integrates with the `/inbox/messages` endpoint which returns:

```json
{
  "messages": [
    {
      "id": "uuid",
      "date": 1640995200000,
      "message": "<p>Congratulations, you won the auction for <PERSON>!</p>",
      "extra": {
        "category": "transfers",
        "title": "Fresh Meat"
      },
      "gameworldId": "gameworld-123",
      "teamId": "team-456"
    }
  ],
  "count": 1,
  "teamId": "team-456",
  "gameworldId": "gameworld-123"
}
```

### Local Storage

The feature uses AsyncStorage to track read messages:

- `@last_read_message_id` - Stores the ID of the most recently read message
- `@last_read_message_date` - Stores the timestamp of the most recently read message

### Navigation

- Added `/inbox` route (`app/(app)/inbox.tsx`)
- Mail icon is placed in the header next to the logout button
- Clicking the mail icon navigates to the inbox screen

### Styling

The feature follows the existing design patterns:
- Uses the app's theme system for colors and typography
- Consistent with other modal and list components
- Responsive design with proper spacing and shadows
- Badge styling matches the app's error color for visibility

### Features

1. **Unread Message Badge**: Shows count of unread messages (up to 99+)
2. **Message Categories**: Visual categorization with colored badges
3. **Date Formatting**: Smart date formatting (time for today, day for this week, date for older)
4. **HTML Content**: Strips HTML tags from message content for clean display
5. **Auto-mark as Read**: Messages are automatically marked as read when inbox is opened
6. **Empty States**: Proper handling of no messages and loading states
7. **Error Handling**: Graceful error handling for API failures

## Usage

1. The mail icon appears in the app header on all screens
2. A red badge shows the number of unread messages
3. Clicking the icon opens the inbox screen
4. Messages are displayed in reverse chronological order
5. Opening the inbox marks all messages as read
6. The badge disappears when there are no unread messages

## File Structure

```
src/
├── components/
│   └── MailIcon.tsx           # Mail icon with badge component
├── screens/
│   └── InboxScreen.tsx        # Inbox screen displaying messages
├── hooks/
│   └── useInboxMessages.ts    # Hook for fetching messages
└── context/
    └── ManagerContext.tsx     # Added storage keys for message tracking

app/(app)/
└── inbox.tsx                  # Route configuration for inbox screen

docs/
└── MAIL_FEATURE.md           # This documentation file
```

## Future Enhancements

Potential improvements that could be added:

1. **Message Actions**: Mark as read/unread, delete messages
2. **Message Filtering**: Filter by category or date
3. **Push Notifications**: Integration with the existing notification system
4. **Message Composition**: Allow sending messages to other players
5. **Message Search**: Search functionality for finding specific messages
6. **Pagination**: Handle large numbers of messages efficiently
7. **Message Details**: Expandable message view for longer content
