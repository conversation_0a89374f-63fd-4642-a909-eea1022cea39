# Training Screen Feature Documentation

## Overview

The Training Screen allows players to manage their team's training activities, including upgrading training facilities and assigning players to training slots to improve specific attributes.

## Features

### 🏟️ Training Ground Quality
- **Display**: Shows current training ground level (starts at Level 1)
- **Upgrade**: Button to upgrade training ground quality
- **Cost**: £100k per level upgrade
- **Confirmation**: Modal dialog before upgrading

### 🎯 Training Slots (5 Total)
- **Slot 1**: Unlocked by default
- **Slot 2**: Unlock cost £50k
- **Slot 3**: Unlock cost £100k  
- **Slot 4**: Unlock cost £200k
- **Slot 5**: Unlock cost £500k

### 🎨 Visual Design
- **Height**: Training slots are twice the height of regular player rows (120px)
- **Style**: Consistent with existing PlayerCard components
- **Icons**: Lock icons for locked slots, arrow icons for selection
- **Layout**: Similar to team screen player rows with enhanced spacing

### 👥 Player Assignment
- **Modal Interface**: Dropdown-style selection for players and attributes
- **Player Selection**: Choose from all available team players
- **Attribute Selection**: Choose from 13 trainable attributes:
  - **Goalkeeper**: Reflexes, Positioning, Shot Stopping
  - **Defender**: Tackling, Marking, Heading
  - **Midfielder**: Passing, Vision, Ball Control
  - **Attacker**: Finishing, Pace, Crossing
  - **General**: Stamina

### 🔒 Lock/Unlock System
- **Visual Feedback**: Lock icons and unlock costs displayed
- **Confirmation**: Modal dialog before unlocking slots
- **Cost Validation**: Checks team balance before allowing unlock
- **Progressive Costs**: Increasing costs for higher-tier slots

## File Structure

```
app/(app)/training.tsx              # Navigation route
src/screens/TrainingScreen.tsx      # Main screen component
src/components/TrainingSlot.tsx     # Individual slot component
src/components/TrainingModal.tsx    # Player/attribute selection modal
src/types/navigation.ts             # Updated with Training route
src/tests/TrainingScreen.test.tsx   # Test file
```

## Navigation Integration

The training screen is accessible from the home screen menu with:
- **Icon**: Fitness center icon (MaterialIcons)
- **Title**: "TRAINING"
- **Route**: `/training`

## State Management

### Training Ground State
```typescript
const [trainingGroundQuality] = useState(1); // Current level
const upgradeQualityCost = trainingGroundQuality * 100000; // £100k per level
```

### Training Slots State
```typescript
interface TrainingSlotData {
  isUnlocked: boolean;
  unlockCost?: number;
  assignedPlayer?: Player;
  trainingAttribute?: string;
}

const [trainingSlots, setTrainingSlots] = useState<TrainingSlotData[]>([
  { isUnlocked: true }, // Slot 1 - unlocked by default
  { isUnlocked: false, unlockCost: 50000 }, // Slot 2
  { isUnlocked: false, unlockCost: 100000 }, // Slot 3
  { isUnlocked: false, unlockCost: 200000 }, // Slot 4
  { isUnlocked: false, unlockCost: 500000 }, // Slot 5
]);
```

## User Interactions

### 1. Unlocking Slots
1. User taps on a locked slot
2. Confirmation modal appears with unlock cost
3. System checks team balance
4. If sufficient funds, slot is unlocked
5. Visual feedback updates immediately

### 2. Assigning Players
1. User taps on an unlocked slot
2. Training modal opens
3. User selects a player from dropdown
4. User selects an attribute to train
5. Assignment is confirmed and displayed

### 3. Upgrading Training Ground
1. User taps upgrade button
2. Confirmation modal shows upgrade cost
3. User confirms upgrade
4. Training ground level increases

## API Integration Points

The implementation is structured for easy backend integration:

### Unlock Slot
```typescript
// TODO: API call to unlock slot
POST /training/unlock-slot
{
  "slotIndex": number,
  "cost": number,
  "teamId": string
}
```

### Assign Player to Training
```typescript
// TODO: API call to assign player
POST /training/assign-player
{
  "slotIndex": number,
  "playerId": string,
  "attribute": string,
  "teamId": string
}
```

### Upgrade Training Ground
```typescript
// TODO: API call to upgrade facility
POST /training/upgrade-facility
{
  "newLevel": number,
  "cost": number,
  "teamId": string
}
```

## Testing

Basic test coverage includes:
- Component rendering
- Training ground display
- Training slots display
- Modal interactions
- State management

Run tests with:
```bash
npm test -- src/tests/TrainingScreen.test.tsx
```

## Future Enhancements

1. **Training Progress**: Visual progress bars for ongoing training
2. **Training Duration**: Time-based training completion
3. **Training Effectiveness**: Different results based on training ground quality
4. **Bulk Operations**: Assign multiple players at once
5. **Training History**: Track completed training sessions
6. **Specialized Training**: Position-specific training programs

## Styling Consistency

The training screen follows the existing app design patterns:
- Uses the same color scheme and typography
- Consistent with PlayerCard styling
- Proper theming support (light/dark mode)
- Responsive design for different screen sizes
- Accessibility considerations with proper touch targets

## Performance Considerations

- Efficient state management with minimal re-renders
- Lazy loading of player data
- Optimized modal rendering
- Proper cleanup of event listeners
- Memory-efficient component structure
