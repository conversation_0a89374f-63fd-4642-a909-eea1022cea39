import typescriptEslintPlugin from '@typescript-eslint/eslint-plugin';
import typescriptEslintParser from '@typescript-eslint/parser';
import pluginImport from 'eslint-plugin-import';
import eslintPluginJest from 'eslint-plugin-jest';
import eslintPluginReact from 'eslint-plugin-react';
import eslintPluginReactHooks from 'eslint-plugin-react-hooks';
import pluginUnusedImports from 'eslint-plugin-unused-imports';
import { getTsconfig } from 'get-tsconfig';
import path from 'path';
const tsconfig = getTsconfig();
if (tsconfig == null) {
  throw new Error('Unable to find tsconfig.json');
}

const config = [
  {
    ignores: ['old-web/**/*'],
  },
  {
    files: ['**/*.{js,jsx,ts,tsx}'],
    languageOptions: {
      ecmaVersion: 2022,
      sourceType: 'module',
    },
    plugins: {
      'unused-imports': pluginUnusedImports,
    },
    rules: {
      'no-console': 'off',
      'no-debugger': 'error',
      'no-shadow': 'off',
      'no-unused-vars': 'off',
      '@typescript-eslint/no-unused-vars': 'off',
      'unused-imports/no-unused-imports': 'error',
      'unused-imports/no-unused-vars': [
        'error',
        { vars: 'all', varsIgnorePattern: '^_', args: 'after-used', argsIgnorePattern: '^_' },
      ],
    },
  },
  {
    files: ['**/*.jsx', '**/*.tsx'],
    plugins: {
      react: eslintPluginReact,
      'react-hooks': eslintPluginReactHooks,
      import: pluginImport,
      'unused-imports': pluginUnusedImports,
    },
    rules: {
      'react/react-in-jsx-scope': 'off',
      'react-hooks/exhaustive-deps': 'error',
    },
  },
  {
    files: ['**/*.js', '**/*.jsx'],
    plugins: {
      import: pluginImport,
      'unused-imports': pluginUnusedImports,
    },
    rules: {
      'import/order': 'off',
      'import/prefer-default-export': 'off',
      'no-restricted-syntax': [
        'off',
        { selector: 'ForInStatement' },
        { selector: 'ForOfStatement' },
      ],
    },
  },
  {
    files: ['**/*.ts', '**/*.tsx'],
    languageOptions: {
      parser: typescriptEslintParser,
      parserOptions: {
        project: path.basename(tsconfig.path),
        tsconfigRootDir: path.dirname(tsconfig.path),
      },
    },
    plugins: {
      '@typescript-eslint': typescriptEslintPlugin,
      import: pluginImport,
      'unused-imports': pluginUnusedImports,
    },
    rules: {
      '@typescript-eslint/explicit-function-return-type': 'off',
      '@typescript-eslint/no-non-null-assertion': 'off',
      '@typescript-eslint/no-shadow': 'error',
    },
  },
  {
    files: ['**/*.test.ts', '**/*.spec.ts', '**/*.test.tsx', '**/*.spec.tsx'],
    plugins: {
      jest: eslintPluginJest,
      import: pluginImport,
    },
    rules: {
      '@typescript-eslint/no-explicit-any': 'off',
      '@typescript-eslint/no-unsafe-argument': 'off',
      '@typescript-eslint/no-unsafe-assignment': 'off',
      '@typescript-eslint/no-unsafe-member-access': 'off',
      '@typescript-eslint/no-unsafe-return': 'off',
      '@typescript-eslint/unbound-method': 'off',
      'import/no-extraneous-dependencies': [
        'error',
        {
          devDependencies: ['**/*.spec.ts', '**/*.test.ts', '**/*.spec.tsx', '**/*.test.tsx'],
        },
      ],
      'jest/expect-expect': ['error', { assertFunctionNames: ['expect', 'expect*'] }],
      'jest/unbound-method': 'error',
    },
  },
];

export default config;
