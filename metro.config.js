const { getSentryExpoConfig } = require('@sentry/react-native/metro');

// Get the base Sentry config
const config = getSentryExpoConfig(__dirname, {
  annotateReactComponents: true,
});

// Merge SVG transformer configuration
config.transformer = {
  ...config.transformer,
  babelTransformerPath: require.resolve('react-native-svg-transformer'),
};

config.resolver = {
  ...config.resolver,
  assetExts: config.resolver.assetExts.filter((ext) => ext !== 'svg'),
  sourceExts: [...config.resolver.sourceExts, 'svg'],
};

module.exports = config;
