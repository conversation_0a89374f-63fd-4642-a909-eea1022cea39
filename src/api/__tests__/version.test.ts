import { checkAppVersion } from '../version';
import { callApi } from '../client';

// Mock the API client
jest.mock('../client');
const mockCallApi = callApi as jest.MockedFunction<typeof callApi>;

describe('Version API', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  it('should call the correct endpoint with version data', async () => {
    const mockResponse = {
      latest: 2,
      versionString: '1.1.0',
      forceUpdate: false,
      status: 200,
    };

    mockCallApi.mockResolvedValue(mockResponse);

    const result = await checkAppVersion(1, 'android');

    expect(mockCallApi).toHaveBeenCalledWith('/appversion', {
      method: 'POST',
      body: JSON.stringify({
        currentVersion: 1,
        platform: 'android',
      }),
    });

    expect(result).toEqual({
      latest: 2,
      versionString: '1.1.0',
      forceUpdate: false,
      status: 200,
    });
  });

  it('should handle force update response', async () => {
    const mockResponse = {
      latest: 3,
      versionString: '2.0.0',
      forceUpdate: true,
      status: 200,
    };

    mockCallApi.mockResolvedValue(mockResponse);

    const result = await checkAppVersion(1, 'android');

    expect(result.forceUpdate).toBe(true);
    expect(result.latest).toBe(3);
    expect(result.versionString).toBe('2.0.0');
  });

  it('should throw error on non-200 status', async () => {
    const mockResponse = {
      status: 500,
      error: 'Server error',
    };

    mockCallApi.mockResolvedValue(mockResponse);

    await expect(checkAppVersion(1, 'android')).rejects.toThrow(
      'Failed to check app version'
    );
  });

  it('should work with iOS platform', async () => {
    const mockResponse = {
      latest: 2,
      versionString: '1.1.0',
      forceUpdate: false,
      status: 200,
    };

    mockCallApi.mockResolvedValue(mockResponse);

    await checkAppVersion(1, 'ios');

    expect(mockCallApi).toHaveBeenCalledWith('/appversion', {
      method: 'POST',
      body: JSON.stringify({
        currentVersion: 1,
        platform: 'ios',
      }),
    });
  });
});
