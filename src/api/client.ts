import AsyncStorage from '@react-native-async-storage/async-storage';
import { fetchAuthSession, signOut } from 'aws-amplify/auth';
import axios, { AxiosRequestConfig } from 'axios';
import { ANONYMOUS_USER_KEY, LAST_LOGIN_TYPE } from '../context/AuthContext';
import { logger } from '../utils/logger';

async function buildAuthHeaders(forceRefresh = false) {
  const headers: { Authorization?: string } = {};
  if (process.env.EXPO_PUBLIC_IS_LOCAL === 'true') return headers;

  try {
    const lastLoginType = await AsyncStorage.getItem(LAST_LOGIN_TYPE);
    if (lastLoginType === 'anonymous') {
      let anonymousId = await AsyncStorage.getItem(ANONYMOUS_USER_KEY);
      if (!anonymousId) {
        // First-time: try to read from Cognito identity, else generate a fallback
        try {
          const session = await fetchAuthSession(forceRefresh ? { forceRefresh: true } : undefined as any);
          anonymousId = (session as any)?.identityId ?? undefined;
        } catch (e) {
          // ignore and fallback to generated id
        }
        if (!anonymousId) {
          // Simple fallback ID (avoids new dependencies); not cryptographically strong
          anonymousId = `anon_${Date.now()}_${Math.random().toString(36).slice(2, 10)}`;
          logger.warn('Generated fallback anonymous ID');
        }
        await AsyncStorage.setItem(ANONYMOUS_USER_KEY, anonymousId);
      } else if (forceRefresh) {
        // Refresh credentials but do NOT overwrite stored anonymous ID
        try {
          const session = await fetchAuthSession({ forceRefresh: true } as any);
          const newId = (session as any)?.identityId;
          if (newId && newId !== anonymousId) {
            logger.warn('Anonymous identityId differs from stored ID; keeping stored value');
          }
        } catch (e) {
          // ignore session refresh errors here; higher-level logic will handle
        }
      }
      headers['Authorization'] = `Anonymous ${anonymousId}`;
      logger.log(forceRefresh ? 'Using stored guest identity after refresh' : 'Using guest user identity ID:', anonymousId);
    } else if (lastLoginType === 'authenticated') {
      const session = await fetchAuthSession(forceRefresh ? { forceRefresh: true } : undefined as any);
      const token = session.tokens?.accessToken?.toString();
      if (token) {
        headers['Authorization'] = `Bearer ${token}`;
        logger.log(forceRefresh ? 'Using refreshed authenticated token' : 'Using authenticated user token');
      }
    } else {
      logger.warn('No valid auth type found in buildAuthHeaders');
    }
  } catch (authError) {
    logger.error('Failed to build auth headers:', authError);
    throw authError;
  }

  return headers;
}

export async function callApi<T>(
  endpoint: string,
  options: RequestInit = {},
  skipAuth = false
): Promise<T extends any[] ? { data: T; status: number } : T & { status: number }> {
  try {
    const authHeaders = !skipAuth ? await buildAuthHeaders(false) : {};

    // Convert fetch options to axios config
    const axiosConfig: AxiosRequestConfig = {
      method: options.method || 'GET',
      headers: {
        ...options.headers,
        'Content-Type': 'application/json',
        ...authHeaders,
      } as any,
    };

    // Handle request body
    if ((options.method === 'POST' || options.method === 'PUT') && options.body) {
      if (typeof options.body !== 'string') {
        logger.log('Converting body to JSON:', options.body);
        axiosConfig.data = options.body;
      } else {
        axiosConfig.data = JSON.parse(options.body);
      }
    }

    const doRequest = async (cfg: AxiosRequestConfig) =>
      axios(`${process.env.EXPO_PUBLIC_API_URL}${endpoint}`, cfg);

    let response = await doRequest(axiosConfig);

    // Axios automatically throws for non-2xx responses, so we don't need to check response.ok
    if (Array.isArray(response.data)) {
      return { data: response.data, status: response.status } as T extends any[]
        ? { data: T; status: number }
        : never;
    }
    return { ...response.data, status: response.status } as T extends any[]
      ? never
      : T & { status: number };
  } catch (error: any) {
    const status = error?.response?.status;
    if ((status === 401 || status === 403) && !skipAuth && process.env.EXPO_PUBLIC_IS_LOCAL !== 'true') {
      try {
        const lastLoginType = await AsyncStorage.getItem(LAST_LOGIN_TYPE);
        if (lastLoginType === 'authenticated' || lastLoginType === 'anonymous') {
          logger.warn(`Received ${status}. Forcing token refresh and retrying...`);
          const refreshedHeaders = await buildAuthHeaders(true);

          const retryConfig: AxiosRequestConfig = {
            method: options.method || 'GET',
            headers: {
              ...options.headers,
              'Content-Type': 'application/json',
              ...refreshedHeaders,
            } as any,
          };
          if ((options.method === 'POST' || options.method === 'PUT') && options.body) {
            retryConfig.data = typeof options.body === 'string' ? JSON.parse(options.body) : options.body;
          }

          const retryResponse = await axios(`${process.env.EXPO_PUBLIC_API_URL}${endpoint}`, retryConfig);
          if (Array.isArray(retryResponse.data)) {
            return { data: retryResponse.data, status: retryResponse.status } as T extends any[]
              ? { data: T; status: number }
              : never;
          }
          return { ...retryResponse.data, status: retryResponse.status } as T extends any[]
            ? never
            : T & { status: number };
        }
      } catch (retryError) {
        logger.error('Retry after token refresh failed:', retryError);
        // If refresh token is invalid/expired, sign the user out to reset state
        const name = (retryError as any)?.name || (retryError as any)?.code;
        const message = (retryError as any)?.message || '';
        if (
          name === 'NotAuthorizedException' ||
          name === 'InvalidParameterException' ||
          /invalid\s*refresh\s*token/i.test(String(message)) ||
          /refresh token has expired/i.test(String(message))
        ) {
          try {
            logger.warn('Refresh token invalid/expired. Signing out...');
            await signOut();
          } catch (signOutErr) {
            logger.error('Sign out after refresh failure also failed:', signOutErr);
          }
        }
        throw retryError;
      }
    }

    logger.error('API call failed:', error);
    throw error;
  }
}
