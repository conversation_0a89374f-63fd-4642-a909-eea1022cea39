import { callApi } from './client';

export interface FeedbackRequest {
  type: 'bug' | 'feature' | 'feedback';
  title: string;
  description: string;
  metadata?: Record<string, any>;
}

export interface FeedbackResponse {
  message: string;
  notionPageId: string;
}

export interface FeedbackError {
  error: string;
  details: string;
}

export const submitFeedback = async (feedback: FeedbackRequest): Promise<FeedbackResponse> => {
  const response = await callApi<FeedbackResponse>('/feedback', {
    method: 'POST',
    body: JSON.stringify(feedback),
  });
  if (response.status !== 200) {
    throw new Error(`Failed to submit feedback: ${response}`);
  }
  return response;
};
