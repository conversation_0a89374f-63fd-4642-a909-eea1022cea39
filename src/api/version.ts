import { callApi } from './client';

export interface VersionCheckRequest {
  currentVersion: number;
  platform: 'android' | 'ios';
}

export interface VersionCheckResponse {
  latest: number;
  versionString: string;
  forceUpdate: boolean;
}

export const checkAppVersion = async (
  currentVersion: number,
  platform: 'android' | 'ios'
): Promise<VersionCheckResponse> => {
  const response = await callApi<VersionCheckResponse>('/appversion', {
    method: 'POST',
    body: JSON.stringify({
      currentVersion,
      platform,
    }),
  });
  
  if (response.status !== 200) {
    throw new Error(`Failed to check app version: ${response}`);
  }
  
  return response;
};
