import { ResourcesConfig } from '@aws-amplify/core';

const awsExports: ResourcesConfig = {
  Auth: {
    Cognito: {
      userPoolId: 'us-east-2_DHNnMYsNY', // Cognito User Pool ID
      userPoolClientId: '5pm5gsq31ki0bmpnflara9f8rq', // Cognito App Client ID
      identityPoolId: 'us-east-2:1fa5af50-1ffd-45a1-9ce4-621a7eecb2e9',
      allowGuestAccess: true,
      loginWith: {
        oauth: {
          domain: 'stage-auth-domain.auth.us-east-2.amazoncognito.com',
          scopes: ['openid', 'email', 'profile'],
          redirectSignIn: [
            'jfg://login',
            'http://localhost:8081/',
            'https://master.d1673favy6fst1.amplifyapp.com/',
          ],
          redirectSignOut: [
            'jfg://welcome',
            'http://localhost:8081/',
            'https://master.d1673favy6fst1.amplifyapp.com/',
          ],
          responseType: 'code',
        },
      },
    },
  },
  API: {
    REST: {
      jfg: {
        endpoint: 'https://jfg-stage-v2.rwscripts.com',
        region: 'us-east-2',
      },
    },
  },
};

export default awsExports;
