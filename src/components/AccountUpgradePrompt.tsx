import { router } from 'expo-router';
import React, { useState } from 'react';
import { Modal } from 'react-native';
import styled, { DefaultTheme } from 'styled-components/native';
import { Text } from './Text';
import { useAuth } from '../context/AuthContext';
import { useTheme } from '../theme/ThemeContext';

interface StyledProps {
  theme: DefaultTheme;
}

const ModalOverlay = styled.View`
  flex: 1;
  background-color: rgba(0, 0, 0, 0.5);
  justify-content: center;
  align-items: center;
  padding: 20px;
`;

const ModalContent = styled.View<StyledProps>`
  background-color: ${({ theme }) => theme.colors.background};
  border-radius: 12px;
  padding: 24px;
  width: 100%;
  max-width: 400px;
  shadow-color: #000;
  shadow-offset: 0px 4px;
  shadow-opacity: 0.25;
  shadow-radius: 8px;
  elevation: 8;
`;

const IconContainer = styled.View<StyledProps>`
  width: 60px;
  height: 60px;
  border-radius: 30px;
  background-color: ${({ theme }) => theme.colors.primary}20;
  justify-content: center;
  align-items: center;
  align-self: center;
  margin-bottom: 16px;
`;

const IconText = styled(Text)<StyledProps>`
  font-size: 24px;
  color: ${({ theme }) => theme.colors.primary};
`;

const Title = styled(Text)<StyledProps>`
  font-size: 20px;
  font-weight: bold;
  color: ${({ theme }) => theme.colors.text.primary};
  text-align: center;
  margin-bottom: 12px;
  font-family: ${({ theme }) => theme.typography.bold};
`;

const Description = styled(Text)<StyledProps>`
  font-size: 16px;
  color: ${({ theme }) => theme.colors.text.secondary};
  text-align: center;
  line-height: 24px;
  margin-bottom: 24px;
`;

const ButtonContainer = styled.View`
  gap: 12px;
`;

const PrimaryButton = styled.TouchableOpacity<StyledProps>`
  background-color: ${({ theme }) => theme.colors.primary};
  padding: 16px 24px;
  border-radius: 8px;
  align-items: center;
`;

const SecondaryButton = styled.TouchableOpacity<StyledProps>`
  background-color: transparent;
  border: 1px solid ${({ theme }) => theme.colors.border};
  padding: 16px 24px;
  border-radius: 8px;
  align-items: center;
`;

const PrimaryButtonText = styled(Text)<StyledProps>`
  color: ${({ theme }) => theme.colors.background};
  font-size: 16px;
  font-weight: bold;
  font-family: ${({ theme }) => theme.typography.bold};
`;

const SecondaryButtonText = styled(Text)<StyledProps>`
  color: ${({ theme }) => theme.colors.text.primary};
  font-size: 16px;
  font-family: ${({ theme }) => theme.typography.regular};
`;

interface AccountUpgradePromptProps {
  visible: boolean;
  onClose: () => void;
  title?: string;
  description?: string;
  trigger?: 'manual' | 'achievement' | 'data_warning' | 'feature_unlock';
}

const AccountUpgradePrompt: React.FC<AccountUpgradePromptProps> = ({
  visible,
  onClose,
  title,
  description,
  trigger = 'manual',
}) => {
  const { theme } = useTheme();
  const { isAnonymous } = useAuth();
  const [isNavigating, setIsNavigating] = useState(false);

  // Don't show if user is not anonymous
  if (!isAnonymous) {
    return null;
  }

  const getDefaultContent = () => {
    switch (trigger) {
      case 'achievement':
        return {
          title: 'Great Achievement!',
          description: 'Create an account to save your progress and unlock more features. Your data will be preserved!',
        };
      case 'data_warning':
        return {
          title: 'Don\'t Lose Your Progress!',
          description: 'Your game data is only stored locally. Create an account to save your team and progress across devices.',
        };
      case 'feature_unlock':
        return {
          title: 'Unlock More Features',
          description: 'Create an account to access premium features, cloud saves, and compete with friends!',
        };
      default:
        return {
          title: 'Save Your Progress',
          description: 'Create an account to keep your team safe and access your game from any device.',
        };
    }
  };

  const content = {
    title: title || getDefaultContent().title,
    description: description || getDefaultContent().description,
  };

  const handleCreateAccount = async () => {
    if (isNavigating) return;
    
    setIsNavigating(true);
    onClose();
    
    // Navigate to login/signup screen
    router.push('/login');
    
    setTimeout(() => setIsNavigating(false), 1000);
  };

  const handleMaybeLater = () => {
    onClose();
  };

  return (
    <Modal
      visible={visible}
      transparent
      animationType="fade"
      onRequestClose={onClose}
    >
      <ModalOverlay>
        <ModalContent theme={theme}>
          <IconContainer theme={theme}>
            <IconText theme={theme}>🔒</IconText>
          </IconContainer>
          
          <Title theme={theme}>{content.title}</Title>
          <Description theme={theme}>{content.description}</Description>
          
          <ButtonContainer>
            <PrimaryButton theme={theme} onPress={handleCreateAccount} disabled={isNavigating}>
              <PrimaryButtonText theme={theme}>Create Account</PrimaryButtonText>
            </PrimaryButton>
            
            <SecondaryButton theme={theme} onPress={handleMaybeLater}>
              <SecondaryButtonText theme={theme}>Maybe Later</SecondaryButtonText>
            </SecondaryButton>
          </ButtonContainer>
        </ModalContent>
      </ModalOverlay>
    </Modal>
  );
};

export default AccountUpgradePrompt;
