import styled from 'styled-components/native';
import { StyledProps } from './Common';

export const ActionButton = styled.TouchableOpacity<{
  variant?: 'primary' | 'secondary' | 'warn' | 'danger';
}>`
  flex: 1;
  min-height: 50px;
  padding: 15px;
  margin: 0 0 10px 0;
  border-radius: 8px;
  align-items: center;
  justify-content: center;
  background-color: ${(props) => {
    switch (props.variant) {
      case 'primary':
        return props.theme.colors.button.primary;
      case 'secondary':
        return props.theme.colors.button.secondary;
      case 'danger':
        return props.theme.colors.button.error;
      case 'warn':
        return props.theme.colors.button.warning;
      default:
        return props.theme.colors.button.primary;
    }
  }};
  border-width: 1px;
  border-color: ${(props) => {
    switch (props.variant) {
      case 'primary':
        return props.theme.colors.buttonBorder.primary;
      case 'secondary':
        return props.theme.colors.buttonBorder.secondary;
      case 'danger':
        return props.theme.colors.buttonBorder.error;
      case 'warn':
        return props.theme.colors.buttonBorder.warning;
      default:
        return props.theme.colors.buttonBorder.primary;
    }
  }};
  opacity: ${(props) => (props.disabled ? 0.6 : 1)};
`;
export const ActionButtonText = styled.Text<{
  variant?: 'primary' | 'secondary' | 'warn' | 'danger';
}>`
  color: ${(props) => {
    switch (props.variant) {
      case 'primary':
        return props.theme.colors.text.primary;
      case 'secondary':
        return props.theme.colors.text.secondary;
      case 'danger':
        return props.theme.colors.text.primary;
      case 'warn':
        return props.theme.colors.text.secondary;
      default:
        return props.theme.colors.text.primary;
    }
  }};
  font-family: ${(props: StyledProps) => props.theme.typography.bold};
  font-size: 14px;
  text-align: center;
`;
