import React from 'react';
import { TouchableOpacity } from 'react-native';
import styled, { DefaultTheme } from 'styled-components/native';
import { ActiveTransfer } from '../hooks/useMyBidsPlayers';
import { formatCurrencyShort } from '../utils/utils';
import { Text } from './Text';

interface StyledProps {
  theme: DefaultTheme;
}

interface ActiveTransferCardProps {
  transfer: ActiveTransfer;
  onSelect?: (transfer: ActiveTransfer) => void;
}

const Card = styled(TouchableOpacity)<StyledProps>`
  background-color: ${(props) => props.theme.colors.surface};
  border-radius: 8px;
  padding: 12px;
  margin-bottom: 8px;
  border-left-width: 4px;
  border-left-color: ${(props) => props.theme.colors.primary};
`;

const CardHeader = styled.View`
  flex-direction: row;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 8px;
`;

const PlayerInfo = styled.View`
  flex: 1;
`;

const PlayerName = styled(Text)<StyledProps>`
  font-family: ${(props: StyledProps) => props.theme.typography.bold};
  font-size: 16px;
  color: ${(props) => props.theme.colors.text.primary};
  margin-bottom: 2px;
`;

const PlayerDetails = styled(Text)<StyledProps>`
  font-size: 12px;
  color: ${(props) => props.theme.colors.text.secondary};
  margin-bottom: 1px;
`;

const TransferInfo = styled.View`
  align-items: flex-end;
`;

const OfferAmount = styled(Text)<StyledProps>`
  font-family: ${(props: StyledProps) => props.theme.typography.bold};
  font-size: 16px;
  color: ${(props) => props.theme.colors.primary};
  margin-bottom: 2px;
`;

const TransferStatus = styled.View`
  flex-direction: row;
  justify-content: space-between;
  align-items: center;
  margin-top: 8px;
  padding-top: 8px;
  border-top-width: 1px;
  border-top-color: ${(props: StyledProps) =>
    props.theme.colors.border || props.theme.colors.text.secondary + '20'};
`;

const SellerInfo = styled(Text)<StyledProps>`
  font-size: 12px;
  color: ${(props) => props.theme.colors.text.secondary};
  flex: 1;
`;

const CounterOfferInfo = styled.View`
  align-items: flex-end;
`;

const CounterOfferLabel = styled(Text)<StyledProps>`
  font-size: 10px;
  color: ${(props) => props.theme.colors.text.secondary};
  text-transform: uppercase;
`;

const CounterOfferAmount = styled(Text)<StyledProps>`
  font-family: ${(props: StyledProps) => props.theme.typography.bold};
  font-size: 12px;
  color: ${(props) => props.theme.colors.warning || '#FFA500'};
`;

const formatDate = (timestamp: number): string => {
  const date = new Date(timestamp);
  return date.toLocaleDateString('en-GB', {
    day: '2-digit',
    month: '2-digit',
    year: '2-digit',
  });
};

const ActiveTransferCard: React.FC<ActiveTransferCardProps> = ({ transfer, onSelect }) => {
  const handlePress = () => {
    if (onSelect) {
      onSelect(transfer);
    }
  };

  // Only show counter offer if it exists and is newer than the original offer
  const hasCounterOffer =
    transfer.counterOfferValue !== '0' &&
    transfer.counterOfferTime !== '0' &&
    Number(transfer.counterOfferTime) > transfer.date;

  return (
    <Card onPress={handlePress}>
      <CardHeader>
        <PlayerInfo>
          <PlayerName>{`${transfer.player.firstName} ${transfer.player.surname}`}</PlayerName>
          <PlayerDetails>Age: {transfer.player.age}</PlayerDetails>
          <PlayerDetails>Value: {formatCurrencyShort(transfer.player.value)}</PlayerDetails>
          <PlayerDetails>Offered: {formatDate(transfer.date)}</PlayerDetails>
        </PlayerInfo>
        <TransferInfo>
          <OfferAmount>{formatCurrencyShort(transfer.value)}</OfferAmount>
        </TransferInfo>
      </CardHeader>

      <TransferStatus>
        <SellerInfo>From: {transfer.seller.teamName}</SellerInfo>
        {hasCounterOffer && (
          <CounterOfferInfo>
            <CounterOfferLabel>Counter Offer</CounterOfferLabel>
            <CounterOfferAmount>
              {formatCurrencyShort(Number(transfer.counterOfferValue))}
            </CounterOfferAmount>
          </CounterOfferInfo>
        )}
      </TransferStatus>
    </Card>
  );
};

export default ActiveTransferCard;
