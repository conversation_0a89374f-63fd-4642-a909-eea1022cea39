import React, { createContext, ReactNode, useContext, useEffect, useRef, useState } from 'react';
import { RewardedAd, RewardedAdEventType, TestIds } from 'react-native-google-mobile-ads';
import { useManager } from '../../context/ManagerContext';
import { initAds } from './initAds';

import analytics from '@react-native-firebase/analytics';

// Ad type constants
export const AD_TYPES = {
  MAGIC_SPONGE: 'MAGIC_SPONGE',
  RED_CARD_APPEAL: 'RED_CARD_APPEAL',
  TEAM_NAME_CHANGE: 'TEAM_NAME_CHANGE',
} as const;

export type AdType = (typeof AD_TYPES)[keyof typeof AD_TYPES];

// Ad configuration mapping
const AD_CONFIG: Record<AdType, string> = {
  [AD_TYPES.MAGIC_SPONGE]: __DEV__ ? TestIds.REWARDED : 'ca-app-pub-3959534729713487/5735956068',
  [AD_TYPES.RED_CARD_APPEAL]: __DEV__ ? TestIds.REWARDED : 'ca-app-pub-3959534729713487/9895508567',
  [AD_TYPES.TEAM_NAME_CHANGE]: __DEV__
    ? TestIds.REWARDED
    : 'ca-app-pub-3959534729713487/8307928870', // Using same as magic sponge for now
};

interface AdState {
  ad: RewardedAd | null;
  isLoaded: boolean;
}

interface RewardAdContextProps {
  isAdLoaded: (adType: AdType) => boolean;
  showAd: (adType: AdType, onRewardEarned?: () => void) => Promise<void>;
}

const RewardAdContext = createContext<RewardAdContextProps | undefined>(undefined);

export const useRewardAd = () => {
  const context = useContext(RewardAdContext);
  if (!context) {
    throw new Error('useRewardAd must be used within a RewardAdProvider');
  }
  return context;
};

export const RewardAdProvider = ({ children }: { children: ReactNode }) => {
  const { manager } = useManager();

  // Store callbacks for each ad type
  const rewardCallbacks = useRef<Record<string, (() => void) | undefined>>({});

  const [adStates, setAdStates] = useState<Record<AdType, AdState>>(() => {
    const initialState: Record<AdType, AdState> = {} as Record<AdType, AdState>;
    Object.keys(AD_TYPES).forEach((key) => {
      const adType = AD_TYPES[key as keyof typeof AD_TYPES];
      initialState[adType] = { ad: null, isLoaded: false };
    });
    return initialState;
  });

  useEffect(() => {
    initAds();
  }, []);

  useEffect(() => {
    if (!manager?.managerId) return;

    const unsubscribeFunctions: (() => void)[] = [];

    // Initialize all ad types
    Object.entries(AD_CONFIG).forEach(([adType, adUnitId]) => {
      const ad = RewardedAd.createForAdRequest(adUnitId, {
        keywords: ['football', 'soccer', 'manager', 'sports', 'game'],
        serverSideVerificationOptions: {
          userId: manager.managerId,
        },
      });

      const unsubscribeLoaded = ad.addAdEventListener(RewardedAdEventType.LOADED, () => {
        setAdStates((prev) => ({
          ...prev,
          [adType]: { ...prev[adType as AdType], isLoaded: true },
        }));
      });

      const unsubscribeEarned = ad.addAdEventListener(
        RewardedAdEventType.EARNED_REWARD,
        (payload) => {
          console.log(`User earned reward for ${adType}:`, payload);

          // Invoke the callback if it exists
          const callback = rewardCallbacks.current[adType];
          if (callback) {
            callback();
            // Clear the callback after use
            rewardCallbacks.current[adType] = undefined;
          }

          setAdStates((prev) => ({
            ...prev,
            [adType]: { ...prev[adType as AdType], isLoaded: false },
          }));
          // Reload the ad for next use
          ad.load();
        }
      );

      // Update the ad instance in state
      setAdStates((prev) => ({
        ...prev,
        [adType]: { ...prev[adType as AdType], ad },
      }));

      // Load the ad
      ad.load();

      unsubscribeFunctions.push(unsubscribeLoaded, unsubscribeEarned);
    });

    return () => {
      unsubscribeFunctions.forEach((unsubscribe) => unsubscribe());
    };
  }, [manager?.managerId]);

  const isAdLoaded = (adType: AdType): boolean => {
    return adStates[adType]?.isLoaded || false;
  };

  const showAd = async (adType: AdType, onRewardEarned?: () => void): Promise<void> => {
    const adState = adStates[adType];
    if (adState?.isLoaded && adState.ad) {
      // Store the callback to be invoked on reward
      rewardCallbacks.current[adType] = onRewardEarned;

      await adState.ad.show();
      await analytics().logEvent('ad_shown', { ad_type: adType });
    }
  };

  return (
    <RewardAdContext.Provider
      value={{
        isAdLoaded,
        showAd,
      }}
    >
      {children}
    </RewardAdContext.Provider>
  );
};
