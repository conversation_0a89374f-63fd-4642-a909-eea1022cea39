import React, { createContext, ReactNode, useContext } from 'react';

// Ad type constants (matching native version)
export const AD_TYPES = {
  MAGIC_SPONGE: 'MAGIC_SPONGE',
  RED_CARD_APPEAL: 'RED_CARD_APPEAL',
  TEAM_NAME_CHANGE: 'TEAM_NAME_CHANGE',
} as const;

export type AdType = (typeof AD_TYPES)[keyof typeof AD_TYPES];

interface RewardAdContextProps {
  isAdLoaded: (adType: AdType) => boolean;
  showAd: (adType: AdType) => Promise<void>;
}

const RewardAdContext = createContext<RewardAdContextProps | undefined>(undefined);

export const useRewardAd = () => {
  const context = useContext(RewardAdContext);
  if (!context) {
    throw new Error('useRewardAd must be used within a RewardAdProvider');
  }
  return context;
};

export const RewardAdProvider = ({ children }: { children: ReactNode }) => (
  <RewardAdContext.Provider
    value={{
      isAdLoaded: () => false,
      showAd: () => {
        return Promise.resolve();
      },
    }}
  >
    {children}
  </RewardAdContext.Provider>
);
