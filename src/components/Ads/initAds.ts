import { MobileAds } from 'react-native-google-mobile-ads';
import { logger } from '../../utils/logger';

let isInitialized = false;

export function initAds() {
  if (isInitialized) return;
  isInitialized = true;

  // Initialize the Mobile Ads SDK
  MobileAds()
    .initialize()
    .then(() => {
      logger.log('Mobile Ads SDK initialized successfully');
    })
    .catch((error: any) => {
      logger.error('Mobile Ads SDK initialization failed:', error);
    });
}