import styled from 'styled-components/native';
import { StyledProps } from './Common';
import { Text } from './Text';

export const BalanceRow = styled.View`
  flex-direction: row;
  align-items: center;
  justify-content: center;
  margin-bottom: 24px;
`;

export const BalanceIcon = styled.Image`
  width: 28px;
  height: 28px;
  margin-right: 8px;
  color: ${(props: StyledProps) => props.theme.colors.text.primary};
`;

export const ScoutIcon = styled.Image`
  width: 28px;
  height: 28px;
  margin-right: 8px;
  color: ${(props: StyledProps) => props.theme.colors.text.primary};
`;

export const BalanceText = styled(Text)`
  font-size: 25px;
  color: ${(props: StyledProps) => props.theme.colors.text.primary};
  font-family: ${(props: StyledProps) => props.theme.typography.bold};
`;
