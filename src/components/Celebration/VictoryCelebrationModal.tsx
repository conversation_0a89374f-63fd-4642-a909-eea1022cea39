import React, { useEffect, useMemo, useState } from 'react';
import { Modal, Platform } from 'react-native';
import styled from 'styled-components/native';
import { useManager } from '../../context/ManagerContext';
import { useCachedFixtures } from '../../hooks/useCachedData';
import { useVictoryCelebration } from '../../hooks/useVictoryCelebration';
import CrossPlatformLottie from '../CrossPlatformLottie';
import { Text } from '../Text';

const Overlay = styled.View`
  flex: 1;
  background-color: rgba(0, 0, 0, 0.7);
  justify-content: center;
  align-items: center;
`;

const Panel = styled.View`
  width: 90%;
  max-width: 500px;
  max-height: 80%;
  background-color: ${(p) => p.theme.colors.surface};
  border-radius: 20px;
  padding: 28px 24px 32px 24px;
  overflow: hidden;
`;

const Heading = styled(Text)`
  font-size: 26px;
  text-align: center;
  margin-bottom: 12px;
`;

const ScoreBlock = styled.View`
  margin-top: 12px;
  margin-bottom: 20px;
`;

const TeamLine = styled.View`
  flex-direction: row;
  justify-content: space-between;
  align-items: flex-end;
  margin-top: 10px;
`;

const TeamName = styled(Text)<{ highlight?: boolean }>`
  font-size: 18px;
  flex: 1;
  color: ${(p) => (p.highlight ? p.theme.colors.primary : p.theme.colors.text.primary)};
`;

const TeamScore = styled(Text)`
  font-size: 24px;
  margin-left: 16px;
`;

const ScorerLine = styled(Text)`
  font-size: 14px;
  margin-left: 4px;
  margin-top: 2px;
  color: ${(p) => p.theme.colors.text.secondary};
`;

const ButtonRow = styled.View`
  margin-top: 8px;
  flex-direction: row;
  justify-content: center;
`;

const ContinueButton = styled.TouchableOpacity`
  background-color: ${(p) => p.theme.colors.primary};
  border-radius: 30px;
  padding: 14px 42px;
  elevation: 3;
`;

const ContinueText = styled(Text)`
  font-size: 18px;
`;

const ConfettiContainer = styled.View`
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  pointer-events: none;
`;

function formatMinute(min: number) {
  return `${min}'`;
}

export default function VictoryCelebrationModal() {
  const { openFixtureId, closeCelebration } = useVictoryCelebration();
  const { team, manager } = useManager();
  const { data: fixtures } = useCachedFixtures(manager?.gameworldId, team?.league.id, team?.teamId);

  const [visible, setVisible] = useState(false);

  const fixture = useMemo(
    () => fixtures?.find((f) => f.fixtureId === openFixtureId),
    [fixtures, openFixtureId]
  );

  // Debug logging
  useEffect(() => {
    if (__DEV__) {
      console.log('VictoryModal state', {
        openFixtureId,
        fixturesCount: fixtures?.length,
        hasFixture: !!fixture,
      });
    }
  }, [openFixtureId, fixtures, fixture]);

  useEffect(() => {
    if (openFixtureId && fixture) {
      // delay a tick to ensure RN root has mounted
      const t = setTimeout(() => setVisible(true), 0);
      return () => clearTimeout(t);
    }
    setVisible(false);
  }, [openFixtureId, fixture]);

  if (!openFixtureId || !fixture) return null;

  const [homeScore, awayScore] = fixture.score || [0, 0];
  const isHomeTeam = team?.teamId === fixture.homeTeamId;
  const isAwayTeam = team?.teamId === fixture.awayTeamId;

  // Build scorer arrays per team as flat list of entries (playerName, minute)
  const homeScorers: { name: string; minute: number }[] = [];
  const awayScorers: { name: string; minute: number }[] = [];
  (fixture.scorers || []).forEach((s) => {
    (s.goalTime || []).forEach((gt) => {
      const entry = { name: s.playerName, minute: gt.minute };
      if (s.team === 0) homeScorers.push(entry);
      else if (s.team === 1) awayScorers.push(entry);
    });
  });
  // Sort by minute ascending
  homeScorers.sort((a, b) => a.minute - b.minute);
  awayScorers.sort((a, b) => a.minute - b.minute);

  return (
    <Modal
      key={openFixtureId}
      visible={visible}
      transparent
      animationType={Platform.OS === 'android' ? 'fade' : 'slide'}
      onRequestClose={closeCelebration}
    >
      <Overlay style={{ zIndex: 9999, elevation: 9999 }}>
        <Panel>
          <ConfettiContainer>
            <CrossPlatformLottie
              source={require('../../../assets/animations/confetti.json')}
              autoPlay
              loop={false}
              style={{ width: '100%', height: '100%' }}
            />
          </ConfettiContainer>
          <Heading bold>Victory!</Heading>
          <ScoreBlock>
            <TeamLine>
              <TeamName bold highlight={isHomeTeam}>
                {fixture.homeTeamName}
              </TeamName>
              <TeamScore bold>{homeScore}</TeamScore>
            </TeamLine>
            {homeScorers.length === 0 && fixture.score && <ScorerLine>No scorers data</ScorerLine>}
            {homeScorers.map((s, idx) => (
              <ScorerLine key={`h-${idx}`}>
                {s.name} ({formatMinute(s.minute)})
              </ScorerLine>
            ))}
            <TeamLine>
              <TeamName bold highlight={isAwayTeam}>
                {fixture.awayTeamName}
              </TeamName>
              <TeamScore bold>{awayScore}</TeamScore>
            </TeamLine>
            {awayScorers.length === 0 && fixture.score && <ScorerLine>No scorers data</ScorerLine>}
            {awayScorers.map((s, idx) => (
              <ScorerLine key={`a-${idx}`}>
                {s.name} ({formatMinute(s.minute)})
              </ScorerLine>
            ))}
          </ScoreBlock>
          <ButtonRow>
            <ContinueButton accessibilityRole="button" onPress={closeCelebration}>
              <ContinueText bold>Continue</ContinueText>
            </ContinueButton>
          </ButtonRow>
        </Panel>
      </Overlay>
    </Modal>
  );
}
