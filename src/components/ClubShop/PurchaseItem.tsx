import { PACKAGE_TYPE } from '@revenuecat/purchases-typescript-internal/dist/offerings';
import React, { useMemo } from 'react';
import { View } from 'react-native';
import { PurchasesPackage } from 'react-native-purchases';
import styled, { DefaultTheme } from 'styled-components/native';
import { Text } from '../Text';

interface StyledProps {
  theme: DefaultTheme;
}

interface PurchaseItemProps {
  package: PurchasesPackage;
  onPress: (packageToPurchase: PurchasesPackage) => void;
  disabled?: boolean;
}

const ItemCard = styled.TouchableOpacity<StyledProps & { disabled?: boolean }>`
  background-color: ${(props) => props.theme.colors.surface};
  border-radius: 12px;
  padding: 20px;
  margin-bottom: 16px;
  border-width: 1px;
  border-color: ${(props) => props.theme.colors.border};
  opacity: ${(props) => (props.disabled ? 0.6 : 1)};
  shadow-color: ${(props) => props.theme.colors.shadow};
  shadow-offset: 0px 2px;
  shadow-opacity: 0.1;
  shadow-radius: 4px;
  elevation: 3;
`;

const ItemHeader = styled.View`
  flex-direction: row;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12px;
`;

const ItemTitle = styled(Text)<StyledProps>`
  font-size: 18px;
  font-family: ${(props) => props.theme.typography.bold};
  color: ${(props) => props.theme.colors.text.primary};
  flex: 1;
  margin-right: 12px;
`;

const ItemPrice = styled(Text)<StyledProps>`
  font-size: 20px;
  font-family: ${(props) => props.theme.typography.bold};
  color: ${(props) => props.theme.colors.primary};
`;

const ItemPriceSub = styled(Text)<StyledProps>`
  font-size: 12px;
  color: ${(props) => props.theme.colors.primary};
  margin-top: 2px;
`;

const ItemDescription = styled(Text)<StyledProps>`
  font-size: 14px;
  color: ${(props) => props.theme.colors.text.secondary};
  line-height: 20px;
  margin-bottom: 16px;
`;

const PurchaseButton = styled.View<StyledProps>`
  background-color: ${(props) => props.theme.colors.primary};
  padding: 12px 20px;
  border-radius: 8px;
  align-items: center;
`;

const PurchaseButtonText = styled(Text)`
  color: white;
  font-family: NunitoBold;
  font-size: 16px;
`;

const PurchaseItem: React.FC<PurchaseItemProps> = ({ package: pkg, onPress, disabled = false }) => {
  const handlePress = () => {
    if (!disabled) {
      onPress(pkg);
    }
  };

  const description = useMemo(() => {
    if (pkg.product.identifier === 'jfg_superfan_1:sf1') {
      return 'Support your team, support this one-man dev team to keep making the game. Every month you will receive the following:\n5 x Magic Sponge, 3 x Red Card Appeal, 5 x Super Scout Token, No Banner Ads';
    }
    return pkg.product.description;
  }, [pkg.product.description, pkg.product.identifier]);

  return (
    <ItemCard onPress={handlePress} disabled={disabled}>
      <ItemHeader>
        <ItemTitle>{pkg.product.title.replace(/\s*\(Jumpers for Goalposts\)$/i, '')}</ItemTitle>
        <View style={{ alignItems: 'center' }}>
          <ItemPrice>{pkg.product.priceString}</ItemPrice>
          {pkg.packageType === PACKAGE_TYPE.MONTHLY && <ItemPriceSub>Monthly</ItemPriceSub>}
        </View>
      </ItemHeader>

      <ItemDescription>{description}</ItemDescription>

      <PurchaseButton>
        <PurchaseButtonText>{disabled ? 'Processing...' : 'Purchase'}</PurchaseButtonText>
      </PurchaseButton>
    </ItemCard>
  );
};

export default PurchaseItem;
