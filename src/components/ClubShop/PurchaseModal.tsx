import React from 'react';
import { Modal } from 'react-native';
import { PurchasesPackage } from 'react-native-purchases';
import styled, { DefaultTheme } from 'styled-components/native';
import { Text } from '../Text';

interface StyledProps {
  theme: DefaultTheme;
}

interface PurchaseModalProps {
  visible: boolean;
  package: PurchasesPackage | null;
  onConfirm: () => void;
  onCancel: () => void;
  isLoading?: boolean;
}

const ModalOverlay = styled.View`
  flex: 1;
  background-color: rgba(0, 0, 0, 0.5);
  justify-content: center;
  align-items: center;
  padding: 20px;
`;

const ModalContent = styled.View<StyledProps>`
  background-color: ${(props) => props.theme.colors.background};
  border-radius: 16px;
  padding: 24px;
  width: 100%;
  max-width: 400px;
  align-items: center;
`;

const ModalTitle = styled(Text)<StyledProps>`
  font-size: 20px;
  font-family: ${(props) => props.theme.typography.bold};
  color: ${(props) => props.theme.colors.text.primary};
  text-align: center;
  margin-bottom: 16px;
`;

const ProductTitle = styled(Text)<StyledProps>`
  font-size: 18px;
  font-family: ${(props) => props.theme.typography.bold};
  color: ${(props) => props.theme.colors.primary};
  text-align: center;
  margin-bottom: 8px;
`;

const ProductDescription = styled(Text)<StyledProps>`
  font-size: 14px;
  color: ${(props) => props.theme.colors.text.secondary};
  text-align: center;
  margin-bottom: 16px;
  line-height: 20px;
`;

const PriceContainer = styled.View<StyledProps>`
  background-color: ${(props) => props.theme.colors.surface};
  padding: 16px;
  border-radius: 8px;
  margin-bottom: 24px;
  width: 100%;
`;

const PriceText = styled(Text)<StyledProps>`
  font-size: 24px;
  font-family: ${(props) => props.theme.typography.bold};
  color: ${(props) => props.theme.colors.primary};
  text-align: center;
`;

const ButtonContainer = styled.View`
  flex-direction: row;
  gap: 12px;
  width: 100%;
`;

const Button = styled.TouchableOpacity<StyledProps & { variant?: 'primary' | 'secondary' }>`
  flex: 1;
  padding: 14px;
  border-radius: 8px;
  align-items: center;
  background-color: ${(props) =>
    props.variant === 'secondary'
      ? props.theme.colors.button.secondary
      : props.theme.colors.primary};
  opacity: ${(props) => (props.disabled ? 0.6 : 1)};
`;

const ButtonText = styled(Text)<{ variant?: 'primary' | 'secondary' }>`
  font-family: NunitoBold;
  font-size: 16px;
  color: ${(props) => (props.variant === 'secondary' ? props.theme.colors.text.primary : 'white')};
`;

const PurchaseModal: React.FC<PurchaseModalProps> = ({
  visible,
  package: pkg,
  onConfirm,
  onCancel,
  isLoading = false,
}) => {
  if (!pkg) return null;

  return (
    <Modal visible={visible} transparent animationType="fade" onRequestClose={onCancel}>
      <ModalOverlay>
        <ModalContent>
          <ModalTitle>Confirm Purchase</ModalTitle>

          <ProductTitle>
            {pkg.product.title.replace(/\s*\(Jumpers for Goalposts\)$/i, '')}
          </ProductTitle>
          <ProductDescription>{pkg.product.description}</ProductDescription>

          <PriceContainer>
            <PriceText>{pkg.product.priceString}</PriceText>
          </PriceContainer>

          <ButtonContainer>
            <Button variant="secondary" onPress={onCancel} disabled={isLoading}>
              <ButtonText variant="secondary">Cancel</ButtonText>
            </Button>

            <Button onPress={onConfirm} disabled={isLoading}>
              <ButtonText>{isLoading ? 'Processing...' : 'Purchase'}</ButtonText>
            </Button>
          </ButtonContainer>
        </ModalContent>
      </ModalOverlay>
    </Modal>
  );
};

export default PurchaseModal;
