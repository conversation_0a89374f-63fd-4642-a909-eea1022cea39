import styled, { DefaultTheme } from 'styled-components/native';
import { Text } from './Text';

export interface StyledProps {
  theme: DefaultTheme;
}

export const Container = styled.View`
  flex: 1;
  background-color: ${(props: StyledProps) => props.theme.colors.background};
  padding: 20px;
`;
export const Title = styled(Text)`
  font-size: 24px;
  font-family: ${(props: StyledProps) => props.theme.typography.bold};
  margin-bottom: 20px;
  text-align: center;
`;
export const Description = styled(Text)`
  font-size: 16px;
  margin-bottom: 30px;
  text-align: center;
`;