import LottieView, { LottieViewProps } from 'lottie-react-native';
import React, { forwardRef } from 'react';

export interface CrossPlatformLottieProps {
  source: LottieViewProps['source'];
  autoPlay?: boolean;
  loop?: boolean;
  style?: any;
  onComplete?: () => void;
}

const CrossPlatformLottie = forwardRef<LottieView, CrossPlatformLottieProps>(
  ({ source, autoPlay = true, loop = false, style, onComplete }, ref) => {
    return (
      <LottieView
        ref={ref}
        source={source}
        autoPlay={autoPlay}
        loop={loop}
        style={style}
        onAnimationFinish={() => onComplete?.()}
      />
    );
  }
);

export default CrossPlatformLottie;
