import Lottie from 'lottie-react';
import React, { forwardRef } from 'react';

export interface CrossPlatformLottieProps {
  source: any; // JSON object for web
  autoPlay?: boolean;
  loop?: boolean;
  style?: React.CSSProperties;
  onComplete?: () => void;
}

// Web implementation using lottie-react
const CrossPlatformLottie = forwardRef<any, CrossPlatformLottieProps>(
  ({ source, autoPlay = true, loop = false, style, onComplete }, ref) => (
    <Lottie
      lottieRef={ref as any}
      animationData={source}
      autoplay={autoPlay}
      loop={loop}
      style={style}
      onComplete={onComplete}
    />
  )
);

export default CrossPlatformLottie;
