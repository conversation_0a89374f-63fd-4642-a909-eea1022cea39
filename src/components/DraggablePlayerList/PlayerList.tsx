/**
 * PlayerList Component
 *
 * This is the main component that renders a reorderable list of players.
 * It uses react-native-reanimated for smooth animations and gesture handling.
 */

import React, { useEffect, useRef, useState } from 'react';
import { ScrollView, View } from 'react-native';

import Animated, {
  scrollTo,
  useAnimatedRef,
  useAnimatedScrollHandler,
  useSharedValue,
} from 'react-native-reanimated';
import styled from 'styled-components/native';
import { Player } from '../../models/player';
import { logger } from '../../utils/logger';
import { PlayerListItem } from './components/PlayerListItem';
import SectionContainer from './components/SectionContainer';
import SectionHeader from './components/SectionHeader';
import { getInitialPositions } from './constants';
import { TPlayerPositions } from './types';

/**
 * ListContainer - The main container for the entire list
 *
 * This container takes up the full height and width of its parent.
 */
const ListContainer = styled.View`
  flex: 1;
  width: 100%;
`;

/**
 * AnimatedScrollView - The scrollable container for list items
 *
 * This is an Animated.ScrollView that contains all the list items.
 * It's styled to take up the full width of its container.
 */
const AnimatedScrollView = styled(Animated.ScrollView)`
  width: 100%; /* Full width to ensure consistent sizing */
`;

interface PlayerListProps {
  data: Player[];
  onReorder: (newData: Player[]) => void;
  onSelect?: (player: Player, currentScrollPosition?: number) => void;
  positionFilter?: 'All' | 'Goalkeeper' | 'Defender' | 'Midfielder' | 'Attacker';
  teamAverages?: Record<string, number>;
  initialScrollPosition?: number;
}

/**
 * PlayerList Component
 *
 * This component manages the state and interactions for the player list.
 */
export const PlayerList: React.FC<PlayerListProps> = ({
  data,
  onReorder,
  onSelect,
  positionFilter = 'All',
  teamAverages = {},
  initialScrollPosition = 0,
}) => {
  logger.log('data', data);

  // Define sections with their colors and player ranges
  const sections = [
    { title: 'Goalkeeper', color: '#ffeb3b', startIndex: 0, endIndex: 0 },
    { title: 'Defenders', color: '#4caf50', startIndex: 1, endIndex: 4 },
    { title: 'Midfielders', color: '#2196f3', startIndex: 5, endIndex: 8 },
    { title: 'Attackers', color: '#f44336', startIndex: 9, endIndex: 10 },
    { title: 'Substitutes', color: '#9c27b0', startIndex: 11, endIndex: 15 },
    { title: 'Squad Players', color: '#607d8b', startIndex: 16, endIndex: data.length - 1 },
  ];

  // Group players into sections
  const getPlayersForSection = (startIndex: number, endIndex: number) => {
    return data.slice(startIndex, endIndex + 1);
  };
  // Reference to the scroll view for programmatic scrolling
  const scrollviewRef = useAnimatedRef();
  const regularScrollRef = useRef<ScrollView>(null);

  // Combined ref handler
  const handleScrollViewRef = (ref: any) => {
    scrollviewRef(ref);
    regularScrollRef.current = ref;
  };

  // Shared value that tracks the position of each player in the list
  const currentPlayerPositions = useSharedValue<TPlayerPositions>(getInitialPositions(data));

  // State for tap mode - track selected players for swapping
  const [selectedPlayers, setSelectedPlayers] = useState<string[]>([]);

  // Handle player selection in tap mode
  const handlePlayerTap = (playerId: string) => {
    setSelectedPlayers((prev) => {
      if (prev.includes(playerId)) {
        // Deselect if already selected
        return prev.filter((id) => id !== playerId);
      } else if (prev.length === 0) {
        // First selection
        return [playerId];
      } else if (prev.length === 1) {
        // Second selection - perform swap
        const firstPlayerId = prev[0];
        const secondPlayerId = playerId;

        // Find indices of the players to swap
        const firstIndex = data.findIndex((p) => p.playerId === firstPlayerId);
        const secondIndex = data.findIndex((p) => p.playerId === secondPlayerId);

        if (firstIndex !== -1 && secondIndex !== -1) {
          // Create new array with swapped players
          const newData = [...data];
          [newData[firstIndex], newData[secondIndex]] = [newData[secondIndex], newData[firstIndex]];
          onReorder(newData);
        }

        // Clear selection after swap
        return [];
      } else {
        // Should not happen, but reset to single selection
        return [playerId];
      }
    });
  };

  // Tracks the current scroll position of the list
  const scrollY = useSharedValue(initialScrollPosition);

  // Update positions when data changes
  useEffect(() => {
    currentPlayerPositions.value = getInitialPositions(data);
  }, [currentPlayerPositions, data]);

  // Restore scroll position when component mounts or initialScrollPosition changes
  useEffect(() => {
    logger.log('PlayerList: initialScrollPosition changed to:', initialScrollPosition);
    if (initialScrollPosition > 0) {
      // Use a small delay to ensure the scroll view is ready
      const timer = setTimeout(() => {
        logger.log('PlayerList: Attempting to scroll to position:', initialScrollPosition);
        // Try both methods to ensure it works
        if (regularScrollRef.current) {
          regularScrollRef.current.scrollTo({ y: initialScrollPosition, animated: false });
          logger.log('PlayerList: Used regular scroll ref');
        } else {
          scrollTo(scrollviewRef, 0, initialScrollPosition, false);
          logger.log('PlayerList: Used animated scroll ref');
        }
      }, 100);
      return () => clearTimeout(timer);
    }
  }, [initialScrollPosition, scrollviewRef]);

  /**
   * scrollHandler - Tracks scroll position changes
   *
   * This animated scroll handler updates the scrollY shared value
   * whenever the user scrolls the list manually.
   */
  const scrollHandler = useAnimatedScrollHandler((event) => {
    scrollY.value = event.contentOffset.y;
  });

  return (
    <ListContainer>
      <AnimatedScrollView
        ref={handleScrollViewRef}
        scrollEventThrottle={10}
        onScroll={scrollHandler}
        style={{ width: '100%' }}
      >
        {sections.map((section) => {
          const sectionPlayers = getPlayersForSection(section.startIndex, section.endIndex);
          if (sectionPlayers.length === 0) return null;

          return (
            <View key={section.title}>
              <SectionHeader title={section.title} backgroundColor={section.color} />
              <SectionContainer backgroundColor={`${section.color}40`}>
                {sectionPlayers.map((player, sectionIndex) => {
                  const globalIndex = section.startIndex + sectionIndex;
                  const isSelectedForSwap = selectedPlayers.includes(player.playerId);
                  return (
                    <PlayerListItem
                      key={player.playerId}
                      item={player}
                      currentPlayerPositions={currentPlayerPositions}
                      scrollY={scrollY}
                      index={globalIndex}
                      onSelect={onSelect}
                      selectedPlayerId={
                        data.find((p) => onSelect && p === data[globalIndex])?.playerId
                      }
                      positionFilter={positionFilter}
                      teamAverages={teamAverages}
                      isSelectedForSwap={isSelectedForSwap}
                      onPlayerTap={handlePlayerTap}
                    />
                  );
                })}
              </SectionContainer>
            </View>
          );
        })}
      </AnimatedScrollView>
    </ListContainer>
  );
};
