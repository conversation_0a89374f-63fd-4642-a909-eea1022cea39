/**
 * PlayerListItem Component
 *
 * This component renders an individual player item in the player list.
 * It uses styled-components for styling. The component is animated using react-native-reanimated.
 */

import React from 'react';
import Animated from 'react-native-reanimated';
import styled from 'styled-components/native';
import { logger } from '../../../utils/logger';
import TapPlayerRow from '../../PlayerRow/TapPlayerRow';
import { PLAYER_CARD_HEIGHT } from '../constants';
import { TPlayerListItem } from '../types';

// Styled components for the PlayerListItem
const AnimatedContainer = styled(Animated.View)`
  height: ${PLAYER_CARD_HEIGHT}px;
  position: relative;
  width: 100%; /* Full width to ensure consistent sizing */
`;

/**
 * PlayerListItem Component
 *
 * This component renders a single player in the player list.
 */
export const PlayerListItem = ({
  item,
  scrollY,
  onSelect,
  selectedPlayerId,
  positionFilter,
  isSelectedForSwap = false,
  onPlayerTap,
}: TPlayerListItem) => {
  // Use neutral background color for all players (unless they're injured/suspended)
  const getBackgroundColor = () => {
    return '#9e9e9e40'; // Neutral gray background for all players
  };

  // Wrapper function to pass current scroll position when selecting a player
  const handlePlayerSelect = (player: any) => {
    logger.log('PlayerListItem: Player selected, current scroll position:', scrollY.value);
    if (onSelect) {
      onSelect(player, scrollY.value);
    }
  };

  return (
    // The container uses animatedStyles from the useGesture hook
    <AnimatedContainer>
      <TapPlayerRow
        player={item}
        backgroundColor={getBackgroundColor()}
        isSelected={selectedPlayerId === item.playerId}
        onSelect={handlePlayerSelect}
        positionFilter={positionFilter}
        isSelectedForSwap={isSelectedForSwap}
        onPlayerTap={onPlayerTap}
      />
    </AnimatedContainer>
  );
};
