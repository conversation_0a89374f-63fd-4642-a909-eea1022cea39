import React from 'react';
import styled from 'styled-components/native';

interface SectionContainerProps {
  backgroundColor: string;
  children: React.ReactNode;
}

const Container = styled.View<{ backgroundColor: string }>`
  background-color: ${(props) => props.backgroundColor};
  margin: 0px 15px 10px;
  border-bottom-left-radius: 8px;
  border-bottom-right-radius: 8px;
  padding: 5px;
`;

const SectionContainer: React.FC<SectionContainerProps> = ({ backgroundColor, children }) => {
  return (
    <Container backgroundColor={backgroundColor}>
      {children}
    </Container>
  );
};

export default SectionContainer;
