import React from 'react';
import styled from 'styled-components/native';
import { StyledProps } from '../../Common';
import { Text } from '../../Text';

interface SectionHeaderProps {
  title: string;
  backgroundColor: string;
}

const HeaderContainer = styled.View<{ backgroundColor: string }>`
  background-color: ${(props) => props.backgroundColor};
  padding: 8px 15px;
  margin: 10px 15px 0px;
  border-top-left-radius: 8px;
  border-top-right-radius: 8px;
`;

const HeaderText = styled(Text)`
  font-size: 16px;
  color: white;
  font-family: ${(props: StyledProps) => props.theme.typography.bold};
  text-transform: uppercase;
`;

const SectionHeader: React.FC<SectionHeaderProps> = ({ title, backgroundColor }) => {
  return (
    <HeaderContainer backgroundColor={backgroundColor}>
      <HeaderText>{title}</HeaderText>
    </HeaderContainer>
  );
};

export default SectionHeader;
