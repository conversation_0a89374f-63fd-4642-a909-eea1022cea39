import { Dimensions, Platform } from 'react-native';
import { TPlayerPositions } from './types';

// Height of each player card in the list
export const PLAYER_CARD_HEIGHT = 100;
// Margin between cards (top and bottom margin combined)
export const CARD_MARGIN = 5;

// Helper function to initialize player positions
export const getInitialPositions = (players: any[]): TPlayerPositions => {
  let playerPositions: TPlayerPositions = {};
  for (let i = 0; i < players.length; i++) {
    playerPositions[players[i].playerId] = {
      updatedIndex: i,
      updatedTop: i * (PLAYER_CARD_HEIGHT + CARD_MARGIN),
    };
  }
  return playerPositions;
};

// Color palette for the list
export const Color_Pallete = {
  metal_black: '#0E0C0A',
  night_shadow: '#1C1C1C',
  crystal_white: '#FFFFFF',
  silver_storm: '#808080',
};

// Platform detection
export const isAndroid = Platform.OS === 'android';
export const isIOS = Platform.OS === 'ios';

// Animation and scrolling constants
export const ANIMATION_DURATION = 600;
export const MIN_BOUNDRY = 0;
export const PAGE_HEADER_OFFSET = 74;
export const SCREEN_HEIGHT = Dimensions.get('window').height;
export const EDGE_THRESHOLD = Platform.select({
  android: 150,
  ios: 100,
  web: 150,
  default: 150,
});
export const SCROLL_SPEED_OFFSET = Platform.select({
  android: 15,
  ios: 8,
  web: 15,
  default: 15,
});
