import { SharedValue } from 'react-native-reanimated';
import { Player } from '../../models/player';

export type TPlayerItem = Player;

export type TPlayerListItem = {
  item: TPlayerItem;
  currentPlayerPositions: SharedValue<TPlayerPositions>;
  scrollY: SharedValue<number>;
  index: number;
  onSelect?: (player: TPlayerItem, currentScrollPosition?: number) => void;
  selectedPlayerId?: string;
  positionFilter?: 'All' | 'Goalkeeper' | 'Defender' | 'Midfielder' | 'Attacker';
  teamAverages?: Record<string, number>;
  isSelectedForSwap?: boolean;
  onPlayerTap?: (playerId: string) => void;
};

export type TPlayerPositions = {
  [key: string]: {
    updatedIndex: number;
    updatedTop: number;
  };
};

export type NullableNumber = null | number;
export type NullableString = null | string;
