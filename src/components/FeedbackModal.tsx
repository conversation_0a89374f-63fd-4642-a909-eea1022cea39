import { MaterialIcons } from '@expo/vector-icons';
import React, { useState } from 'react';
import { ActivityIndicator, Modal, Platform } from 'react-native';
import styled, { DefaultTheme } from 'styled-components/native';
import { FeedbackRequest, submitFeedback } from '../api/feedback';
import { logger } from '../utils/logger';
import { ActionButton, ActionButtonText } from './ActionButton';
import { AlertButton, CrossPlatformAlert } from './CrossPlatformAlert';
import { Text } from './Text';

interface StyledProps {
  theme: DefaultTheme;
}

interface FeedbackModalProps {
  visible: boolean;
  onClose: () => void;
}

const ModalContainer = styled.View`
  flex: 1;
  justify-content: center;
  align-items: center;
  background-color: rgba(0, 0, 0, 0.5);
  padding: 20px;
`;

const ModalContent = styled.View`
  background-color: ${(props: StyledProps) => props.theme.colors.surface};
  border-radius: 12px;
  padding: 24px;
  width: 100%;
  max-width: 400px;
  max-height: 80%;
  height: 66%;
  display: flex;
  flex-direction: column;
  ${Platform.select({
    ios: `
      shadow-color: ${(props: StyledProps) => props.theme.colors.shadow};
      shadow-offset: 0px 4px;
      shadow-opacity: 0.25;
      shadow-radius: 4px;
    `,
    android: `
      elevation: 8;
    `,
  })}
`;

const ModalHeader = styled.View`
  flex-direction: row;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
`;

const ModalTitle = styled(Text)`
  font-size: 20px;
  color: ${(props: StyledProps) => props.theme.colors.text.primary};
  font-family: ${(props: StyledProps) => props.theme.typography.bold};
`;

const CloseButton = styled.TouchableOpacity`
  padding: 4px;
`;

const FormContainer = styled.ScrollView`
  flex: 1;
`;

const InputContainer = styled.View`
  margin-bottom: 16px;
`;

const Label = styled(Text)`
  font-size: 16px;
  color: ${(props: StyledProps) => props.theme.colors.text.primary};
  margin-bottom: 8px;
  font-family: ${(props: StyledProps) => props.theme.typography.bold};
  color: ${(props: StyledProps) => props.theme.colors.text.primary};
`;

const TypeSelector = styled.View`
  flex-direction: row;
  margin-bottom: 16px;
`;

const TypeButton = styled.TouchableOpacity<{ selected: boolean }>`
  flex: 1;
  padding: 12px;
  margin: 0 4px;
  border-radius: 8px;
  border: 1px solid
    ${(props: StyledProps & { selected: boolean }) =>
      props.selected ? props.theme.colors.primary : props.theme.colors.border};
  background-color: ${(props: StyledProps & { selected: boolean }) =>
    props.selected ? props.theme.colors.primary + '20' : 'transparent'};
  align-items: center;
`;

const TypeButtonText = styled(Text)<{ selected: boolean }>`
  font-size: 14px;
  color: ${(props: StyledProps & { selected: boolean }) =>
    props.selected ? props.theme.colors.primary : props.theme.colors.text.secondary};
  font-family: ${(props: StyledProps) => props.theme.typography.bold};
`;

const Input = styled.TextInput`
  background-color: ${(props: StyledProps) => props.theme.colors.background};
  border: 1px solid ${(props: StyledProps) => props.theme.colors.border};
  border-radius: 8px;
  padding: 12px;
  font-size: 16px;
  color: ${(props: StyledProps) => props.theme.colors.text.primary};
  font-family: ${(props: StyledProps) => props.theme.typography.regular};
`;

const TextArea = styled.TextInput`
  background-color: ${(props: StyledProps) => props.theme.colors.background};
  border: 1px solid ${(props: StyledProps) => props.theme.colors.border};
  border-radius: 8px;
  padding: 12px;
  font-size: 16px;
  color: ${(props: StyledProps) => props.theme.colors.text.primary};
  font-family: ${(props: StyledProps) => props.theme.typography.regular};
  min-height: 100px;
`;

const ButtonContainer = styled.View`
  flex-direction: row;
  gap: 12px;
  margin-top: 20px;
`;

const LoadingContainer = styled.View`
  flex-direction: row;
  align-items: center;
  justify-content: center;
  gap: 8px;
`;

export const FeedbackModal: React.FC<FeedbackModalProps> = ({ visible, onClose }) => {
  const [type, setType] = useState<'bug' | 'feature' | 'feedback'>('feedback');
  const [title, setTitle] = useState('');
  const [description, setDescription] = useState('');
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [alertMessage, setAlertMessage] = useState<{
    title: string;
    message: string;
    buttons: AlertButton[];
  } | null>(null);

  const resetForm = () => {
    setType('feedback');
    setTitle('');
    setDescription('');
    setIsSubmitting(false);
  };

  const handleClose = () => {
    if (!isSubmitting) {
      resetForm();
      onClose();
    }
  };

  const handleSubmit = async () => {
    if (!title.trim() || !description.trim()) {
      setAlertMessage({
        title: 'Missing Information',
        message: 'Please fill in both title and description.',
        buttons: [
          {
            text: 'OK',
            onPress: () => setAlertMessage(null),
          },
        ],
      });
      return;
    }
    if (title.trim().length < 5) {
      setAlertMessage({
        title: 'Title Too Short',
        message: 'The title must be at least 5 characters long.',
        buttons: [
          {
            text: 'OK',
            onPress: () => setAlertMessage(null),
          },
        ],
      });
      return;
    }
    if (description.trim().length < 10) {
      setAlertMessage({
        title: 'Description Too Short',
        message: 'The description must be at least 10 characters long.',
        buttons: [
          {
            text: 'OK',
            onPress: () => setAlertMessage(null),
          },
        ],
      });
      return;
    }

    setIsSubmitting(true);

    try {
      const feedbackData: FeedbackRequest = {
        type,
        title: title.trim(),
        description: description.trim(),
        metadata: {
          platform: Platform.OS,
          timestamp: new Date().toISOString(),
          version: Platform.Version,
        },
      };

      const response = await submitFeedback(feedbackData);

      console.log('Feedback response:', response);

      setAlertMessage({
        title: 'Feedback Submitted',
        message: 'Thank you for your feedback! We appreciate your input.',
        buttons: [
          {
            text: 'OK',
            onPress: handleClose,
          },
        ],
      });

      logger.log('Feedback submitted successfully:', response.notionPageId);
    } catch (error) {
      logger.error('Error submitting feedback:', error);
      setAlertMessage({
        title: 'Submission Failed',
        message: "Sorry, we couldn't submit your feedback right now. Please try again later.",
        buttons: [
          {
            text: 'OK',
            onPress: () => setAlertMessage(null),
          },
        ],
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  const typeOptions = [
    { key: 'bug' as const, label: 'Bug Report' },
    { key: 'feature' as const, label: 'Feature Request' },
    { key: 'feedback' as const, label: 'General Feedback' },
  ];

  return (
    <>
      <Modal visible={visible} transparent animationType="fade" onRequestClose={handleClose}>
        <ModalContainer>
          <ModalContent>
            <ModalHeader>
              <ModalTitle>Send Feedback</ModalTitle>
              <CloseButton onPress={handleClose} disabled={isSubmitting}>
                <MaterialIcons name="close" size={24} />
              </CloseButton>
            </ModalHeader>

            <FormContainer showsVerticalScrollIndicator={false}>
              <InputContainer>
                <Label>Feedback Type</Label>
                <TypeSelector>
                  {typeOptions.map((option) => (
                    <TypeButton
                      key={option.key}
                      selected={type === option.key}
                      onPress={() => setType(option.key)}
                      disabled={isSubmitting}
                    >
                      <TypeButtonText selected={type === option.key}>{option.label}</TypeButtonText>
                    </TypeButton>
                  ))}
                </TypeSelector>
              </InputContainer>

              <InputContainer>
                <Label>Title (min 5 characters)</Label>
                <Input
                  value={title}
                  onChangeText={setTitle}
                  placeholder="Brief summary of your feedback"
                  maxLength={100}
                  editable={!isSubmitting}
                />
              </InputContainer>

              <InputContainer>
                <Label>Description (min 10 characters)</Label>
                <TextArea
                  value={description}
                  onChangeText={setDescription}
                  placeholder="Please provide detailed information about your feedback..."
                  multiline
                  maxLength={1000}
                  editable={!isSubmitting}
                />
              </InputContainer>
            </FormContainer>

            <ButtonContainer>
              <ActionButton variant="secondary" onPress={handleClose} disabled={isSubmitting}>
                <ActionButtonText variant="secondary">Cancel</ActionButtonText>
              </ActionButton>
              <ActionButton
                onPress={handleSubmit}
                disabled={
                  isSubmitting ||
                  !title.trim() ||
                  !description.trim() ||
                  title.trim().length < 5 ||
                  description.trim().length < 10
                }
              >
                {isSubmitting ? (
                  <LoadingContainer>
                    <ActivityIndicator size="small" color="#ffffff" />
                    <ActionButtonText>Submitting...</ActionButtonText>
                  </LoadingContainer>
                ) : (
                  <ActionButtonText>Submit</ActionButtonText>
                )}
              </ActionButton>
            </ButtonContainer>
          </ModalContent>
        </ModalContainer>
      </Modal>
      <CrossPlatformAlert
        visible={!!alertMessage}
        title={alertMessage?.title || ''}
        message={alertMessage?.message || ''}
        buttons={alertMessage?.buttons || []}
        onDismiss={() => {
          setAlertMessage(null);
        }}
      />
    </>
  );
};
