import { MaterialIcons } from '@expo/vector-icons';
import { useRouter } from 'expo-router';
import React, { useEffect, useRef, useState } from 'react';
import { Animated, Modal, Platform } from 'react-native';
import styled, { DefaultTheme } from 'styled-components/native';
import { useAuth } from '../context/AuthContext';
import { useManager } from '../context/ManagerContext';
import { useDeviceShake } from '../hooks/useDeviceShake';
import { logger } from '../utils/logger';
import { FeedbackModal } from './FeedbackModal';
import {
  ButtonText,
  CancelButton,
  ModalButtonContainer,
  ModalContainer,
  ModalContent,
  ModalTitle,
  ModalTitleRow,
  ReleaseButton,
} from './PlayerDetail/ModalSharedComponents';
import { Text } from './Text';

interface StyledProps {
  theme: DefaultTheme;
}

interface MenuItem {
  name: string;
  icon: keyof typeof MaterialIcons.glyphMap;
  href: string;
}

interface HamburgerMenuProps {
  loading?: boolean;
}

const HamburgerButton = styled.TouchableOpacity`
  padding-right: 20px;
  z-index: 1;

  ${Platform.select({
    ios: `
      shadow-color: ${(props: StyledProps) => props.theme.colors.shadow};
      shadow-offset: 0px 2px;
      shadow-opacity: 0.25;
      shadow-radius: 3.84px;
    `,
    android: `
      elevation: 3;
    `,
  })}
`;

const MenuContainer = styled(Animated.View)`
  width: 280px;
  height: 100%;
  background-color: ${(props: StyledProps) => props.theme.colors.surface};
  padding: 60px 20px 20px;
  position: absolute;
  right: 0;
  ${Platform.select({
    ios: `
      shadow-color: ${(props: StyledProps) => props.theme.colors.shadow};
      shadow-offset: -2px 0px;
      shadow-opacity: 0.25;
      shadow-radius: 3.84px;
    `,
    android: `
      elevation: 5;
    `,
  })}
`;

const AnimatedModalContainer = styled(Animated.View)`
  flex: 1;
  flex-direction: row;
  background-color: rgba(0, 0, 0, 0.5);
`;

const MenuScrollContainer = styled.ScrollView`
  flex: 1;
`;

const MenuHeader = styled.View`
  flex-direction: row;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 30px;
  padding-bottom: 20px;
  border-bottom-width: 1px;
  border-bottom-color: ${(props: StyledProps) => props.theme.colors.border};
`;

const MenuTitle = styled(Text)`
  font-size: 24px;
  color: ${(props: StyledProps) => props.theme.colors.text.primary};
  font-family: ${(props: StyledProps) => props.theme.typography.bold};
`;

const CloseButton = styled.TouchableOpacity`
  padding: 5px;
`;

const MenuItem = styled.TouchableOpacity`
  flex-direction: row;
  align-items: center;
  padding: 15px 0;
  border-bottom-width: 1px;
  border-bottom-color: ${(props: StyledProps) => props.theme.colors.border}20;
`;

const MenuItemIcon = styled.View`
  width: 40px;
  margin-right: 15px;
`;

const MenuItemText = styled(Text)`
  font-size: 18px;
  color: ${(props: StyledProps) => props.theme.colors.text.primary};
  font-family: 'Nunito';
`;

const CloseArea = styled.TouchableOpacity`
  flex: 1;
`;

// Custom styled components for warning modal specific styling
const WarningIcon = styled.View`
  align-items: center;
  margin-bottom: 16px;
`;

const WarningText = styled(Text)`
  font-size: 16px;
  color: ${(props: StyledProps) => props.theme.colors.text.secondary};
  text-align: center;
  line-height: 22px;
  margin-bottom: 24px;
`;

const ThemedIcon = styled(MaterialIcons)<{ loading?: boolean }>`
  color: ${(props: StyledProps & { loading?: boolean }) =>
    props.loading ? props.theme.colors.text.secondary : props.theme.colors.text.primary};
`;

const WarningCloseButton = styled.TouchableOpacity`
  padding: 4px;
`;

export const HamburgerMenu: React.FC<HamburgerMenuProps> = ({ loading = false }) => {
  const [isVisible, setIsVisible] = useState(false);
  const [modalVisible, setModalVisible] = useState(false);
  const [isAnimating, setIsAnimating] = useState(false);
  const [isFeedbackVisible, setIsFeedbackVisible] = useState(false);
  const [isExitWarningVisible, setIsExitWarningVisible] = useState(false);
  const feedbackModalOpenRef = useRef(false);
  const slideAnim = useRef(new Animated.Value(280)).current; // Start off-screen (menu width)
  const fadeAnim = useRef(new Animated.Value(0)).current; // Start transparent
  const router = useRouter();
  const { logout: managerLogout } = useManager();
  const { logout, isAnonymous } = useAuth();

  // Device shake detection to open feedback
  useDeviceShake({
    onShake: () => {
      if (!feedbackModalOpenRef.current) {
        setIsFeedbackVisible(true);
        feedbackModalOpenRef.current = true;
      }
    },
  });

  // Ensure ref is in sync with modal visibility
  useEffect(() => {
    if (!isFeedbackVisible) {
      feedbackModalOpenRef.current = false;
    }
  }, [isFeedbackVisible]);

  // Handle modal opening
  useEffect(() => {
    if (isVisible && !modalVisible) {
      setModalVisible(true);
      setIsAnimating(true);
      Animated.parallel([
        Animated.timing(slideAnim, {
          toValue: 0,
          duration: 300,
          useNativeDriver: true,
        }),
        Animated.timing(fadeAnim, {
          toValue: 1,
          duration: 300,
          useNativeDriver: true,
        }),
      ]).start(() => setIsAnimating(false));
    }
  }, [isVisible, modalVisible, slideAnim, fadeAnim]);

  const closeMenu = () => {
    if (isAnimating) return; // Prevent multiple close attempts

    setIsAnimating(true);
    Animated.parallel([
      Animated.timing(slideAnim, {
        toValue: 280,
        duration: 300,
        useNativeDriver: true,
      }),
      Animated.timing(fadeAnim, {
        toValue: 0,
        duration: 300,
        useNativeDriver: true,
      }),
    ]).start(() => {
      setModalVisible(false);
      setIsVisible(false);
      setIsAnimating(false);
    });
  };

  const openMenu = () => {
    setIsVisible(true);
  };

  const hideEmail = isAnonymous;
  const hidePush = Platform.OS === 'web';
  const hideNotificationsMenu = hideEmail && hidePush;

  const menuItems: MenuItem[] = [
    { name: 'Home', icon: 'home', href: '/' },
    { name: 'Team', icon: 'group', href: '/team' },
    { name: 'League', icon: 'leaderboard', href: '/league' },
    { name: 'Trophy Room', icon: 'military-tech', href: '/trophy-room' },
    { name: 'Fixtures', icon: 'event', href: '/fixtures' },
    { name: 'Transfers', icon: 'swap-horiz', href: '/transfers' },
    { name: 'Training', icon: 'fitness-center', href: '/training' },
    { name: 'Finances', icon: 'account-balance-wallet', href: '/finances' },
    { name: 'Club Shop', icon: 'shopping-cart', href: '/club-shop' },
    { name: 'Team Settings', icon: 'settings', href: '/team-settings' },
    { name: 'Profile', icon: 'person', href: '/manager-profile' },
    // Only show Notifications if not hidden
    ...(!hideNotificationsMenu
      ? [
          {
            name: 'Notifications',
            icon: 'notifications' as keyof typeof MaterialIcons.glyphMap,
            href: '/notification-settings',
          },
        ]
      : []),
  ];

  const handleFeedbackPress = () => {
    closeMenu();
    setIsFeedbackVisible(true);
  };

  const handleMenuItemPress = (href: string) => {
    closeMenu();
    logger.debug('Navigating to:', href);
    router.push(href);
  };

  const handleLogout = async () => {
    if (isAnonymous) {
      closeMenu();
      setIsExitWarningVisible(true);
    } else {
      closeMenu();
      await managerLogout();
      await logout();
      //router.replace('/welcome');   // AuthContext should handle this - at least for sso
    }
  };

  const handleExitAnyway = async () => {
    setIsExitWarningVisible(false);
    await managerLogout();
    await logout();
    //router.replace('/welcome');   // AuthContext should handle this - at least for sso
  };

  const handleCreateAccount = () => {
    setIsExitWarningVisible(false);
    router.push('/login');
  };

  const handleCancelExit = () => {
    setIsExitWarningVisible(false);
  };

  return (
    <>
      <HamburgerButton onPress={openMenu} disabled={loading}>
        <MaterialIcons name="menu" size={24} color={'#000000'} />
      </HamburgerButton>

      <Modal visible={modalVisible} transparent animationType="none" onRequestClose={closeMenu}>
        <AnimatedModalContainer style={{ opacity: fadeAnim }}>
          <CloseArea onPress={closeMenu} />
          <MenuContainer style={{ transform: [{ translateX: slideAnim }] }}>
            <MenuHeader>
              <MenuTitle>Menu</MenuTitle>
              <CloseButton onPress={closeMenu}>
                <ThemedIcon name="close" size={24} />
              </CloseButton>
            </MenuHeader>

            <MenuScrollContainer showsVerticalScrollIndicator={false}>
              {menuItems.map((item) => (
                <MenuItem
                  key={item.name}
                  onPress={() => handleMenuItemPress(item.href)}
                  disabled={loading}
                  style={loading ? { opacity: 0.5 } : {}}
                >
                  <MenuItemIcon>
                    <ThemedIcon name={item.icon} size={24} loading={loading} />
                  </MenuItemIcon>
                  <MenuItemText>{item.name}</MenuItemText>
                </MenuItem>
              ))}

              {/* Create Account option for anonymous users */}
              {isAnonymous && (
                <MenuItem
                  onPress={() => {
                    closeMenu();
                    router.push('/login');
                  }}
                  disabled={loading}
                  style={loading ? { opacity: 0.5 } : {}}
                >
                  <MenuItemIcon>
                    <ThemedIcon name="person-add" size={24} loading={loading} />
                  </MenuItemIcon>
                  <MenuItemText>Create Account / Log In</MenuItemText>
                </MenuItem>
              )}

              {/* Feedback option */}
              <MenuItem
                onPress={handleFeedbackPress}
                disabled={loading}
                style={loading ? { opacity: 0.5 } : {}}
              >
                <MenuItemIcon>
                  <ThemedIcon name="feedback" size={24} loading={loading} />
                </MenuItemIcon>
                <MenuItemText>Send Feedback</MenuItemText>
              </MenuItem>

              {/* Logout option */}
              <MenuItem
                onPress={handleLogout}
                disabled={loading}
                style={loading ? { opacity: 0.5 } : {}}
              >
                <MenuItemIcon>
                  <ThemedIcon name="logout" size={24} loading={loading} />
                </MenuItemIcon>
                <MenuItemText>{isAnonymous ? 'Exit Guest Mode' : 'Logout'}</MenuItemText>
              </MenuItem>
            </MenuScrollContainer>
          </MenuContainer>
        </AnimatedModalContainer>
      </Modal>

      <FeedbackModal visible={isFeedbackVisible} onClose={() => setIsFeedbackVisible(false)} />

      {/* Exit Warning Modal for Anonymous Users */}
      <Modal
        visible={isExitWarningVisible}
        transparent
        animationType="slide"
        onRequestClose={handleCancelExit}
      >
        <ModalContainer>
          <ModalContent>
            <ModalTitleRow>
              <ModalTitle>Warning</ModalTitle>
              <WarningCloseButton onPress={handleCancelExit}>
                <ThemedIcon name="close" size={24} />
              </WarningCloseButton>
            </ModalTitleRow>

            <WarningIcon>
              <MaterialIcons name="warning" size={40} color="#e3172a" />
            </WarningIcon>

            <WarningText>
              You are currently in Guest Mode. If you exit, you may lose access to your team.
              {'\n\n'}
              Do you want to exit anyway, or would you like to create an account to save your
              progress?
            </WarningText>

            <ModalButtonContainer>
              <ReleaseButton onPress={handleExitAnyway}>
                <ButtonText>Exit Anyway</ButtonText>
              </ReleaseButton>

              <CancelButton onPress={handleCreateAccount}>
                <ButtonText>Create Account</ButtonText>
              </CancelButton>
            </ModalButtonContainer>
          </ModalContent>
        </ModalContainer>
      </Modal>
    </>
  );
};
