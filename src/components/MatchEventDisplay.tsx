import React from 'react';
import styled, { DefaultTheme } from 'styled-components/native';
import { PossibleEventSubstitution } from '../models/fixture';
import { Player } from '../models/player';
import { useTheme } from '../theme/ThemeContext';
import { Text } from './Text';

interface StyledProps {
  theme: DefaultTheme;
}

export interface MatchEvent {
  localisationId: string;
  substitutions: PossibleEventSubstitution;
  minute: number;
  half: number;
}
interface Props {
  events: MatchEvent[];
  commentary: Record<string, string>;
  homeTeamName: string;
  awayTeamName: string;
  homePlayers: Player[];
  awayPlayers: Player[];
}

const EventsSection = styled.View`
  padding: 16px;
  background-color: ${(props: StyledProps) => props.theme.colors.surface};
`;

const EventItem = styled.View`
  flex-direction: row;
  align-items: center;
  padding: 8px 0;
  border-bottom-width: 1px;
  border-bottom-color: ${(props: StyledProps) => props.theme.colors.border};
`;

const EventTime = styled(Text)`
  font-size: 14px;
  color: ${(props: StyledProps) => props.theme.colors.text.secondary};
  font-family: 'Nunito';
  width: 40px;
`;

const EventText = styled(Text)`
  flex: 1;
  font-size: 14px;
  color: ${(props: StyledProps) => props.theme.colors.text.primary};
  font-family: 'Nunito';
  margin-left: 16px;
`;

const HomeHighlight = styled(Text)`
  color: ${(props: StyledProps) => props.theme.colors.success};
  font-family: ${(props: StyledProps) => props.theme.typography.bold};
`;

const AwayHighlight = styled(Text)`
  color: ${(props: StyledProps) => props.theme.colors.primary};
  font-family: ${(props: StyledProps) => props.theme.typography.bold};
`;

const TeamColorLegend = styled.View`
  flex-direction: row;
  align-items: center;
  margin-bottom: 8px;
`;
const TeamColorItem = styled.View`
  flex-direction: row;
  align-items: center;
  margin-right: 16px;
`;
const ColorSwatch = styled.View<{ color: string }>`
  width: 14px;
  height: 14px;
  border-radius: 7px;
  background-color: ${(props) => props.color};
  margin-right: 6px;
`;

const formatMatchTime = (minute: number, half: number): string => {
  const adjustedMinute = half === 2 ? minute + 45 : minute;
  return `${adjustedMinute}'`;
};

const formatPlayerName = (player: Player): string => {
  return `${player.firstName[0]}. ${player.surname}`;
};

const isHomeTeamEntity = (
  value: string | undefined,
  homeTeamName: string,
  awayTeamName: string,
  homePlayers: Player[],
  awayPlayers: Player[]
): boolean => {
  if (!value) return false;

  // Check team names
  if (value === homeTeamName) return true;
  if (value === awayTeamName) return false;

  // Check if the value matches any player name
  const homePlayerMatch = homePlayers.some((player) => formatPlayerName(player) === value);
  const awayPlayerMatch = awayPlayers.some((player) => formatPlayerName(player) === value);

  return homePlayerMatch && !awayPlayerMatch;
};

const getLocalisation = (stringId: string, commentary: Record<string, string>): string => {
  if (commentary[stringId]) {
    return commentary[stringId];
  }

  const variants = Object.keys(commentary).filter(
    (key) => key.startsWith(stringId + '_') && /^\d+$/.test(key.split('_').pop() || '')
  );

  if (variants.length > 0) {
    const randomVariant = variants[Math.floor(Math.random() * variants.length)];
    return commentary[randomVariant] || '';
  }

  return '';
};

const transformEvents = (
  events: MatchEvent[],
  homePlayers: Player[],
  awayPlayers: Player[],
  homeTeamName: string,
  awayTeamName: string
): MatchEvent[] => {
  const allPlayers = [...homePlayers, ...awayPlayers];
  const playerMap = new Map(
    allPlayers.map((player) => [player.playerId, formatPlayerName(player)])
  );

  return events.map((event) => ({
    ...event,
    substitutions: {
      ...event.substitutions,
      player: event.substitutions.player
        ? playerMap.get(event.substitutions.player) || event.substitutions.player
        : undefined,
      oppPlayer: event.substitutions.oppPlayer
        ? playerMap.get(event.substitutions.oppPlayer) || event.substitutions.oppPlayer
        : undefined,
      nextPlayer: event.substitutions.nextPlayer
        ? playerMap.get(event.substitutions.nextPlayer) || event.substitutions.nextPlayer
        : undefined,
      team: event.substitutions.team === homeTeamName ? homeTeamName : awayTeamName,
      oppTeam: event.substitutions.oppTeam === homeTeamName ? homeTeamName : awayTeamName,
      homeTeam: homeTeamName,
      awayTeam: awayTeamName,
    },
  }));
};

const renderEventText = (
  template: string,
  substitutions: PossibleEventSubstitution,
  homeTeamName: string,
  awayTeamName: string,
  homePlayers: Player[],
  awayPlayers: Player[]
): React.ReactNode[] => {
  const parts: React.ReactNode[] = [];
  let lastIndex = 0;
  const regex = /{(\w+)}/g;
  let match;

  while ((match = regex.exec(template)) !== null) {
    const [fullMatch, key] = match;
    const value = substitutions[key as keyof PossibleEventSubstitution];

    if (match.index > lastIndex) {
      parts.push(template.substring(lastIndex, match.index));
    }

    if (value) {
      const isHome = isHomeTeamEntity(value, homeTeamName, awayTeamName, homePlayers, awayPlayers);
      const Highlight = isHome ? HomeHighlight : AwayHighlight;
      parts.push(<Highlight key={match.index}>{value}</Highlight>);
    } else {
      parts.push(fullMatch);
    }

    lastIndex = regex.lastIndex;
  }

  if (lastIndex < template.length) {
    parts.push(template.substring(lastIndex));
  }

  return parts;
};

const MatchEventDisplay: React.FC<Props> = ({
  events,
  commentary,
  homeTeamName,
  awayTeamName,
  homePlayers,
  awayPlayers,
}) => {
  const { theme } = useTheme();
  const transformedEvents = transformEvents(
    events,
    homePlayers,
    awayPlayers,
    homeTeamName,
    awayTeamName
  );

  return (
    <EventsSection>
      <TeamColorLegend>
        <TeamColorItem>
          <ColorSwatch color={theme.colors.success} />
          <Text style={{ fontFamily: theme.typography.bold }}>{homeTeamName}</Text>
        </TeamColorItem>
        <TeamColorItem>
          <ColorSwatch color={theme.colors.primary} />
          <Text style={{ fontFamily: theme.typography.bold }}>{awayTeamName}</Text>
        </TeamColorItem>
      </TeamColorLegend>
      {transformedEvents.map((event, index) => {
        const template = getLocalisation(event.localisationId, commentary);
        return (
          <EventItem key={index}>
            <EventTime>{formatMatchTime(event.minute, event.half)}</EventTime>
            <EventText>
              {renderEventText(
                template,
                event.substitutions,
                homeTeamName,
                awayTeamName,
                homePlayers,
                awayPlayers
              )}
            </EventText>
          </EventItem>
        );
      })}
    </EventsSection>
  );
};

export default MatchEventDisplay;
