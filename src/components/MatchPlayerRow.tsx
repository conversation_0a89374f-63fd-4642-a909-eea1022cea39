import { MaterialIcons } from '@expo/vector-icons';
import React from 'react';
import { Image } from 'react-native';
import styled from 'styled-components/native';
import { MatchPlayer } from '../models/fixture';
import { useTheme } from '../theme/ThemeContext';
import { StyledProps } from './Common';
import { Text } from './Text';

interface Props {
  player: MatchPlayer;
  isAfter11?: boolean;
}

const PlayerRow = styled.View<{ isAfter11?: boolean }>`
  flex-direction: row;
  align-items: center;
  padding: 12px 0;
  border-bottom-width: ${(props) => (props.isAfter11 ? 3 : 1)}px;
  border-bottom-color: ${(props: StyledProps) => props.theme.colors.border};
`;

const PlayerInfo = styled.View`
  flex: 1;
  flex-direction: row;
  align-items: center;
`;

const PlayerName = styled(Text)`
  font-size: 16px;
  color: ${(props: StyledProps) => props.theme.colors.text.primary};
  font-family: 'Nunito';
  flex: 1;
`;

const StatusContainer = styled.View`
  flex-direction: row;
  align-items: center;
  margin-left: 8px;
`;

const GoalContainer = styled.View`
  flex-direction: row;
  align-items: center;
  margin-left: 8px;
`;

const GoalText = styled(Text)`
  font-size: 12px;
  color: ${(props: StyledProps) => props.theme.colors.text.primary};
  font-family: ${(props: StyledProps) => props.theme.typography.bold};
  margin-left: 2px;
`;

const RatingContainer = styled.View`
  min-width: 40px;
  align-items: flex-end;
`;

const RatingText = styled(Text)`
  font-size: 16px;
  color: ${(props: StyledProps) => props.theme.colors.text.primary};
  font-family: ${(props: StyledProps) => props.theme.typography.bold};
`;

const SubInfo = styled.View`
  flex-direction: row;
  align-items: center;
  margin-left: 6px;
`;

const SubText = styled(Text)`
  font-size: 12px;
  margin-left: 2px;
  font-family: ${(props: StyledProps) => props.theme.typography.bold};
`;

const formatPlayerName = (playerName: string): string => {
  const parts = playerName.split(' ');
  if (parts.length < 2) return playerName;

  const firstName = parts[0];
  const surname = parts.slice(1).join(' ');
  return `${firstName[0]}. ${surname}`;
};

const SubOn: React.FC<{ minute: number }> = ({ minute }) => {
  const { theme } = useTheme();
  return (
    <SubInfo>
      <MaterialIcons name="arrow-upward" size={16} color="#4CAF50" />
      <SubText style={{ color: theme.colors.success }}>{minute}'</SubText>
    </SubInfo>
  );
};

const SubOff: React.FC<{ minute: number }> = ({ minute }) => {
  const { theme } = useTheme();
  return (
    <SubInfo>
      <MaterialIcons name="arrow-downward" size={16} color="#F44336" />
      <SubText style={{ color: theme.colors.error }}>{minute}'</SubText>
    </SubInfo>
  );
};

const MatchPlayerRow: React.FC<Props> = ({ player, isAfter11 }) => {
  const formattedName = formatPlayerName(player.playerName);
  const isInjured = player.injured !== undefined && player.injured > 0;
  const isSentOff = player.sentOff !== undefined && player.sentOff > 0;
  const goalCount = player.goals?.length || 0;

  return (
    <PlayerRow isAfter11={isAfter11}>
      <PlayerInfo>
        <PlayerName>{formattedName}</PlayerName>
        {/* Substitution info */}
        {((player.joinedMatchMinute !== undefined && player.joinedMatchMinute > 0) ||
          player.leftMatchMinute !== undefined) && (
          <SubInfo>
            {/* Swap icons: subbed ON for joinedMatchMinute, subbed OFF for leftMatchMinute */}
            {player.joinedMatchMinute !== undefined && player.joinedMatchMinute > 0 && (
              <SubOn minute={player.joinedMatchMinute} />
            )}
            {player.leftMatchMinute !== undefined && <SubOff minute={player.leftMatchMinute} />}
          </SubInfo>
        )}

        <StatusContainer>
          {isInjured && (
            <Image
              source={require('../../assets/injury.png')}
              style={{ width: 16, height: 16, marginRight: 4 }}
              resizeMode="contain"
            />
          )}
          {isSentOff && (
            <Image
              source={require('../../assets/redcard.png')}
              style={{ width: 16, height: 16, marginRight: 4 }}
              resizeMode="contain"
            />
          )}
        </StatusContainer>

        {goalCount > 0 && (
          <GoalContainer>
            <MaterialIcons name="sports-soccer" size={16} color="#4CAF50" />
            {goalCount > 1 && <GoalText>x{goalCount}</GoalText>}
          </GoalContainer>
        )}
      </PlayerInfo>

      <RatingContainer>
        {player.rating !== undefined && <RatingText>{player.rating.toFixed(1)}</RatingText>}
      </RatingContainer>
    </PlayerRow>
  );
};

export default MatchPlayerRow;
