import React, { useState } from 'react';
import { TouchableOpacity } from 'react-native';
import styled from 'styled-components/native';
import { MatchPlayer } from '../models/fixture';
import { StyledProps } from './Common';
import MatchPlayerRow from './MatchPlayerRow';
import { Text } from './Text';

interface TabProps extends StyledProps {
  isActive: boolean;
}

interface Props {
  homePlayers: MatchPlayer[];
  awayPlayers: MatchPlayer[];
  homeTeamName: string;
  awayTeamName: string;
}

const PlayersSection = styled.View`
  background-color: ${(props: StyledProps) => props.theme.colors.surface};
  margin-bottom: 16px;
`;

const SectionTitle = styled(Text)`
  font-size: 18px;
  color: ${(props: StyledProps) => props.theme.colors.text.primary};
  font-family: ${(props: StyledProps) => props.theme.typography.bold};
  padding: 16px;
  padding-bottom: 0;
`;

const TabContainer = styled.View`
  flex-direction: row;
  border-bottom-width: 1px;
  border-bottom-color: ${(props: StyledProps) => props.theme.colors.primary};
  margin: 0 16px;
`;

const Tab = styled(TouchableOpacity)<TabProps>`
  flex: 1;
  padding: 12px;
  align-items: center;
  border-bottom-width: 2px;
  border-bottom-color: ${(props) => (props.isActive ? props.theme.colors.primary : 'transparent')};
`;

const TabText = styled(Text)<TabProps>`
  font-family: ${(props: StyledProps) => props.theme.typography.bold};
  font-size: 14px;
  color: ${(props) =>
    props.isActive ? props.theme.colors.text.primary : props.theme.colors.text.secondary};
`;

const PlayersList = styled.View`
  padding: 16px;
`;

const MatchPlayersDisplay: React.FC<Props> = ({
  homePlayers,
  awayPlayers,
  homeTeamName,
  awayTeamName,
}) => {
  const [activeTab, setActiveTab] = useState<'home' | 'away'>('home');

  const reconstructLineup = (players: MatchPlayer[]) => {
    // Find all starting players (not subbed on)
    const starters = players.filter((p) => !p.joinedMatchMinute);
    // Map of playerId to player for quick lookup
    const playerMap = Object.fromEntries(players.map((p) => [p.playerId, p]));
    // Build the lineup, replacing subbed off players with their subs
    let lineup = [...starters];
    // For each starter, if they were subbed off, replace them with their sub
    lineup = lineup.map((player) => {
      if (player.substitute && playerMap[player.substitute]) {
        return playerMap[player.substitute];
      }
      return player;
    });
    // Add any subs who came on but didn't replace a starter (e.g. extra subs)
    const subs = players.filter(
      (p) => p.joinedMatchMinute && !starters.some((s) => s.substitute === p.playerId)
    );
    return [...lineup, ...subs];
  };

  const currentPlayersRaw = activeTab === 'home' ? homePlayers : awayPlayers;
  const currentPlayers = reconstructLineup(currentPlayersRaw);

  return (
    <PlayersSection>
      <SectionTitle>MATCH SQUADS</SectionTitle>

      <TabContainer>
        <Tab isActive={activeTab === 'home'} onPress={() => setActiveTab('home')}>
          <TabText isActive={activeTab === 'home'}>{homeTeamName.toUpperCase()}</TabText>
        </Tab>
        <Tab isActive={activeTab === 'away'} onPress={() => setActiveTab('away')}>
          <TabText isActive={activeTab === 'away'}>{awayTeamName.toUpperCase()}</TabText>
        </Tab>
      </TabContainer>

      <PlayersList>
        {currentPlayers.map((player, index) => (
          <MatchPlayerRow
            key={`${player.playerId}-${index}`}
            player={player}
            isAfter11={index === 10}
          />
        ))}
      </PlayersList>
    </PlayersSection>
  );
};

export default MatchPlayersDisplay;
