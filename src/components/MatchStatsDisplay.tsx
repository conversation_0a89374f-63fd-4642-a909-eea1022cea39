import React from 'react';
import styled, { DefaultTheme } from 'styled-components/native';
import { Scorer } from './MatchScoreDisplay';
import { Text } from './Text';

interface StyledProps {
  theme: DefaultTheme;
}

export interface MatchStats {
  possession: [string, string];
  shots: [number, number];
  score: [number, number];
  shotsOnTarget: [number, number];
  corners: [number, number];
  fouls: [number, number];
  yellowCards: [number, number];
  redCards: [number, number];
  passes: [number, number];
  passAccuracy: [number, number];
  tackles: [number, number];
  interceptions: [number, number];

  scorers?: Scorer[];
}

interface MatchStatsDisplayProps {
  stats: MatchStats;
}

const StatsSection = styled.View`
  padding: 16px;
  background-color: ${(props: StyledProps) => props.theme.colors.surface};
  margin-bottom: 16px;
`;

const StatRow = styled.View`
  flex-direction: row;
  justify-content: space-between;
  align-items: center;
  padding: 8px 0;
  border-bottom-width: 1px;
  border-bottom-color: ${(props: StyledProps) => props.theme.colors.border};
`;

const StatValue = styled(Text)`
  font-size: 16px;
  color: ${(props: StyledProps) => props.theme.colors.text.primary};
  font-family: ${(props: StyledProps) => props.theme.typography.bold};
  flex: 1;
  text-align: center;
`;

const StatLabel = styled(Text)`
  font-size: 14px;
  color: ${(props: StyledProps) => props.theme.colors.text.secondary};
  font-family: 'Nunito';
  flex: 2;
  text-align: center;
`;

const calculatePosession = (value: string, totalPossession: [string, string]): number => {
  return Math.round(
    (Number.parseInt(value) /
      (Number.parseInt(totalPossession[0]) + Number.parseInt(totalPossession[1]))) *
      100
  );
};

const MatchStatsDisplay = ({ stats }: MatchStatsDisplayProps) => {
  return (
    <StatsSection>
      <StatRow>
        <StatValue>{`${calculatePosession(stats.possession[0], stats.possession)}%`}</StatValue>
        <StatLabel>Possession</StatLabel>
        <StatValue>{`${calculatePosession(stats.possession[1], stats.possession)}%`}</StatValue>
      </StatRow>
      <StatRow>
        <StatValue>{stats.shots[0]}</StatValue>
        <StatLabel>Shots</StatLabel>
        <StatValue>{stats.shots[1]}</StatValue>
      </StatRow>
      <StatRow>
        <StatValue>{stats.shotsOnTarget[0]}</StatValue>
        <StatLabel>Shots on Target</StatLabel>
        <StatValue>{stats.shotsOnTarget[1]}</StatValue>
      </StatRow>
      <StatRow>
        <StatValue>{stats.passes[0]}</StatValue>
        <StatLabel>Passes</StatLabel>
        <StatValue>{stats.passes[1]}</StatValue>
      </StatRow>
      <StatRow>
        <StatValue>{`${stats.passAccuracy[0]}%`}</StatValue>
        <StatLabel>Pass Accuracy</StatLabel>
        <StatValue>{`${stats.passAccuracy[1]}%`}</StatValue>
      </StatRow>
      <StatRow>
        <StatValue>{stats.tackles[0]}</StatValue>
        <StatLabel>Tackles</StatLabel>
        <StatValue>{stats.tackles[1]}</StatValue>
      </StatRow>
      <StatRow>
        <StatValue>{stats.interceptions[0]}</StatValue>
        <StatLabel>Interceptions</StatLabel>
        <StatValue>{stats.interceptions[1]}</StatValue>
      </StatRow>
      <StatRow>
        <StatValue>{stats.corners[0]}</StatValue>
        <StatLabel>Corners</StatLabel>
        <StatValue>{stats.corners[1]}</StatValue>
      </StatRow>
      <StatRow>
        <StatValue>{stats.fouls[0]}</StatValue>
        <StatLabel>Fouls</StatLabel>
        <StatValue>{stats.fouls[1]}</StatValue>
      </StatRow>
      <StatRow>
        <StatValue>{stats.yellowCards[0]}</StatValue>
        <StatLabel>Yellow Cards</StatLabel>
        <StatValue>{stats.yellowCards[1]}</StatValue>
      </StatRow>
      <StatRow>
        <StatValue>{stats.redCards[0]}</StatValue>
        <StatLabel>Red Cards</StatLabel>
        <StatValue>{stats.redCards[1]}</StatValue>
      </StatRow>
    </StatsSection>
  );
};

export default MatchStatsDisplay;
