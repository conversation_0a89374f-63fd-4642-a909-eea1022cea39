import React, { useState } from 'react';
import { Modal } from 'react-native';
import styled from 'styled-components/native';
import { formatCurrencyShort } from '../utils/utils';
import { ActionButton, ActionButtonText } from './ActionButton';
import { StyledProps } from './Common';
import { CrossPlatformAlert } from './CrossPlatformAlert';
import {
  ButtonRowContainer,
  DetailText,
  ModalContainer,
  ModalContent,
  ModalTitle,
} from './PlayerDetail/ModalSharedComponents';
import { Text } from './Text';

interface TeamData {
  teamId: string;
  teamName: string;
  gameworldId: string;
  balance: number;
  played: number;
  points: number;
  wins: number;
  draws: number;
  losses: number;
  managerStats: {
    scoutTokens: number;
    superScoutTokens: number;
    magicSponges: number;
    cardAppeals: number;
    trainingBoosts: number;
    loginStreak: number;
    wins: number;
    defeats: number;
    trophies: number;
  };
}

interface MigrationConflictsModalProps {
  visible: boolean;
  guestData?: TeamData;
  authenticatedData?: TeamData;
  guestUserId: string;
  onSelectGuest: () => void;
  onSelectAuthenticated: () => void;
  onCancel: () => void;
  isResolving: boolean;
}

const TeamContainer = styled.View`
  flex-direction: row;
  gap: 16px;
  margin-bottom: 24px;
`;

const TeamCard = styled.TouchableOpacity<{ isSelected: boolean }>`
  flex: 1;
  background-color: ${(props: StyledProps) => props.theme.colors.background};
  border: 2px solid
    ${(props: StyledProps & { isSelected: boolean }) =>
      props.isSelected ? props.theme.colors.primary : props.theme.colors.border};
  border-radius: 12px;
  padding: 16px;
  opacity: ${(props: { disabled?: boolean }) => (props.disabled ? 0.5 : 1)};
`;

const TeamHeader = styled.View`
  margin-bottom: 12px;
`;

const TeamLabel = styled(Text)<{ isSelected: boolean }>`
  font-size: 14px;
  font-family: ${(props: StyledProps) => props.theme.typography.bold};
  color: ${(props: StyledProps & { isSelected: boolean }) =>
    props.isSelected ? props.theme.colors.primary : props.theme.colors.text.secondary};
  margin-bottom: 4px;
`;

const TeamName = styled(Text)<{ isSelected: boolean }>`
  font-size: 18px;
  font-family: ${(props: StyledProps) => props.theme.typography.bold};
  color: ${(props: StyledProps) => props.theme.colors.text.primary};
  margin-bottom: 8px;
`;

const StatRow = styled.View`
  flex-direction: row;
  justify-content: space-between;
  margin-bottom: 4px;
`;

const StatLabel = styled(Text)`
  font-size: 14px;
  color: ${(props: StyledProps) => props.theme.colors.text.secondary};
`;

const StatValue = styled(Text)`
  font-size: 14px;
  font-family: ${(props: StyledProps) => props.theme.typography.bold};
  color: ${(props: StyledProps) => props.theme.colors.text.primary};
`;

const WarningText = styled(DetailText)`
  color: #e3172a;
  font-family: ${(props: StyledProps) => props.theme.typography.bold};
  margin-bottom: 16px;
`;

const ModalScrollContainer = styled.ScrollView`
  border: 0px;
`;

export const MigrationConflictsModal: React.FC<MigrationConflictsModalProps> = ({
  visible,
  guestData,
  authenticatedData,
  onSelectGuest,
  onSelectAuthenticated,
  onCancel,
  isResolving,
}) => {
  const [selectedTeam, setSelectedTeam] = useState<'guest' | 'authenticated' | null>(null);
  const [showConfirmModal, setShowConfirmModal] = useState(false);

  const handleTeamSelection = (team: 'guest' | 'authenticated') => {
    setSelectedTeam(team);
  };

  const handleConfirm = () => {
    if (!selectedTeam) return;
    setShowConfirmModal(true);
  };

  const handleFinalConfirm = () => {
    setShowConfirmModal(false);
    if (selectedTeam === 'guest') {
      onSelectGuest();
    } else {
      onSelectAuthenticated();
    }
  };

  const getSelectedTeamName = () => {
    if (selectedTeam === 'guest') return guestData?.teamName;
    if (selectedTeam === 'authenticated') return authenticatedData?.teamName;
    return '';
  };

  const getOtherTeamName = () => {
    if (selectedTeam === 'guest') return authenticatedData?.teamName;
    if (selectedTeam === 'authenticated') return guestData?.teamName;
    return '';
  };

  return (
    <>
      <Modal visible={visible} transparent animationType="fade" onRequestClose={onCancel}>
        <ModalContainer>
          <ModalContent style={{ maxWidth: 500, maxHeight: '80%' }}>
            <ModalTitle>Choose Your Team Data</ModalTitle>
            <DetailText>
              We found existing data for both your guest session and your account. Please choose
              which team data you'd like to keep.
            </DetailText>

            <WarningText>
              ⚠️ This is a destructive operation. The unselected team will be permanently released.
            </WarningText>

            <ModalScrollContainer showsVerticalScrollIndicator={false}>
              <TeamContainer>
                {guestData && (
                  <TeamCard
                    isSelected={selectedTeam === 'guest'}
                    onPress={() => handleTeamSelection('guest')}
                    disabled={isResolving}
                  >
                    <TeamHeader>
                      <TeamLabel isSelected={selectedTeam === 'guest'}>Guest Team</TeamLabel>
                      <TeamName isSelected={selectedTeam === 'guest'}>
                        {guestData.teamName}
                      </TeamName>
                    </TeamHeader>

                    <StatRow>
                      <StatLabel>Balance:</StatLabel>
                      <StatValue>{formatCurrencyShort(guestData.balance)}</StatValue>
                    </StatRow>
                    <StatRow>
                      <StatLabel>League Points:</StatLabel>
                      <StatValue>{guestData.points}</StatValue>
                    </StatRow>
                    <StatRow>
                      <StatLabel>Matches Played:</StatLabel>
                      <StatValue>{guestData.played}</StatValue>
                    </StatRow>
                    <StatRow>
                      <StatLabel>Record:</StatLabel>
                      <StatValue>
                        {guestData.wins}W-{guestData.draws}D-{guestData.losses}L
                      </StatValue>
                    </StatRow>
                    <StatRow>
                      <StatLabel>Scout Tokens:</StatLabel>
                      <StatValue>{guestData.managerStats.scoutTokens}</StatValue>
                    </StatRow>
                    <StatRow>
                      <StatLabel>Magic Sponges:</StatLabel>
                      <StatValue>{guestData.managerStats.magicSponges}</StatValue>
                    </StatRow>
                    <StatRow>
                      <StatLabel>Login Streak:</StatLabel>
                      <StatValue>{guestData.managerStats.loginStreak}</StatValue>
                    </StatRow>
                    <StatRow>
                      <StatLabel>Trophies:</StatLabel>
                      <StatValue>{guestData.managerStats.trophies}</StatValue>
                    </StatRow>
                  </TeamCard>
                )}

                {authenticatedData && (
                  <TeamCard
                    isSelected={selectedTeam === 'authenticated'}
                    onPress={() => handleTeamSelection('authenticated')}
                    disabled={isResolving}
                  >
                    <TeamHeader>
                      <TeamLabel isSelected={selectedTeam === 'authenticated'}>
                        Account Team
                      </TeamLabel>
                      <TeamName isSelected={selectedTeam === 'authenticated'}>
                        {authenticatedData.teamName}
                      </TeamName>
                    </TeamHeader>

                    <StatRow>
                      <StatLabel>Balance:</StatLabel>
                      <StatValue>{formatCurrencyShort(authenticatedData.balance)}</StatValue>
                    </StatRow>
                    <StatRow>
                      <StatLabel>League Points:</StatLabel>
                      <StatValue>{authenticatedData.points}</StatValue>
                    </StatRow>
                    <StatRow>
                      <StatLabel>Matches Played:</StatLabel>
                      <StatValue>{authenticatedData.played}</StatValue>
                    </StatRow>
                    <StatRow>
                      <StatLabel>Record:</StatLabel>
                      <StatValue>
                        {authenticatedData.wins}W-{authenticatedData.draws}D-
                        {authenticatedData.losses}L
                      </StatValue>
                    </StatRow>
                    <StatRow>
                      <StatLabel>Scout Tokens:</StatLabel>
                      <StatValue>{authenticatedData.managerStats.scoutTokens}</StatValue>
                    </StatRow>
                    <StatRow>
                      <StatLabel>Magic Sponges:</StatLabel>
                      <StatValue>{authenticatedData.managerStats.magicSponges}</StatValue>
                    </StatRow>
                    <StatRow>
                      <StatLabel>Login Streak:</StatLabel>
                      <StatValue>{authenticatedData.managerStats.loginStreak}</StatValue>
                    </StatRow>
                    <StatRow>
                      <StatLabel>Trophies:</StatLabel>
                      <StatValue>{authenticatedData.managerStats.trophies}</StatValue>
                    </StatRow>
                  </TeamCard>
                )}
              </TeamContainer>
            </ModalScrollContainer>

            <ButtonRowContainer>
              <ActionButton variant={'secondary'} onPress={onCancel} disabled={isResolving}>
                <ActionButtonText variant={'secondary'}>Cancel & Logout</ActionButtonText>
              </ActionButton>
              <ActionButton
                variant={'danger'}
                onPress={handleConfirm}
                disabled={!selectedTeam || isResolving}
                style={{ opacity: !selectedTeam || isResolving ? 0.5 : 1 }}
              >
                <ActionButtonText>Continue Migration</ActionButtonText>
              </ActionButton>
            </ButtonRowContainer>
          </ModalContent>
        </ModalContainer>
      </Modal>

      <CrossPlatformAlert
        visible={showConfirmModal}
        title="Confirm Team Migration"
        message={`This is a destructive operation. This will keep "${getSelectedTeamName()}" and release "${getOtherTeamName()}" back into the game world. This cannot be undone. Are you sure?`}
        buttons={[
          {
            text: 'Cancel',
            onPress: () => setShowConfirmModal(false),
            style: 'cancel',
          },
          {
            text: 'Yes, Continue',
            onPress: handleFinalConfirm,
            style: 'destructive',
          },
        ]}
        onDismiss={() => setShowConfirmModal(false)}
      />
    </>
  );
};
