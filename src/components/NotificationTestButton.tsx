import React from 'react';
import { TouchableOpacity } from 'react-native';
import styled from 'styled-components/native';
import {
  getScheduledNotifications,
  scheduleTestNotification,
} from '../services/NotificationScheduler';
import { logger } from '../utils/logger';
import { StyledProps } from './Common';
import { Text } from './Text';

const TestButton = styled(TouchableOpacity)`
  background-color: #007bff;
  padding: 10px 15px;
  border-radius: 5px;
  margin: 5px;
  align-items: center;
`;

const ButtonText = styled(Text)`
  color: white;
  font-family: ${(props: StyledProps) => props.theme.typography.bold};
`;

const Container = styled.View`
  flex-direction: row;
  flex-wrap: wrap;
  justify-content: center;
  margin: 10px 0;
`;

interface NotificationTestButtonProps {
  visible?: boolean;
}

export const NotificationTestButton: React.FC<NotificationTestButtonProps> = ({
  visible = __DEV__, // Only show in development by default
}) => {
  if (!visible) {
    return null;
  }

  const handleTestNotification = async () => {
    logger.log('Testing notification...');
    await scheduleTestNotification();
  };

  const handleShowScheduled = async () => {
    logger.log('Getting scheduled notifications...');
    await getScheduledNotifications();
  };

  return (
    <Container>
      <TestButton onPress={handleTestNotification}>
        <ButtonText>Test Notification</ButtonText>
      </TestButton>
      <TestButton onPress={handleShowScheduled}>
        <ButtonText>Show Scheduled</ButtonText>
      </TestButton>
    </Container>
  );
};
