import React from 'react';
import styled, { DefaultTheme } from 'styled-components/native';
import { Text } from '../Text';

interface StyledProps {
  theme: DefaultTheme;
}

interface Attribute {
  name: string;
  value: number;
  potential?: number;
}

interface AttributeTableProps {
  title: string;
  attributes: Attribute[];
  floorAttributes: boolean;
}

const AttributeTableContainer = styled.View`
  margin-bottom: 24px;
`;

const AttributeTableHeader = styled.View`
  background-color: ${(props: StyledProps) => props.theme.colors.primary};
  padding: 8px 12px;
  border-top-left-radius: 8px;
  border-top-right-radius: 8px;
`;

const AttributeTableHeaderText = styled(Text)`
  font-size: 16px;
  color: white;
  font-family: ${(props: StyledProps) => props.theme.typography.bold};
`;

const AttributeRow = styled.View`
  flex-direction: row;
  padding: 8px 12px;
  border-bottom-width: 1px;
  border-bottom-color: #e0e0e0;
  background-color: ${(props: StyledProps) => props.theme.colors.background};
`;

const AttributeName = styled(Text)`
  flex: 1;
  font-size: 16px;
  color: ${(props: StyledProps) => props.theme.colors.text.primary};
  font-family: 'Nunito';
`;

const AttributeValue = styled(Text)`
  font-size: 16px;
  color: ${(props: StyledProps) => props.theme.colors.text.primary};
  font-family: ${(props: StyledProps) => props.theme.typography.bold};
`;

export const AttributeTable: React.FC<AttributeTableProps> = ({
  title,
  attributes,
  floorAttributes,
}) => {
  return (
    <AttributeTableContainer>
      <AttributeTableHeader>
        <AttributeTableHeaderText>{title}</AttributeTableHeaderText>
      </AttributeTableHeader>
      {attributes.map((attr, index) => (
        <AttributeRow key={`${title.toLowerCase()}-${index}`}>
          <AttributeName>{attr.name}</AttributeName>
          {attr.potential && (
            <AttributeValue>
              {attr.value.toFixed(2)} → {attr.potential}
            </AttributeValue>
          )}
          {!attr.potential && (
            <AttributeValue>
              {floorAttributes ? Math.floor(attr.value) : attr.value.toFixed(2)}
            </AttributeValue>
          )}
        </AttributeRow>
      ))}
    </AttributeTableContainer>
  );
};
