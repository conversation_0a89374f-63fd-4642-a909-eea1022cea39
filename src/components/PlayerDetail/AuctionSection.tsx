import React, { useEffect, useState } from 'react';
import styled from 'styled-components/native';
import { TransferListPlayer } from '../../models/player';
import { Text } from '../Text';
import { BidHistory } from './BidHistory';
import { useAuctionTimer } from './hooks/useAuctionTimer';

import { usePlayerUtils } from '../../context/PlayerContext';
import { useCachedMyActiveTransfers } from '../../hooks/useCachedData';
import { useTheme } from '../../theme/ThemeContext';
import { formatCurrencyShort } from '../../utils/utils';
import { ActionButton, ActionButtonText } from '../ActionButton';
import { StyledProps } from '../Common';
import { PlayerStatus } from './hooks/usePlayerStatus';

interface TransferSectionProps {
  player: TransferListPlayer;
  playerStatus: PlayerStatus;
  isPlayerInUserTeam: boolean;
  maxBid: number | null;
  isHighestBidder: boolean;
}

const StatusContainer = styled.View`
  background-color: #e3172a;
  padding: 12px;
  border-radius: 8px;
  margin-bottom: 24px;
  align-items: center;
`;

const StatusText = styled(Text)`
  font-size: 16px;
  color: white;
  font-family: ${(props: StyledProps) => props.theme.typography.bold};
`;

export const AuctionSection: React.FC<TransferSectionProps> = ({
  player,
  playerStatus,
  isPlayerInUserTeam,
  maxBid,
  isHighestBidder,
}) => {
  const [alreadyMadeOffer, setAlreadyMadeOffer] = useState(false);
  //const [transfer, setTransfer] = useState<ActiveTransfer | null>(null);
  const { playerActions } = usePlayerUtils();
  const { timeRemaining, auctionCompleted } = useAuctionTimer(player);
  const { data: myBidsData } = useCachedMyActiveTransfers(player.gameworldId);
  const { theme } = useTheme();

  useEffect(() => {
    const matchingTransfer = myBidsData.transfers.find(
      (t) => t.player.playerId === player.playerId
    );
    setAlreadyMadeOffer(!!matchingTransfer);
    //setTransfer(matchingTransfer || null);
  }, [myBidsData.transfers, player.playerId]);

  const handleTransferPress = () => {
    playerActions.prefillOfferAmount(
      player as TransferListPlayer,
      playerStatus.isAuctionPlayer,
      currentBid
    );
    playerActions.setIsOfferModalVisible(true);
  };

  const currentBid = player.bidHistory?.length
    ? Math.max(...(player.bidHistory || []).map((bid) => bid.maximumBid))
    : player.auctionStartPrice || 0;

  const formattedCurrentBid = currentBid ? formatCurrencyShort(currentBid) : 'No bids';

  return (
    <>
      <StatusContainer style={{ backgroundColor: '#2E7D32' }}>
        <StatusText>
          Current Bid: {maxBid ? formatCurrencyShort(maxBid) : formattedCurrentBid}
        </StatusText>
        <StatusText>{timeRemaining}</StatusText>
        {maxBid && (
          <StatusText
            style={{
              color: isHighestBidder ? '#FFEB3B' : '#FFFFFF',
              fontFamily: theme.typography.bold,
            }}
          >
            {isHighestBidder ? '✓ You are the highest bidder!' : '✗ You are not the highest bidder'}
          </StatusText>
        )}
      </StatusContainer>

      {/* Only show Bid button if auction is not completed and no offer has been made */}
      {!alreadyMadeOffer && !auctionCompleted && !isPlayerInUserTeam && (
        <ActionButton variant="primary" onPress={handleTransferPress}>
          <ActionButtonText>Bid Now</ActionButtonText>
        </ActionButton>
      )}

      {/* Bid History Section */}
      <BidHistory transferListPlayer={player} />
    </>
  );
};
