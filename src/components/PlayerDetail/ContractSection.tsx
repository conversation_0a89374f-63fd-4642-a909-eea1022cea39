import React, { useCallback, useRef, useState } from 'react';
import { CorePlayer, Player } from '../../models/player';

import { usePlayerUtils } from '../../context/PlayerContext';
import { ActionButton, ActionButtonText } from '../ActionButton';

interface TransferSectionProps {
  player: CorePlayer;
}

export const ContractSection: React.FC<TransferSectionProps> = ({ player }) => {
  const { playerActions } = usePlayerUtils();
  const [optimisticTransferStatus, setOptimisticTransferStatus] = useState<boolean | null>(null);
  const debounceTimeoutRef = useRef<NodeJS.Timeout | null>(null);

  // Use optimistic status if available, otherwise use actual player status
  const displayTransferStatus =
    optimisticTransferStatus !== null ? optimisticTransferStatus : player.isTransferListed;

  const handleToggleTransferStatus = useCallback(async () => {
    const newStatus = !displayTransferStatus;

    // Set optimistic state immediately
    setOptimisticTransferStatus(newStatus);

    // Clear any existing debounce timeout
    if (debounceTimeoutRef.current) {
      clearTimeout(debounceTimeoutRef.current);
    }

    // Debounce the API call
    debounceTimeoutRef.current = setTimeout(async () => {
      try {
        await playerActions.toggleIsTransferListed(player as Player, newStatus);
        // Reset optimistic state after successful API call
        setOptimisticTransferStatus(null);
      } catch {
        // Revert optimistic state on error
        setOptimisticTransferStatus(!newStatus);
      }
    }, 500); // 500ms debounce
  }, [displayTransferStatus, player, playerActions]);

  // Cleanup timeout on unmount
  React.useEffect(() => {
    return () => {
      if (debounceTimeoutRef.current) {
        clearTimeout(debounceTimeoutRef.current);
      }
    };
  }, []);

  return (
    <>
      <ActionButton variant="primary" onPress={handleToggleTransferStatus}>
        <ActionButtonText>
          {displayTransferStatus ? 'Remove from Transfer List' : 'Add to Transfer List'}
        </ActionButtonText>
      </ActionButton>
      <ActionButton variant="danger" onPress={() => playerActions.setIsReleaseModalVisible(true)}>
        <ActionButtonText>Release Player</ActionButtonText>
      </ActionButton>
    </>
  );
};
