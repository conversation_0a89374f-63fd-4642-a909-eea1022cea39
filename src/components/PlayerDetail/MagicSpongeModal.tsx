import { useRouter } from 'expo-router';
import React, { useCallback, useEffect, useMemo, useRef } from 'react';
import { Animated, Easing, Image, Modal, View } from 'react-native';
import { usePlayerUtils } from '../../context/PlayerContext';
import { Player } from '../../models/player';
import { AD_TYPES, useRewardAd } from '../Ads';
import { MagicSpongeActions, MagicSpongeState } from './hooks/useMagicSponge';
import {
  ButtonText,
  DetailText,
  ModalButton,
  ModalButtonContainer,
  ModalContainer,
  ModalContent,
  ModalPill,
  ModalPillText,
  ModalTitle,
  ModalTitleRow,
  TransferButton,
} from './ModalSharedComponents';

interface MagicSpongeModalProps {
  player: Player;
  isInjured: boolean;
  spongesAvailable: number;
  magicSpongeState: MagicSpongeState;
  magicSpongeActions: MagicSpongeActions;
}

export const MagicSpongeModal: React.FC<MagicSpongeModalProps> = ({
  player,
  isInjured,
  spongesAvailable,
  magicSpongeState,
  magicSpongeActions,
}) => {
  const { isAdLoaded, showAd } = useRewardAd();
  const { playerActions } = usePlayerUtils();
  const router = useRouter();

  const handleMagicSpongeUse = async () => {
    await magicSpongeActions.useMagicSponge(player as Player, (_title, _message) => {
      playerActions.setShowAlert(true);
    });
  };

  const rewardEarned = useCallback(() => {
    magicSpongeActions.incrementMagicSpongeCount();
  }, [magicSpongeActions]);

  console.log('MagicSpongeModal rendered', {
    playerId: player.playerId,
    isInjured,
    spongesAvailable,
    isSpongeModalVisible: magicSpongeState.isSpongeModalVisible,
  });

  // Improved explanatory copy + before/after preview
  const { spongeReason, previewLines, actionLabel } = useMemo(() => {
    if (isInjured) {
      const now = Date.now();
      const msRemaining =
        player.injuredUntil && player.injuredUntil > now ? player.injuredUntil - now : 0;
      const daysRemaining = Math.max(0, Math.ceil(msRemaining / (1000 * 60 * 60 * 24)));
      const daysAfter = Math.max(0, daysRemaining - 1);
      const healsCompletely = daysAfter === 0;
      return {
        spongeReason: healsCompletely
          ? 'Use a magic sponge to COMPLETELY heal this injury right now.'
          : 'Use a magic sponge to speed up recovery and remove 1 day from the injury.',
        previewLines: [
          `Injury remaining: ${daysRemaining} day${daysRemaining === 1 ? '' : 's'} → ${daysAfter} day${daysAfter === 1 ? '' : 's'}${daysAfter === 0 ? ' (healed)' : ''}`,
        ],
        actionLabel: healsCompletely ? 'Heal injury now' : 'Reduce injury by 1 day',
      };
    }
    // Energy case
    const currentEnergy = player.energy ?? 0;
    const energyAfter = 100;
    const gain = Math.max(0, energyAfter - currentEnergy);
    return {
      spongeReason: "Use a magic sponge to instantly restore this player's energy to 100%.",
      previewLines: [`Energy: ${currentEnergy}% → ${energyAfter}%${gain > 0 ? ` (+${gain})` : ''}`],
      actionLabel: gain > 0 ? 'Restore to 100%' : 'Energy already full',
    };
  }, [isInjured, player.energy, player.injuredUntil]);

  // SUCCESS / RESULT HANDLING
  const result = magicSpongeState.lastResult;

  // Animation for success pulse
  const successScale = useRef(new Animated.Value(1));
  useEffect(() => {
    if (result) {
      successScale.current.setValue(0.92);
      Animated.sequence([
        Animated.timing(successScale.current, {
          toValue: 1.05,
          duration: 220,
          easing: Easing.out(Easing.quad),
          useNativeDriver: true,
        }),
        Animated.timing(successScale.current, {
          toValue: 1,
          duration: 180,
          easing: Easing.inOut(Easing.quad),
          useNativeDriver: true,
        }),
      ]).start();
    }
  }, [result]);

  // Auto-close shortly after showing result to reinforce action happened
  useEffect(() => {
    if (result) {
      const timeout = setTimeout(() => {
        // Close first; result will be cleared automatically on next open (hook wrapper)
        magicSpongeActions.setIsSpongeModalVisible(false);
      }, 1600);
      return () => clearTimeout(timeout);
    }
  }, [result, magicSpongeActions]);

  // Format result lines
  const resultLines = useMemo(() => {
    if (!result) return [] as string[];
    const lines: string[] = [];
    if (result.injuryMsBefore !== undefined) {
      const msPerDay = 1000 * 60 * 60 * 24;
      const daysBefore = Math.max(0, Math.ceil(result.injuryMsBefore / msPerDay));
      const daysAfter = result.injuryMsAfter
        ? Math.max(0, Math.ceil(result.injuryMsAfter / msPerDay))
        : 0;
      lines.push(
        `Injury: ${daysBefore} day${daysBefore === 1 ? '' : 's'} → ${daysAfter} day${daysAfter === 1 ? '' : 's'}${daysAfter === 0 ? ' (healed)' : ''}`
      );
    }
    if (result.energyBefore !== result.energyAfter) {
      lines.push(
        `Energy: ${result.energyBefore}% → ${result.energyAfter}% (+${result.energyAfter - result.energyBefore})`
      );
    }
    return lines;
  }, [result]);

  const closeModal = () => {
    // Close without resetting to avoid brief re-render of initial state
    magicSpongeActions.setIsSpongeModalVisible(false);
  };

  return (
    <Modal
      visible={magicSpongeState.isSpongeModalVisible}
      transparent
      animationType="fade"
      onRequestClose={closeModal}
    >
      <ModalContainer>
        <ModalContent>
          <ModalTitleRow>
            <ModalTitle>Magic Sponge</ModalTitle>
            <ModalPill>
              <Image
                source={require('../../../assets/magicSponge.png')}
                style={{ width: 24, height: 24 }}
              />
              <ModalPillText>{spongesAvailable}</ModalPillText>
            </ModalPill>
          </ModalTitleRow>

          {!result && (
            <>
              <DetailText>{spongeReason}</DetailText>
              <View style={{ marginTop: 8, marginBottom: 4 }}>
                {previewLines.map((l) => (
                  <DetailText key={l} style={{ fontWeight: '600' }}>
                    {l}
                  </DetailText>
                ))}
              </View>
            </>
          )}

          {result && (
            <Animated.View
              style={{
                marginTop: 4,
                marginBottom: 12,
                transform: [{ scale: successScale.current }],
              }}
            >
              <DetailText style={{ fontWeight: '700', color: '#2e7d32' }}>
                {result.healed
                  ? '✅ Injury healed!'
                  : result.injuryMsBefore !== undefined
                    ? '👍 Injury recovery accelerated'
                    : '⚡ Energy restored'}
              </DetailText>
              {resultLines.map((l) => (
                <DetailText key={l} style={{ fontWeight: '600' }}>
                  {l}
                </DetailText>
              ))}
            </Animated.View>
          )}

          <ModalButtonContainer>
            {!result && (
              <ModalButton
                disabled={
                  spongesAvailable === 0 ||
                  magicSpongeState.isSpongeLoading ||
                  (actionLabel === 'Energy already full' && !isInjured)
                }
                onPress={handleMagicSpongeUse}
                style={{
                  opacity:
                    spongesAvailable === 0 ||
                    magicSpongeState.isSpongeLoading ||
                    (actionLabel === 'Energy already full' && !isInjured)
                      ? 0.5
                      : 1,
                }}
                testID="magic-sponge-apply"
              >
                <ButtonText>
                  {magicSpongeState.isSpongeLoading ? 'Applying…' : actionLabel}
                </ButtonText>
              </ModalButton>
            )}
            {!result && (
              <ModalButton
                disabled={!isAdLoaded(AD_TYPES.MAGIC_SPONGE)}
                onPress={async () => await showAd(AD_TYPES.MAGIC_SPONGE, rewardEarned)}
                style={{ opacity: !isAdLoaded(AD_TYPES.MAGIC_SPONGE) ? 0.5 : 1 }}
              >
                <ButtonText>Watch ad</ButtonText>
              </ModalButton>
            )}
            {!result && (
              <ModalButton
                onPress={() => {
                  router.push('/club-shop');
                  closeModal();
                }}
              >
                <ButtonText>Buy more</ButtonText>
              </ModalButton>
            )}
          </ModalButtonContainer>
          {!result && (
            <TransferButton onPress={closeModal} style={{ marginTop: 16 }}>
              <ButtonText style={{ color: '#333' }}>Close</ButtonText>
            </TransferButton>
          )}
        </ModalContent>
      </ModalContainer>
    </Modal>
  );
};
