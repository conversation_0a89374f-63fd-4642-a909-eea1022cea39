import styled from 'styled-components/native';
import { StyledProps } from '../Common';
import { Text } from '../Text';

export const DetailText = styled(Text)`
  font-size: 16px;
  color: ${(props: StyledProps) => props.theme.colors.text.secondary};
  text-align: center;
  margin-bottom: 24px;
  line-height: 22px;
`;

export const ModalContainer = styled.View`
  flex: 1;
  justify-content: center;
  align-items: center;
  background-color: rgba(0, 0, 0, 0.5);
  padding: 20px;
`;

export const ModalContent = styled.View`
  background-color: ${(props: StyledProps) =>
    props.theme.colors.surface || props.theme.colors.background};
  border-radius: 12px;
  padding: 24px;
  width: 100%;
  max-width: 400px;
`;

export const ModalTitle = styled(Text)`
  font-size: 20px;
  font-family: ${(props: StyledProps) => props.theme.typography.bold};
  color: ${(props: StyledProps) => props.theme.colors.text.primary};
  margin-bottom: 16px;
  text-align: center;
`;

export const ModalButtonContainer = styled.View`
  width: 100%;
  gap: 12px;
`;

export const ModalButton = styled.TouchableOpacity`
  background-color: ${(props: StyledProps) => props.theme.colors.primary};
  border-radius: 8px;
  padding: 16px;
  align-items: center;
  width: 100%;
`;

export const TransferButton = styled.TouchableOpacity`
  background-color: ${(props: StyledProps) => props.theme.colors.button.primary};
  border: 1px solid ${(props: StyledProps) => props.theme.colors.border || 'transparent'};
  border-radius: 8px;
  padding: 16px;
  align-items: center;
  width: 100%;
`;

export const ButtonText = styled(Text)`
  color: white;
  font-family: ${(props: StyledProps) => props.theme.typography.bold};
  font-size: 16px;
`;

export const InputContainer = styled.View`
  margin-bottom: 16px;
`;

export const Label = styled(Text)`
  font-family: ${(props: StyledProps) => props.theme.typography.regular};
  font-size: 16px;
  margin-bottom: 8px;
`;

export const ButtonRowContainer = styled.View`
  flex-direction: column;
  justify-content: space-between;
  margin-top: 16px;
  gap: 6px;
`;

export const ButtonColumnContainer = styled.View`
  flex-direction: column;
  justify-content: space-between;
  margin-top: 16px;
  gap: 12px;
`;

export const ModalTitleRow = styled.View`
  flex-direction: row;
  align-items: center;
  justify-content: space-between;
  width: 100%;
  margin-bottom: 16px;
`;

export const ModalPill = styled.View`
  flex-direction: row;
  align-items: center;
  background-color: #f0c419;
  border-radius: 16px;
  padding: 8px 12px;
`;

export const ModalPillText = styled.Text`
  color: #333;
  font-family: ${(props: StyledProps) => props.theme.typography.bold};
  font-size: 14px;
  margin-left: 8px;
`;

export const WarningLabel = styled(Label)`
  color: #e3172a;
  font-family: ${(props: StyledProps) => props.theme.typography.bold};
  margin-top: 10px;
`;

export const CancelButton = styled(TransferButton)`
  background-color: #888;
`;

export const ReleaseButton = styled(TransferButton)`
  background-color: #e3172a;
`;
