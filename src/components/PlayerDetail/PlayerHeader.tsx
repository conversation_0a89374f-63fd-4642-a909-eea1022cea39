import { MaterialIcons } from '@expo/vector-icons';
import React from 'react';
import styled, { DefaultTheme } from 'styled-components/native';
import { CorePlayer } from '../../models/player';
import { Text } from '../Text';

interface StyledProps {
  theme: DefaultTheme;
}

interface PlayerHeaderProps {
  player: CorePlayer;
  onClose: () => void;
}

const HeaderContainer = styled.View`
  align-items: center;
  margin-bottom: 24px;
`;

const PlayerNameText = styled(Text)`
  font-size: 28px;
  color: ${(props: StyledProps) => props.theme.colors.text.primary};
  font-family: ${(props: StyledProps) => props.theme.typography.bold};
  text-align: center;
`;

const PlayerIdText = styled(Text)`
  font-size: 14px;
  color: ${(props: StyledProps) => props.theme.colors.text.secondary};
  font-family: 'Nunito';
  margin-top: 4px;
`;

const CloseButton = styled.TouchableOpacity`
  position: absolute;
  top: 16px;
  left: 16px;
  z-index: 1;
  background-color: ${(props: StyledProps) => props.theme.colors.primary};
  width: 40px;
  height: 40px;
  border-radius: 20px;
  justify-content: center;
  align-items: center;
`;

export const PlayerHeader: React.FC<PlayerHeaderProps> = ({ player, onClose }) => {
  return (
    <>
      <CloseButton onPress={onClose}>
        <MaterialIcons name="arrow-back" size={24} color="white" />
      </CloseButton>

      <HeaderContainer>
        <PlayerNameText>{`${player.firstName} ${player.surname}`}</PlayerNameText>
        <PlayerIdText>{`ID: ${player.playerId}`}</PlayerIdText>
      </HeaderContainer>
    </>
  );
};
