import React from 'react';
import { Image } from 'react-native';
import styled, { DefaultTheme } from 'styled-components/native';
import { CorePlayer } from '../../models/player';
import { Team } from '../../models/team';
import { Text } from '../Text';
import { MagicSpongeActions } from './hooks/useMagicSponge';
import { PlayerStatus } from './hooks/usePlayerStatus';
import { RedCardAppealActions } from './hooks/useRedCardAppeal';

interface StyledProps {
  theme: DefaultTheme;
}

interface PlayerInfoProps {
  player: CorePlayer;
  team?: Team;
  playerStatus: PlayerStatus;
  magicSpongeActions: MagicSpongeActions;
  redCardAppealActions: RedCardAppealActions;
}

const InfoContainer = styled.View`
  flex-direction: row;
  margin-bottom: 24px;
`;

const PlayerImageContainer = styled.View`
  width: 120px;
  height: 120px;
  margin-right: 16px;
  border-radius: 8px;
  overflow: hidden;
  background-color: #f0f0f0;
`;

const PlayerStatsContainer = styled.View`
  flex: 1;
  justify-content: center;
`;

const DetailText = styled(Text)`
  font-size: 16px;
  color: ${(props: StyledProps) => props.theme.colors.text.primary};
  font-family: ${(props: StyledProps) => props.theme.typography.bold};
  margin-bottom: 8px;
`;

const PlayerBoosterButton = styled.TouchableOpacity`
  position: absolute;
  bottom: 8px;
  right: 8px;
  background: rgba(255, 255, 255, 0.85);
  border-radius: 18px;
  padding: 2px;
  z-index: 10;
`;

export const PlayerInfo: React.FC<PlayerInfoProps> = ({
  player,
  team,
  playerStatus,
  magicSpongeActions,
  redCardAppealActions,
}) => {
  const {
    formattedValue,
    currentEnergy,
    energyByNextMatch,
    isInjured,
    isPlayerInUserTeam,
    bestPosition,
    isSuspended,
  } = playerStatus;

  const showMagicSponge = isPlayerInUserTeam && (currentEnergy! < 100 || isInjured);
  const showRedCardAppeal = isPlayerInUserTeam && isSuspended;

  return (
    <InfoContainer>
      <PlayerImageContainer>
        <Image
          source={require('../../../assets/mugshot.png')}
          style={{ width: '100%', height: '100%' }}
          resizeMode="cover"
        />
        {!showRedCardAppeal && showMagicSponge && (
          <PlayerBoosterButton onPress={() => magicSpongeActions.setIsSpongeModalVisible(true)}>
            <Image
              source={require('../../../assets/magicSponge.png')}
              style={{ width: 36, height: 36 }}
            />
          </PlayerBoosterButton>
        )}
        {showRedCardAppeal && (
          <PlayerBoosterButton onPress={() => redCardAppealActions.setIsAppealModalVisible(true)}>
            <Image
              source={require('../../../assets/redcard.png')}
              style={{ height: 30, width: 30, resizeMode: 'contain' }}
            />
          </PlayerBoosterButton>
        )}
      </PlayerImageContainer>
      <PlayerStatsContainer>
        <DetailText>{`Age: ${player.age}`}</DetailText>
        <DetailText>{`Value: ${formattedValue}`}</DetailText>
        <DetailText>{`Position: ${bestPosition.join(', ')}`}</DetailText>
        {team && currentEnergy && <DetailText>{`Energy: ${currentEnergy}%`}</DetailText>}
        {team && team.nextFixture && energyByNextMatch !== undefined && (
          <DetailText>{`Energy by Next Match: ${energyByNextMatch}%`}</DetailText>
        )}
      </PlayerStatsContainer>
    </InfoContainer>
  );
};
