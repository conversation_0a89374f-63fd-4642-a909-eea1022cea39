import { useRouter } from 'expo-router';
import React from 'react';
import { ScrollView, TouchableOpacity } from 'react-native';
import styled, { DefaultTheme } from 'styled-components/native';
import { PlayerMatchHistory as PlayerMatchHistoryType } from '../../models/player';
import { ChevronIcon } from '../PlayerRow/SharedComponents';
import { Text } from '../Text';

interface StyledProps {
  theme: DefaultTheme;
}

interface PlayerMatchHistoryProps {
  matchHistory: PlayerMatchHistoryType[];
}

const Container = styled.View`
  flex: 1;
`;

const MatchRow = styled(TouchableOpacity)`
  background-color: ${(props: StyledProps) => props.theme.colors.surface};
  padding: 16px;
  margin-bottom: 8px;
  border-radius: 8px;
  flex-direction: row;
  align-items: center;
  justify-content: space-between;
`;

const MatchInfo = styled.View`
  flex: 1;
`;

const MatchTitle = styled(Text)`
  font-size: 14px;
  color: ${(props: StyledProps) => props.theme.colors.text.primary};
  font-family: ${(props: StyledProps) => props.theme.typography.bold};
  margin-bottom: 4px;
`;

const StatsContainer = styled.View`
  flex-direction: row;
  flex-wrap: wrap;
  gap: 6px;
  margin-top: 4px;
`;

const StatItem = styled.View`
  flex-direction: row;
  align-items: center;
  margin-right: 12px;
`;

const StatLabel = styled(Text)`
  font-size: 11px;
  color: ${(props: StyledProps) => props.theme.colors.text.secondary};
  font-family: 'Nunito';
  margin-right: 2px;
`;

const StatValue = styled(Text)`
  font-size: 11px;
  color: ${(props: StyledProps) => props.theme.colors.text.primary};
  font-family: ${(props: StyledProps) => props.theme.typography.bold};
`;

const ChevronIconContainer = styled.View`
  margin-left: 8px;
`;

const NoDataText = styled(Text)`
  font-size: 16px;
  color: ${(props: StyledProps) => props.theme.colors.text.secondary};
  font-family: 'Nunito';
  text-align: center;
  padding: 32px;
`;

export const PlayerMatchHistory: React.FC<PlayerMatchHistoryProps> = ({ matchHistory }) => {
  const router = useRouter();

  // Debug logging
  console.log('PlayerMatchHistory - matchHistory:', matchHistory);
  console.log('PlayerMatchHistory - matchHistory length:', matchHistory?.length || 0);

  const handleMatchPress = (fixtureId: string) => {
    router.push(`/fixture-detail/${fixtureId}`);
  };

  const renderStat = (label: string, value: number) => {
    if (value === 0) return null;

    return (
      <StatItem key={label}>
        <StatLabel>{label}:</StatLabel>
        <StatValue>{value}</StatValue>
      </StatItem>
    );
  };

  const renderMatchItem = (item: PlayerMatchHistoryType) => {
    const hasStats =
      item.goals > 0 ||
      item.yellowCards > 0 ||
      item.redCards > 0 ||
      item.tackles > 0 ||
      item.saves > 0 ||
      item.shots > 0 ||
      item.shotsOnTarget > 0 ||
      item.passesCompleted > 0 ||
      item.fouls > 0;

    return (
      <MatchRow key={item.fixtureId} onPress={() => handleMatchPress(item.fixtureId)}>
        <MatchInfo>
          <MatchTitle>
            {item.homeTeamName} {item.homeTeamScore} - {item.awayTeamScore} {item.awayTeamName}
          </MatchTitle>

          {hasStats && (
            <StatsContainer>
              {renderStat('Goals', item.goals)}
              {renderStat('Shots', item.shots)}
              {renderStat('Shots on Target', item.shotsOnTarget)}
              {renderStat('Passes', item.passesCompleted)}
              {renderStat('Tackles', item.tackles)}
              {renderStat('Saves', item.saves)}
              {renderStat('Fouls', item.fouls)}
              {renderStat('Yellow Cards', item.yellowCards)}
              {renderStat('Red Cards', item.redCards)}
            </StatsContainer>
          )}

          {!hasStats && (
            <Text style={{ fontSize: 12, color: '#999', fontStyle: 'italic' }}>
              No notable stats this match
            </Text>
          )}
        </MatchInfo>

        <ChevronIconContainer>
          <ChevronIcon />
        </ChevronIconContainer>
      </MatchRow>
    );
  };

  if (!matchHistory || matchHistory.length === 0) {
    return (
      <Container>
        <NoDataText>No match history available for this player this season.</NoDataText>
      </Container>
    );
  }

  return (
    <Container>
      <ScrollView
        showsVerticalScrollIndicator={false}
        contentContainerStyle={{ paddingBottom: 16 }}
      >
        {matchHistory.map((item) => renderMatchItem(item))}
      </ScrollView>
    </Container>
  );
};
