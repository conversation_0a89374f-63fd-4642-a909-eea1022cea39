import React, { useEffect, useState } from 'react';
import styled from 'styled-components/native';
import { Player } from '../../models/player';
import { StyledProps } from '../Common';
import { Text } from '../Text';

interface PlayerStatusProps {
  player: Player;
  isInjured: boolean;
  isSuspended: boolean;
}

const StatusContainer = styled.View`
  background-color: #e3172a;
  padding: 12px;
  border-radius: 8px;
  margin-bottom: 24px;
  align-items: center;
`;

const StatusText = styled(Text)`
  font-size: 16px;
  color: white;
  font-family: ${(props: StyledProps) => props.theme.typography.bold};
`;

export const PlayerStatus: React.FC<PlayerStatusProps> = ({ player, isInjured, isSuspended }) => {
  const [currentTime, setCurrentTime] = useState(Date.now());

  useEffect(() => {
    const interval = setInterval(() => {
      setCurrentTime(Date.now());
    }, 1000);

    return () => clearInterval(interval);
  }, []);

  // Format the injury time remaining
  const formatInjuryTimeRemaining = (timestamp: number) => {
    const timeRemaining = timestamp - currentTime;

    if (timeRemaining <= 0) {
      return 'Injury has expired';
    }

    const dayMs = 1000 * 60 * 60 * 24;
    if (timeRemaining >= dayMs) {
      const days = Math.ceil(timeRemaining / dayMs); // changed from floor to ceil for consistency with modal
      return `Injured for ${days} day${days === 1 ? '' : 's'}`;
    }

    const hours = Math.floor((timeRemaining % dayMs) / (1000 * 60 * 60));
    const minutes = Math.floor((timeRemaining % (1000 * 60 * 60)) / (1000 * 60));

    if (hours > 0) {
      return `Injured for ${hours} hour${hours === 1 ? '' : 's'}`;
    }
    return `Injured for ${Math.max(1, minutes)} minute${minutes === 1 ? '' : 's'}`;
  };

  if (!isInjured && !isSuspended) {
    return null;
  }

  return (
    <>
      {isInjured && (
        <StatusContainer>
          <StatusText>{formatInjuryTimeRemaining(player.injuredUntil!)}</StatusText>
        </StatusContainer>
      )}

      {isSuspended && (
        <StatusContainer>
          <StatusText>{`Suspended for ${player.suspendedForGames} games`}</StatusText>
        </StatusContainer>
      )}
    </>
  );
};
