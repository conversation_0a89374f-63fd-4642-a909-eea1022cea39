import { useRouter } from 'expo-router';
import React, { useCallback, useEffect, useMemo, useRef } from 'react';
import { Animated, Easing, Image, Modal, View } from 'react-native';
import { usePlayerUtils } from '../../context/PlayerContext';
import { Player } from '../../models/player';
import { AD_TYPES, useRewardAd } from '../Ads';
import { RedCardAppealActions, RedCardAppealState } from './hooks/useRedCardAppeal';
import {
  ButtonText,
  DetailText,
  ModalButton,
  ModalButtonContainer,
  ModalContainer,
  ModalContent,
  ModalPill,
  ModalPillText,
  ModalTitle,
  ModalTitleRow,
  TransferButton,
} from './ModalSharedComponents';

interface RedCardAppealModalProps {
  player: Player;
  isSuspended: boolean;
  appealsAvailable: number;
  redCardAppealState: RedCardAppealState;
  redCardAppealActions: RedCardAppealActions;
}

export const RedCardAppealModal: React.FC<RedCardAppealModalProps> = ({
  player,
  isSuspended,
  appealsAvailable,
  redCardAppealState,
  redCardAppealActions,
}) => {
  const { isAdLoaded, showAd } = useRewardAd();
  const { playerActions } = usePlayerUtils();
  const router = useRouter();

  const handleRedCardAppealUse = async () => {
    await redCardAppealActions.useRedCardAppeal(player as Player, (_title, _message) => {
      playerActions.setShowAlert(true);
    });
  };

  const rewardEarned = useCallback(() => {
    redCardAppealActions.incrementRedCardAppealCount();
  }, [redCardAppealActions]);

  const result = redCardAppealState.lastResult;

  // Animation for success pulse
  const successScale = useRef(new Animated.Value(1));
  useEffect(() => {
    if (result) {
      successScale.current.setValue(0.92);
      Animated.sequence([
        Animated.timing(successScale.current, {
          toValue: 1.05,
          duration: 220,
          easing: Easing.out(Easing.quad),
          useNativeDriver: true,
        }),
        Animated.timing(successScale.current, {
          toValue: 1,
          duration: 180,
          easing: Easing.inOut(Easing.quad),
          useNativeDriver: true,
        }),
      ]).start();
    }
  }, [result]);

  // Auto close after success
  useEffect(() => {
    if (result) {
      const t = setTimeout(() => {
        redCardAppealActions.setIsAppealModalVisible(false);
      }, 1600);
      return () => clearTimeout(t);
    }
  }, [result, redCardAppealActions]);

  const { appealReason, previewLines, actionLabel } = useMemo(() => {
    const games = player.suspendedForGames;
    if (games <= 0) {
      return {
        appealReason: `${player.firstName} ${player.surname} isn't suspended`,
        previewLines: [],
        actionLabel: 'No suspension',
      };
    }
    const after = Math.max(0, games - 1);
    return {
      appealReason: `Use an appeal to reduce ${player.firstName} ${player.surname}'s suspension by 1 game`,
      previewLines: [
        `Suspension: ${games} game${games === 1 ? '' : 's'} → ${after} game${after === 1 ? '' : 's'}${after === 0 ? ' (cleared)' : ''}`,
      ],
      actionLabel: after === 0 ? 'Clear suspension now' : 'Reduce by 1 game',
    };
  }, [player.suspendedForGames, player.firstName, player.surname]);

  const resultLines = useMemo(() => {
    if (!result) return [] as string[];
    const lines: string[] = [];
    lines.push(
      `Suspension: ${result.gamesBefore} game${result.gamesBefore === 1 ? '' : 's'} → ${result.gamesAfter} game${result.gamesAfter === 1 ? '' : 's'}${result.gamesAfter === 0 ? ' (cleared)' : ''}`
    );
    return lines;
  }, [result]);

  const closeModal = () => {
    redCardAppealActions.setIsAppealModalVisible(false);
  };

  const isUseDisabled =
    appealsAvailable === 0 || redCardAppealState.isAppealLoading || player.suspendedForGames <= 0;

  return (
    <Modal
      visible={redCardAppealState.isAppealModalVisible}
      transparent
      animationType="fade"
      onRequestClose={closeModal}
    >
      <ModalContainer>
        <ModalContent>
          <ModalTitleRow>
            <ModalTitle>Red Card Appeal</ModalTitle>
            <ModalPill>
              <Image
                source={require('../../../assets/redcard.png')}
                style={{ resizeMode: 'contain', width: 24, height: 24 }}
              />
              <ModalPillText>{appealsAvailable}</ModalPillText>
            </ModalPill>
          </ModalTitleRow>

          {!result && (
            <>
              <DetailText>{appealReason}</DetailText>
              {previewLines.length > 0 && (
                <View style={{ marginTop: 8, marginBottom: 4 }}>
                  {previewLines.map((l) => (
                    <DetailText key={l} style={{ fontWeight: '600' }}>
                      {l}
                    </DetailText>
                  ))}
                </View>
              )}
            </>
          )}

          {result && (
            <Animated.View
              style={{
                marginTop: 4,
                marginBottom: 12,
                transform: [{ scale: successScale.current }],
              }}
            >
              <DetailText style={{ fontWeight: '700', color: '#2e7d32' }}>
                {result.cleared ? '✅ Suspension cleared!' : '👍 Suspension reduced'}
              </DetailText>
              {resultLines.map((l) => (
                <DetailText key={l} style={{ fontWeight: '600' }}>
                  {l}
                </DetailText>
              ))}
            </Animated.View>
          )}

          <ModalButtonContainer>
            {!result && (
              <ModalButton
                disabled={isUseDisabled}
                onPress={handleRedCardAppealUse}
                style={{ opacity: isUseDisabled ? 0.5 : 1 }}
                testID="red-card-appeal-apply"
              >
                <ButtonText>
                  {redCardAppealState.isAppealLoading ? 'Appealing…' : actionLabel}
                </ButtonText>
              </ModalButton>
            )}
            {!result && (
              <ModalButton
                disabled={!isAdLoaded(AD_TYPES.RED_CARD_APPEAL)}
                onPress={() => showAd(AD_TYPES.RED_CARD_APPEAL, rewardEarned)}
                style={{ opacity: !isAdLoaded(AD_TYPES.RED_CARD_APPEAL) ? 0.5 : 1 }}
              >
                <ButtonText>Watch ad</ButtonText>
              </ModalButton>
            )}
            {!result && (
              <ModalButton
                onPress={() => {
                  router.push('/club-shop');
                  closeModal();
                }}
              >
                <ButtonText>Buy more</ButtonText>
              </ModalButton>
            )}
          </ModalButtonContainer>
          {!result && (
            <TransferButton onPress={closeModal} style={{ marginTop: 16 }}>
              <ButtonText style={{ color: '#333' }}>Close</ButtonText>
            </TransferButton>
          )}
        </ModalContent>
      </ModalContainer>
    </Modal>
  );
};
