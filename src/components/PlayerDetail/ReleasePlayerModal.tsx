import React from 'react';
import { Modal } from 'react-native';
import { usePlayerUtils } from '../../context/PlayerContext';
import { Player } from '../../models/player';
import {
  ButtonRowContainer,
  ButtonText,
  CancelButton,
  InputContainer,
  Label,
  ModalContainer,
  ModalContent,
  ModalTitle,
  ReleaseButton,
  WarningLabel,
} from './ModalSharedComponents';

interface ReleasePlayerModalProps {
  player: Player;
}

export const ReleasePlayerModal: React.FC<ReleasePlayerModalProps> = ({ player }) => {
  const { playerActionsState, playerActions } = usePlayerUtils();
  const handleReleasePlayer = async () => {
    await playerActions.handleReleasePlayer(player, () => {
      playerActions.setIsReleaseModalVisible(false);
    });
  };
  const isReleasing = playerActionsState.isReleasing;

  return (
    <Modal
      visible={playerActionsState.isReleaseModalVisible}
      transparent
      animationType="fade"
      onRequestClose={() => playerActions.setIsReleaseModalVisible(false)}
    >
      <ModalContainer>
        <ModalContent>
          <ModalTitle>Release Player</ModalTitle>

          <InputContainer>
            <Label>
              Are you sure you want to release {player.firstName} {player.surname}?
            </Label>
            <WarningLabel>
              This action cannot be undone. The player will be removed from your team permanently.
            </WarningLabel>
          </InputContainer>

          <ButtonRowContainer>
            <CancelButton
              onPress={() => playerActions.setIsReleaseModalVisible(false)}
              disabled={isReleasing}
            >
              <ButtonText>Cancel</ButtonText>
            </CancelButton>
            <ReleaseButton onPress={handleReleasePlayer} disabled={isReleasing}>
              <ButtonText>{isReleasing ? 'Releasing...' : 'Release Player'}</ButtonText>
            </ReleaseButton>
          </ButtonRowContainer>
        </ModalContent>
      </ModalContainer>
    </Modal>
  );
};
