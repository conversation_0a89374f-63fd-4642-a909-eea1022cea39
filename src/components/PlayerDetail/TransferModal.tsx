import React from 'react';
import { Modal, TextInput } from 'react-native';
import styled, { DefaultTheme } from 'styled-components/native';
import { usePlayerUtils } from '../../context/PlayerContext';
import { ActiveTransfer } from '../../hooks/useMyBidsPlayers';
import { TransferListPlayer } from '../../models/player';
import { formatCurrencyShort } from '../../utils/utils';
import { Text } from '../Text';
import { useAuctionTimer } from './hooks/useAuctionTimer';
import { PlayerStatus } from './hooks/usePlayerStatus';
import {
  ButtonColumnContainer,
  ButtonText,
  CancelButton,
  InputContainer,
  Label,
  ModalContainer,
  ModalContent,
  ModalTitle,
  TransferButton,
  WarningLabel,
} from './ModalSharedComponents';

interface StyledProps {
  theme: DefaultTheme;
}

interface TransferModalProps {
  player: TransferListPlayer;
  playerStatus: PlayerStatus;
  isAuctionPlayer: boolean;
  formattedValue: string;
  onClose: () => void;
  onNewTransfer?: (transfer: ActiveTransfer) => void;
}

const Input = styled(TextInput)`
  background-color: ${(props: StyledProps) => props.theme.colors.surface};
  padding: 12px;
  border-radius: 8px;
  border: 2px solid ${(props: StyledProps) => props.theme.colors.primary};
  font-family: ${(props: StyledProps) => props.theme.typography.regular};
  font-size: 16px;
  color: ${(props: StyledProps) => props.theme.colors.text.primary};
`;

const BoldText = styled.Text`
  font-family: ${(props: StyledProps) => props.theme.typography.bold};
`;

export const TransferModal: React.FC<TransferModalProps> = ({
  player,
  playerStatus,
  isAuctionPlayer,
  formattedValue,
  onClose,
  onNewTransfer,
}) => {
  const currentBid = player.bidHistory?.length
    ? Math.max(...(player.bidHistory || []).map((bid) => bid.maximumBid))
    : player.auctionStartPrice || 0;

  const formattedCurrentBid = currentBid ? formatCurrencyShort(currentBid) : 'No bids';
  const { playerActionsState, playerActions } = usePlayerUtils();

  const { timeRemaining } = useAuctionTimer(player);

  /*useEffect(() => {
    if (!isAuctionPlayer && playerActionsState.isOfferModalVisible) {
      playerActions.setOfferAmount(Math.ceil(player.value));
    }
  }, [
    isAuctionPlayer,
    player.value,
    playerActionsState.isOfferModalVisible,
    playerActions,
    formattedValue,
  ]);*/

  const handleSubmitOffer = async () => {
    await playerActions.handleSubmitOffer(
      player,
      playerStatus.isAuctionPlayer,
      currentBid,
      onNewTransfer
    );
  };

  const roundDownToPowerOf10 = (value: number): number => {
    if (value <= 0) return 0;
    const magnitude = Math.floor(Math.log10(value));
    return Math.floor(value / Math.pow(10, magnitude)) * Math.pow(10, magnitude);
  };
  const increment = roundDownToPowerOf10(playerActionsState.maxBid || currentBid) * 0.1;

  return (
    <Modal visible={playerActionsState.isOfferModalVisible} transparent onRequestClose={onClose}>
      <ModalContainer>
        <ModalContent>
          <ModalTitle>{player.teamId === '' ? 'Submit Bid' : 'Submit Transfer Offer'}</ModalTitle>

          <InputContainer>
            <Label>
              <Text>
                <BoldText>Player:</BoldText> {player.firstName} {player.surname}
              </Text>
            </Label>

            {isAuctionPlayer ? (
              <>
                <Label>
                  <Text>
                    <BoldText>Player Value:</BoldText> {formattedValue}
                  </Text>
                </Label>
                <Label>
                  <Text>
                    <BoldText>Current Bid:</BoldText>{' '}
                    {playerActionsState.maxBid
                      ? formatCurrencyShort(playerActionsState.maxBid)
                      : formattedCurrentBid}
                  </Text>
                </Label>
                {playerActionsState.maxBid && (
                  <WarningLabel
                    style={{ color: playerActionsState.isHighestBidder ? '#2E7D32' : '#e3172a' }}
                  >
                    {playerActionsState.isHighestBidder
                      ? '✓ You are the highest bidder!'
                      : '✗ You are not the highest bidder'}
                  </WarningLabel>
                )}
                <Label>
                  <Text>
                    <BoldText>Time Remaining:</BoldText> {timeRemaining}
                  </Text>
                </Label>
                <Label>
                  <Text>
                    <BoldText>Minimum Bid:</BoldText>{' '}
                    {formatCurrencyShort(
                      Math.ceil((playerActionsState.maxBid || currentBid) + increment)
                    )}
                  </Text>
                </Label>
                <Label>
                  <Text>
                    <BoldText>Your Bid:</BoldText>
                  </Text>
                </Label>
              </>
            ) : (
              <>
                <Label>
                  <Text>
                    <BoldText>Current Value:</BoldText> {formattedValue}
                  </Text>
                </Label>
                <Label>
                  <Text>
                    <BoldText>Offer Amount:</BoldText>
                  </Text>
                </Label>
              </>
            )}

            <Input
              value={playerActionsState.offerAmount.toString()}
              onChangeText={(val) => playerActions.setOfferAmount(Math.ceil(Number(val)))}
              placeholder="Enter amount"
              keyboardType="numeric"
            />
          </InputContainer>

          <ButtonColumnContainer>
            <CancelButton onPress={onClose}>
              <ButtonText>Cancel</ButtonText>
            </CancelButton>
            <TransferButton onPress={handleSubmitOffer} disabled={playerActionsState.isSubmitting}>
              <ButtonText>
                {playerActionsState.isSubmitting
                  ? 'Submitting...'
                  : isAuctionPlayer
                    ? 'Submit Bid'
                    : 'Submit Offer'}
              </ButtonText>
            </TransferButton>
          </ButtonColumnContainer>
        </ModalContent>
      </ModalContainer>
    </Modal>
  );
};
