import React, { useEffect, useState } from 'react';
import { TransferListPlayer } from '../../models/player';

import { usePlayerUtils } from '../../context/PlayerContext';
import { useCachedMyActiveTransfers } from '../../hooks/useCachedData';
import { ActiveTransfer } from '../../hooks/useMyBidsPlayers';
import { ActionButton, ActionButtonText } from '../ActionButton';
import { PlayerStatus } from './hooks/usePlayerStatus';

interface TransferSectionProps {
  player: TransferListPlayer;
  playerStatus: PlayerStatus;
  onNegotiate: (transfer: ActiveTransfer) => void;
}

export const TransferSection: React.FC<TransferSectionProps> = ({
  player,
  playerStatus,
  onNegotiate,
}) => {
  const [alreadyMadeOffer, setAlreadyMadeOffer] = useState(false);
  const [transfer, setTransfer] = useState<ActiveTransfer | null>(null);
  const { playerActions } = usePlayerUtils();
  const { data } = useCachedMyActiveTransfers(player.gameworldId);

  useEffect(() => {
    const matchingTransfer = data.transfers.find((t) => t.player.playerId === player.playerId);
    setAlreadyMadeOffer(!!matchingTransfer);
    setTransfer(matchingTransfer || null);
  }, [data.transfers, player.playerId]);

  const handleTransferPress = () => {
    playerActions.prefillOfferAmount(
      player as TransferListPlayer,
      playerStatus.isAuctionPlayer,
      player.value
    );
    playerActions.setIsOfferModalVisible(true);
  };

  return (
    <>
      {/* Only show Bid button if auction is not completed and no offer has been made */}
      {!alreadyMadeOffer && (
        <ActionButton variant="primary" onPress={handleTransferPress}>
          <ActionButtonText>Submit Offer</ActionButtonText>
        </ActionButton>
      )}

      {/* Show Amend Offer button only if an offer has already been made, not auction, not completed, and transfer exists */}
      {alreadyMadeOffer && transfer && (
        <ActionButton
          variant="primary"
          onPress={() => {
            onNegotiate(transfer);
          }}
        >
          <ActionButtonText>Amend Offer</ActionButtonText>
        </ActionButton>
      )}
    </>
  );
};
