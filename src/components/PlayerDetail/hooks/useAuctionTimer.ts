import { useEffect, useState } from 'react';

import { TransferListPlayer } from '../../../models/player';

export interface AuctionTimerState {
  timeRemaining: string;
  auctionCompleted: boolean;
}

export const useAuctionTimer = (transferListPlayer?: TransferListPlayer): AuctionTimerState => {
  const [timeRemaining, setTimeRemaining] = useState('');
  const [auctionCompleted, setAuctionCompleted] = useState(false);

  useEffect(() => {
    if (!transferListPlayer?.auctionEndTime) return;

    const updateTimeRemaining = () => {
      const currentTime = Date.now();
      const endTime = transferListPlayer.auctionEndTime;
      const timeLeft = endTime - currentTime;

      if (timeLeft <= 0) {
        setTimeRemaining('Please wait. Completing auction');
        setAuctionCompleted(true);
        return;
      } else {
        setAuctionCompleted(false);
      }

      // Format time remaining
      const days = Math.floor(timeLeft / (1000 * 60 * 60 * 24));
      const hours = Math.floor((timeLeft % (1000 * 60 * 60 * 24)) / (1000 * 60 * 60));
      const minutes = Math.floor((timeLeft % (1000 * 60 * 60)) / (1000 * 60));
      const seconds = Math.floor((timeLeft % (1000 * 60)) / 1000);

      if (days > 0) {
        setTimeRemaining(`${days}d ${hours}h remaining`);
      } else if (hours > 0) {
        setTimeRemaining(`${hours}h ${minutes}m remaining`);
      } else if (minutes > 0) {
        setTimeRemaining(`${minutes}m ${seconds}s remaining`);
      } else {
        setTimeRemaining(`${seconds}s remaining`);
      }
    };

    // Update immediately
    updateTimeRemaining();

    // Then update every second
    const interval = setInterval(updateTimeRemaining, 1000);
    return () => clearInterval(interval);
  }, [transferListPlayer?.auctionEndTime]);

  return { timeRemaining, auctionCompleted };
};
