import { useState } from 'react';
import { callApi } from '../../../api/client';
import { useDataCache } from '../../../context/DataCacheContext';
import { useManager } from '../../../context/ManagerContext';
import { Player } from '../../../models/player';

export interface MagicSpongeState {
  isSpongeModalVisible: boolean;
  isSpongeLoading: boolean;
  lastResult?: MagicSpongeResult;
}

export interface MagicSpongeResult {
  energyBefore: number;
  energyAfter: number;
  injuryMsBefore?: number; // ms remaining before
  injuryMsAfter?: number; // ms remaining after
  healed?: boolean; // injury fully healed
}

export interface MagicSpongeActions {
  setIsSpongeModalVisible: (visible: boolean) => void;
  useMagicSponge: (
    player: Player,
    onError: (title: string, message: string) => void
  ) => Promise<void>;
  incrementMagicSpongeCount: () => void;
  resetMagicSpongeResult: () => void;
}

export const useMagicSponge = (): [MagicSpongeState, MagicSpongeActions] => {
  const { manager, updateManager } = useManager();
  const { updatePlayer } = useDataCache();
  const [isSpongeModalVisible, setIsSpongeModalVisible] = useState(false);
  const [isSpongeLoading, setIsSpongeLoading] = useState(false);
  const [lastResult, setLastResult] = useState<MagicSpongeResult | undefined>(undefined);

  const useMagicSpongeApi = async (gameworldId: string, playerId: string) => {
    return callApi<Player>(`/${gameworldId}/players/${playerId}/magic-sponge`, {
      method: 'POST',
      body: JSON.stringify({}),
    });
  };

  const handleUseMagicSponge = async (
    player: Player,
    onError: (title: string, message: string) => void
  ) => {
    setIsSpongeLoading(true);
    const nowBefore = Date.now();
    const energyBefore = player.energy;
    const injuryMsBefore =
      player.injuredUntil && player.injuredUntil > nowBefore
        ? player.injuredUntil - nowBefore
        : undefined;
    try {
      const updatedPlayer = await useMagicSpongeApi(player.gameworldId, player.playerId);
      if (updatedPlayer) {
        // Update the player in the cache
        updatePlayer({
          playerId: player.playerId,
          energy: updatedPlayer.energy,
          injuredUntil: updatedPlayer.injuredUntil,
        });

        // Update manager's magic sponges count
        if (manager) {
          updateManager({
            magicSponges: Math.max(0, manager.magicSponges - 1),
          });
        }

        // Also update the local player object for an immediate UI update
        player.energy = updatedPlayer.energy;
        player.injuredUntil = updatedPlayer.injuredUntil;

        const nowAfter = Date.now();
        const injuryMsAfter =
          updatedPlayer.injuredUntil && updatedPlayer.injuredUntil > nowAfter
            ? updatedPlayer.injuredUntil - nowAfter
            : undefined;
        const healed =
          injuryMsBefore !== undefined && (injuryMsAfter === undefined || injuryMsAfter <= 0);

        setLastResult({
          energyBefore,
          energyAfter: updatedPlayer.energy,
          injuryMsBefore,
          injuryMsAfter,
          healed,
        });
        // We now leave the modal open so the UI can display a success state; caller will close it.
      }
    } catch {
      onError('Error', 'Failed to use magic sponge.');
    } finally {
      setIsSpongeLoading(false);
    }
  };

  const incrementMagicSpongeCount = () => {
    if (manager) {
      updateManager({
        magicSponges: manager.magicSponges + 1,
      });
    }
  };

  const resetMagicSpongeResult = () => setLastResult(undefined);

  const setIsSpongeModalVisibleWrapped = (visible: boolean) => {
    if (visible) {
      // Clear previous result when opening fresh to avoid stale success state
      setLastResult(undefined);
    }
    setIsSpongeModalVisible(visible);
  };

  const state: MagicSpongeState = {
    isSpongeModalVisible,
    isSpongeLoading,
    lastResult,
  };

  const actions: MagicSpongeActions = {
    setIsSpongeModalVisible: setIsSpongeModalVisibleWrapped,
    useMagicSponge: handleUseMagicSponge,
    incrementMagicSpongeCount,
    resetMagicSpongeResult,
  };

  return [state, actions];
};
