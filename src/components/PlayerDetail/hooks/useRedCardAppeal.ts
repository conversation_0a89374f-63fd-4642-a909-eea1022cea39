import { useState } from 'react';
import { callApi } from '../../../api/client';
import { useDataCache } from '../../../context/DataCacheContext';
import { useManager } from '../../../context/ManagerContext';
import { Player } from '../../../models/player';

export interface RedCardAppealState {
  isAppealModalVisible: boolean;
  isAppealLoading: boolean;
  lastResult?: RedCardAppealResult;
}

export interface RedCardAppealResult {
  gamesBefore: number;
  gamesAfter: number;
  cleared: boolean; // suspension fully cleared
}

export interface RedCardAppealActions {
  setIsAppealModalVisible: (visible: boolean) => void;
  useRedCardAppeal: (
    player: Player,
    onError: (title: string, message: string) => void
  ) => Promise<void>;
  incrementRedCardAppealCount: () => void;
  resetRedCardAppealResult: () => void;
}

export const useRedCardAppeal = (): [RedCardAppealState, RedCardAppealActions] => {
  const { manager, updateManager } = useManager();
  const { updatePlayer } = useDataCache();
  const [isAppealModalVisible, setIsAppealModalVisible] = useState(false);
  const [isAppealLoading, setIsAppealLoading] = useState(false);
  const [lastResult, setLastResult] = useState<RedCardAppealResult | undefined>(undefined);

  const useRedCardAppealApi = async (gameworldId: string, playerId: string) => {
    return callApi<Player>(`/${gameworldId}/players/${playerId}/red-card-appeal`, {
      method: 'POST',
      body: JSON.stringify({}),
    });
  };

  const handleUseRedCardAppeal = async (
    player: Player,
    onError: (title: string, message: string) => void
  ) => {
    setIsAppealLoading(true);
    const gamesBefore = player.suspendedForGames;
    try {
      const updatedPlayer = await useRedCardAppealApi(player.gameworldId, player.playerId);
      if (updatedPlayer) {
        // Update the player in the cache
        updatePlayer({
          playerId: player.playerId,
          suspendedForGames: updatedPlayer.suspendedForGames,
        });

        // Update manager's card appeals count
        if (manager) {
          updateManager({
            cardAppeals: Math.max(0, manager.cardAppeals - 1),
          });
        }

        // Also update the local player object for an immediate UI update
        player.suspendedForGames = updatedPlayer.suspendedForGames;
        const gamesAfter = updatedPlayer.suspendedForGames;
        setLastResult({
          gamesBefore,
          gamesAfter,
          cleared: gamesAfter <= 0,
        });
        // Leave modal open to show success feedback; auto-close handled by modal.
      }
    } catch {
      onError('Error', 'Failed to use red card appeal.');
    } finally {
      setIsAppealLoading(false);
    }
  };

  const incrementRedCardAppealCount = () => {
    if (manager) {
      updateManager({
        cardAppeals: manager.cardAppeals + 1,
      });
    }
  };

  const resetRedCardAppealResult = () => setLastResult(undefined);

  const setIsAppealModalVisibleWrapped = (visible: boolean) => {
    if (visible) {
      setLastResult(undefined);
    }
    setIsAppealModalVisible(visible);
  };

  const state: RedCardAppealState = {
    isAppealModalVisible,
    isAppealLoading,
    lastResult,
  };

  const actions: RedCardAppealActions = {
    setIsAppealModalVisible: setIsAppealModalVisibleWrapped,
    useRedCardAppeal: handleUseRedCardAppeal,
    incrementRedCardAppealCount,
    resetRedCardAppealResult,
  };

  return [state, actions];
};
