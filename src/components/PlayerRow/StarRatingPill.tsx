import { FontAwesome } from '@expo/vector-icons';
import React from 'react';
import styled, { useTheme } from 'styled-components/native';

interface StarRatingPillProps {
  fullStars: number;
  halfStar: boolean;
  emptyStars: number;
  style?: object;
}

const PillContainer = styled.View<{ background: string }>`
  flex-direction: row;
  align-items: center;
  padding: 2px 10px;
  border-radius: 16px;
  background-color: ${(props) => props.background};
  position: absolute;
  top: -10px;
  left: -10px;
  z-index: 2;
  elevation: 2;
  min-width: 48px;
  border-color: ${(props) => props.theme.colors.border};
  border-width: 1px;
`;

const StarRatingPill: React.FC<StarRatingPillProps> = ({
  fullStars,
  halfStar,
  emptyStars,
  style,
}) => {
  const theme = useTheme();
  const background = theme.colors.surface;

  return (
    <PillContainer background={background} style={style}>
      {[...Array(fullStars)].map((_, i) => (
        <FontAwesome key={`full-${i}`} name="star" size={14} color={theme.colors.starPrimary} />
      ))}
      {halfStar && <FontAwesome name="star-half-o" size={14} color={theme.colors.starPrimary} />}
      {[...Array(emptyStars)].map((_, i) => (
        <FontAwesome
          key={`empty-${i}`}
          name="star-o"
          size={14}
          color={theme.colors.starSecondary}
        />
      ))}
    </PillContainer>
  );
};

export default StarRatingPill;
