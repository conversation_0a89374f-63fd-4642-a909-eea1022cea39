import { MaterialIcons } from '@expo/vector-icons';
import React from 'react';
import { TouchableOpacity, View } from 'react-native';
import Animated, { useAnimatedStyle, useSharedValue, withSpring } from 'react-native-reanimated';
import { calculateBestPosition as calculatePlayerBestPosition, Player } from '../../models/player';
import { useTheme } from '../../theme/ThemeContext';
import PlayerInfo from './PlayerInfo';
import {
  Card,
  CardContent,
  DetailButton,
  PillPositionContainer,
  PlayerPosition,
  RatingBadge,
  RatingText,
} from './PlayerRowStyles';
import { ChevronIcon } from './SharedComponents';

interface TapPlayerRowProps {
  player: Player;
  isSelected?: boolean;
  backgroundColor?: string;
  positionFilter?: 'All' | 'Goalkeeper' | 'Defender' | 'Midfielder' | 'Attacker';
  onSelect?: (player: Player, currentScrollPosition?: number) => void;
  isSelectedForSwap?: boolean;
  onPlayerTap?: (playerId: string) => void;
}

// Swap icon component for tap mode
const SwapIcon: React.FC<{ isSelected: boolean; onPress: () => void }> = ({
  isSelected,
  onPress,
}) => {
  const { theme } = useTheme();
  return (
    <TouchableOpacity
      onPress={onPress}
      style={{ width: 60, alignItems: 'center', justifyContent: 'center' }}
    >
      <MaterialIcons
        name="swap-vert"
        size={24}
        color={isSelected ? theme.colors.primary : theme.colors.text.primary}
      />
    </TouchableOpacity>
  );
};

const TapPlayerRow: React.FC<TapPlayerRowProps> = ({
  player,
  isSelected = false,
  backgroundColor,
  positionFilter = 'All',
  onSelect,
  isSelectedForSwap = false,
  onPlayerTap,
}) => {
  const { theme } = useTheme();
  const positions = calculatePlayerBestPosition(player);

  // Animation for selection in tap mode
  const scale = useSharedValue(1);
  const animatedStyle = useAnimatedStyle(() => {
    return {
      transform: [{ scale: scale.value }],
    };
  });

  // Update animation when the selection state changes
  React.useEffect(() => {
    if (isSelectedForSwap) {
      scale.value = withSpring(0.95);
    } else {
      scale.value = withSpring(1);
    }
  }, [isSelectedForSwap, scale]);

  const handlePress = () => {
    if (onSelect) {
      onSelect(player);
    }
  };

  const handleSwapIconPress = () => {
    if (onPlayerTap) {
      onPlayerTap(player.playerId);
    }
  };

  // Determine background color based on injury and suspension status
  const isInjured = player.injuredUntil && player.injuredUntil > Date.now();
  const isSuspended = player.suspendedForGames > 0;
  let cardBackgroundColor = backgroundColor;
  if (isInjured || isSuspended) {
    cardBackgroundColor = '#e3172a'; // Light red for injured or suspended
  }

  return (
    <Animated.View style={animatedStyle}>
      <Card
        isSelected={isSelected || isSelectedForSwap}
        backgroundColor={cardBackgroundColor}
        isInSection={true}
      >
        <PillPositionContainer>
          <PlayerPosition>{positions.join(' | ') || 'N/A'}</PlayerPosition>
        </PillPositionContainer>
        {player.averageMatchRating && (
          <RatingBadge>
            <MaterialIcons name="star" size={14} color="#FFD700" />
            <RatingText>{player.averageMatchRating.toFixed(1)}</RatingText>
          </RatingBadge>
        )}
        <CardContent>
          <View style={{ flexDirection: 'row', alignItems: 'center' }}>
            <SwapIcon isSelected={isSelectedForSwap} onPress={handleSwapIconPress} />
            <PlayerInfo player={player} positionFilter={positionFilter} showImages={true} />
          </View>
        </CardContent>

        {/* Detail button on the right side */}
        {onSelect && (
          <DetailButton onPress={handlePress}>
            <ChevronIcon />
          </DetailButton>
        )}
      </Card>
    </Animated.View>
  );
};

export default TapPlayerRow;
