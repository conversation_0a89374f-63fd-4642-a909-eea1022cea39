import React from 'react';
import { AppState, FlatList, ImageSourcePropType } from 'react-native';
import styled from 'styled-components/native';
import magicSpongeImg from '../../../assets/magicSponge.png';
import largeCashImg from '../../../assets/rewards/large_cash.png';
import redCardAppealImg from '../../../assets/rewards/redCardAppeal.png';
import smallCashImg from '../../../assets/rewards/small_cash.png';
import trainBoostImg from '../../../assets/rewards/trainingBoost.png';
import { callApi } from '../../api/client';
import { useManager } from '../../context/ManagerContext';
import { Manager } from '../../models/manager';
import { Reward, RewardsResponse, RewardType } from '../../models/rewards';
import { Team } from '../../models/team';
import { logger } from '../../utils/logger';
import { Text } from '../Text';

const Overlay = styled.View`
  flex: 1;
  justify-content: center;
  align-items: center;
  background-color: rgba(0, 0, 0, 0.5);
`;

const Panel = styled.View`
  flex: 1;
  width: 100%;
  background-color: ${(p) => p.theme.colors.surface};
  border-radius: 0;
  padding-top: 48px;
  align-items: center;
  justify-content: flex-start;
`;

const Heading = styled(Text)`
  font-size: 22px;
  margin-bottom: 8px;
`;

const StreakText = styled(Text)`
  margin-bottom: 16px;
`;

const RewardsList = styled(FlatList as unknown as React.ComponentType<any>).attrs({
  numColumns: 2,
  showsVerticalScrollIndicator: false,
  contentContainerStyle: {
    width: '100%',
    paddingHorizontal: 8,
    paddingBottom: 16,
  },
  columnWrapperStyle: {
    justifyContent: 'space-between',
    width: '100%',
  },
})`
  margin-bottom: 16px;
  width: 100%;
`;

const RewardCard = styled.View<{ isActive: boolean; isDay7: boolean }>`
  margin-top: 8px;
  margin-bottom: 8px;
  padding: ${(p) => (p.isDay7 ? 20 : 14)}px;
  border-radius: 14px;
  background-color: ${(p) => p.theme.colors.surface};
  border-width: ${(p) => (p.isActive ? 2 : 1)}px;
  border-color: ${(p) => (p.isActive ? p.theme.colors.primary : p.theme.colors.border)};
  align-items: center;
  width: ${(p) => (p.isDay7 ? '98%' : '48%')};
  align-self: ${(p) => (p.isDay7 ? 'center' : 'auto')};
  min-height: ${(p) => (p.isDay7 ? 130 : 110)}px;
  justify-content: center;
  position: relative;
  elevation: ${(p) => (p.isDay7 ? 4 : 2)};
`;

const RewardsRow = styled.View<{ isDay7: boolean }>`
  width: 100%;
  flex-direction: row;
  align-items: center;
  justify-content: center;
  margin-top: ${(p) => (p.isDay7 ? 6 : 4)}px;
  margin-bottom: ${(p) => (p.isDay7 ? 6 : 4)}px;
`;

const TokenContainer = styled.View<{ isDay7: boolean }>`
  align-items: center;
  justify-content: center;
  margin-left: 6px;
  margin-right: 6px;
  width: ${(p) => (p.isDay7 ? 84 : 64)}px;
  height: ${(p) => (p.isDay7 ? 84 : 64)}px;
  position: relative;
`;

const TokenImage = styled.Image.attrs({
  resizeMode: 'contain',
})`
  width: 100%;
  height: 100%;
`;

const CountText = styled(Text)<{ isDay7: boolean }>`
  position: absolute;
  right: 4px;
  bottom: 2px;
  font-weight: bold;
  font-size: ${(p) => (p.isDay7 ? 20 : 16)}px;
`;

const RewardValue = styled(Text)<{ isDay7: boolean }>`
  font-size: ${(p) => (p.isDay7 ? 18 : 14)}px;
  margin-bottom: 2px;
`;

const DayBadge = styled.View<{ isActive: boolean; isDay7: boolean }>`
  position: absolute;
  top: 8px;
  left: 8px;
  padding-top: 4px;
  padding-bottom: 4px;
  padding-left: 8px;
  padding-right: 8px;
  border-radius: 999px;
  background-color: ${(p) => (p.isActive ? p.theme.colors.primary : p.theme.colors.background)};
  border-width: 1px;
  border-color: ${(p) => p.theme.colors.border};
`;

const DayBadgeText = styled(Text)<{ isActive: boolean; isDay7: boolean }>`
  font-size: ${(p) => (p.isDay7 ? 14 : 12)}px;
  color: ${(p) => (p.isActive ? p.theme.colors.text.primary : p.theme.colors.text.secondary)};
`;

const CollectButton = styled.TouchableOpacity`
  background-color: ${(p) => p.theme.colors.button.success};
  border-radius: 30px;
  padding: 15px 48px;
  margin-top: 8px;
  margin-bottom: 32px;
`;

const CollectText = styled(Text)`
  color: ${(p) => p.theme.colors.text.primary};
  font-size: 18px;
`;

const StyledModal = styled.Modal``;
const testMode = false;
const DailyRewardsModal: React.FC = () => {
  const { loading, manager, team, updateManager, updateTeam } = useManager();

  const [rewards, setRewards] = React.useState<Reward[][]>([]);
  const [loginStreak, setLoginStreak] = React.useState<number>(0);
  const [rewardDay, setRewardDay] = React.useState<number>(0);
  const [isVisible, setIsVisible] = React.useState(false);
  const hasCheckedOnce = React.useRef(false);

  const checkRewards = React.useCallback(async () => {
    try {
      let res: RewardsResponse & { status: number };

      if (testMode) {
        res = {
          status: 200,
          rewards: [
            [{ type: RewardType.SCOUT_TOKEN, value: 1 }],
            [
              {
                type: RewardType.MONEY,
                value: 10000,
              },
              {
                type: RewardType.SCOUT_TOKEN,
                value: 1,
              },
            ],
            [
              {
                type: RewardType.MAGIC_SPONGE,
                value: 1,
              },
              {
                type: RewardType.SCOUT_TOKEN,
                value: 1,
              },
            ],
            [
              {
                type: RewardType.RED_CARD_APPEAL,
                value: 1,
              },
              {
                type: RewardType.SCOUT_TOKEN,
                value: 1,
              },
            ],
            [
              {
                type: RewardType.MAGIC_SPONGE,
                value: 1,
              },
              {
                type: RewardType.SCOUT_TOKEN,
                value: 1,
              },
            ],
            [
              {
                type: RewardType.TRAINING_BOOST,
                value: 1,
              },
              {
                type: RewardType.SCOUT_TOKEN,
                value: 1,
              },
            ],
            [
              {
                type: RewardType.MONEY,
                value: 100000,
              },
              {
                type: RewardType.SCOUT_TOKEN,
                value: 1,
              },
            ],
          ],
          loginStreak: 5,
          day: 6,
        };
      } else {
        res = await callApi<RewardsResponse>('/manager/rewards');
      }
      logger.debug('rewards response', res);
      if (res.status !== 202) {
        setRewards(res.rewards || []);
        setLoginStreak(res.loginStreak || 0);
        setRewardDay(res.day || 0);
        setIsVisible(true);
      }
    } catch (error) {
      logger.error('Failed to fetch rewards', error);
    }
  }, []);

  React.useEffect(() => {
    if (!loading && !hasCheckedOnce.current) {
      hasCheckedOnce.current = true;
      checkRewards();
    }
  }, [loading, checkRewards]);

  React.useEffect(() => {
    const sub = AppState.addEventListener('change', (state) => {
      if (state === 'active') {
        checkRewards();
      }
    });
    return () => sub.remove();
  }, [checkRewards]);

  if (!isVisible) return null;

  const tokenImg: ImageSourcePropType = require('../../../assets/scoutToken.png');

  const handleCollect = () => {
    try {
      const todaysRewards = rewards[rewardDay] || [];
      if ((!manager && !team) || todaysRewards.length === 0) {
        setIsVisible(false);
        return;
      }

      // Aggregate updates for manager and team to minimize dispatches
      const managerUpdates: Partial<Manager> = {} as any;
      const teamUpdates: Partial<Team> = {} as any;

      todaysRewards.forEach((reward) => {
        switch (reward.type) {
          case RewardType.MONEY:
            if (team) {
              teamUpdates.balance = (teamUpdates.balance ?? team.balance ?? 0) + reward.value;
            }
            break;
          case RewardType.SCOUT_TOKEN:
            if (manager) {
              managerUpdates.scoutTokens =
                (managerUpdates.scoutTokens ?? manager.scoutTokens ?? 0) + reward.value;
            }
            break;
          case RewardType.MAGIC_SPONGE:
            if (manager) {
              managerUpdates.magicSponges =
                (managerUpdates.magicSponges ?? manager.magicSponges ?? 0) + reward.value;
            }
            break;
          case RewardType.RED_CARD_APPEAL:
            if (manager) {
              managerUpdates.cardAppeals =
                (managerUpdates.cardAppeals ?? manager.cardAppeals ?? 0) + reward.value;
            }
            break;
          case RewardType.TRAINING_BOOST:
            if (manager) {
              managerUpdates.trainingBoosts =
                (managerUpdates.trainingBoosts ?? manager.trainingBoosts ?? 0) + reward.value;
            }
            break;
          default:
            break;
        }
      });

      if (manager && Object.keys(managerUpdates).length > 0) {
        updateManager(managerUpdates);
      }
      if (team && Object.keys(teamUpdates).length > 0) {
        updateTeam(teamUpdates as any);
      }

      logger.log('Daily rewards collected and applied to cache', { managerUpdates, teamUpdates });
    } catch (e) {
      logger.error('Failed applying daily rewards to cache', e);
    } finally {
      setIsVisible(false);
    }
  };

  return (
    <StyledModal visible={isVisible} transparent animationType="slide">
      <Overlay>
        <Panel>
          <Heading bold>Daily Rewards</Heading>
          <StreakText>Current Streak: {loginStreak}</StreakText>

          <RewardsList
            data={rewards}
            keyExtractor={(_item: any[], idx: number) => idx.toString()}
            renderItem={({ item, index }: { item: any[]; index: number }) => {
              const isDay7 = index === 6;
              const isActive = index === rewardDay;
              return (
                <RewardCard isActive={isActive} isDay7={isDay7}>
                  <DayBadge isActive={isActive} isDay7={isDay7}>
                    <DayBadgeText isActive={isActive} isDay7={isDay7} bold={isDay7}>
                      Day {index + 1}
                      {isDay7 ? ' • Bonus' : ''}
                      {isActive ? ' • Today' : ''}
                    </DayBadgeText>
                  </DayBadge>

                  <RewardsRow isDay7={isDay7}>
                    {item.map((reward: any, i: number) => {
                      if (reward.type === RewardType.SCOUT_TOKEN) {
                        return (
                          <TokenContainer key={i} isDay7={isDay7}>
                            <TokenImage source={tokenImg} />
                            <CountText isDay7={isDay7}>x{reward.value}</CountText>
                          </TokenContainer>
                        );
                      }
                      if (reward.type === RewardType.MONEY) {
                        const cashImg = isDay7 ? largeCashImg : smallCashImg;
                        return (
                          <TokenContainer key={i} isDay7={isDay7}>
                            <TokenImage source={cashImg} />
                            <CountText isDay7={isDay7}>x{reward.value.toLocaleString()}</CountText>
                          </TokenContainer>
                        );
                      }
                      if (reward.type === RewardType.MAGIC_SPONGE) {
                        return (
                          <TokenContainer key={i} isDay7={isDay7}>
                            <TokenImage source={magicSpongeImg} />
                            <CountText isDay7={isDay7}>x{reward.value}</CountText>
                          </TokenContainer>
                        );
                      }
                      if (reward.type === RewardType.RED_CARD_APPEAL) {
                        return (
                          <TokenContainer key={i} isDay7={isDay7}>
                            <TokenImage source={redCardAppealImg} />
                            <CountText isDay7={isDay7}>x{reward.value}</CountText>
                          </TokenContainer>
                        );
                      }
                      if (reward.type === RewardType.TRAINING_BOOST) {
                        return (
                          <TokenContainer key={i} isDay7={isDay7}>
                            <TokenImage source={trainBoostImg} />
                            <CountText isDay7={isDay7}>x{reward.value}</CountText>
                          </TokenContainer>
                        );
                      }
                      // fallback for unknown types
                      return (
                        <RewardValue key={i} isDay7={isDay7}>
                          {reward.value}
                        </RewardValue>
                      );
                    })}
                  </RewardsRow>
                </RewardCard>
              );
            }}
          />

          <CollectButton onPress={handleCollect}>
            <CollectText bold>Collect</CollectText>
          </CollectButton>
        </Panel>
      </Overlay>
    </StyledModal>
  );
};

export default DailyRewardsModal;
