import React from 'react';
import { ActivityIndicator } from 'react-native';
import styled from 'styled-components/native';

interface SavingIndicatorProps {
  visible: boolean;
}

/**
 * Container for the saving indicator
 * Positioned in the bottom-right corner with a semi-transparent background
 */
const Container = styled.View`
  position: absolute;
  bottom: 20px;
  right: 20px;
  background-color: rgba(0, 0, 0, 0.7);
  border-radius: 20px;
  padding: 8px 16px;
  flex-direction: row;
  align-items: center;
  z-index: 1000;
`;

/**
 * Text for the saving message
 */
const SavingText = styled.Text`
  color: white;
  margin-left: 8px;
  font-size: 14px;
  font-family: 'Nunito';
`;

/**
 * SavingIndicator component
 * 
 * Displays an unobtrusive spinner with "Saving..." text
 * when changes are being saved to the API
 */
const SavingIndicator: React.FC<SavingIndicatorProps> = ({ visible }) => {
  if (!visible) return null;
  
  return (
    <Container>
      <ActivityIndicator size="small" color="#FFFFFF" />
      <SavingText>Saving...</SavingText>
    </Container>
  );
};

export default SavingIndicator;
