import React, { useMemo, useState } from 'react';
import { FlatList, Modal } from 'react-native';
import styled, { DefaultTheme } from 'styled-components/native';
import { League } from '../../models/league';
import { ActionButton, ActionButtonText } from '../ActionButton';
import { ScoutIcon } from '../Balance';
import { Text } from '../Text';
import { ScoutTokenType } from './SendScoutModal';

interface StyledProps {
  theme: DefaultTheme;
}

interface SelectableItemProps extends StyledProps {
  isSelected: boolean;
}

const ModalContainer = styled.View`
  flex: 1;
  justify-content: center;
  align-items: center;
  background-color: rgba(0, 0, 0, 0.5);
  padding: 20px;
`;

const ModalContent = styled.View`
  background-color: ${(props: StyledProps) => props.theme.colors.background};
  border-radius: 12px;
  padding: 20px;
  width: 90%;
  max-height: 80%;
`;

const ModalTitle = styled(Text)`
  font-family: ${(props: StyledProps) => props.theme.typography.bold};
  font-size: 20px;
  margin-bottom: 16px;
  text-align: center;
`;

const TierHeader = styled.TouchableOpacity<StyledProps>`
  padding: 8px 16px;
  background-color: ${(props: StyledProps) => props.theme.colors.surface};
  flex-direction: row;
  justify-content: space-between;
  align-items: center;
`;

const TierHeaderText = styled(Text)`
  font-family: ${(props: StyledProps) => props.theme.typography.bold};
  font-size: 18px;
`;

const TierArrow = styled(Text)`
  font-family: ${(props: StyledProps) => props.theme.typography.bold};
  font-size: 16px;
`;

const LeagueItem = styled.TouchableOpacity<SelectableItemProps>`
  padding: 12px 16px;
  background-color: ${(props) =>
    props.isSelected ? props.theme.colors.primary + '40' : 'transparent'};
`;

const LeagueName = styled(Text)<SelectableItemProps>`
  font-family: ${(props) =>
    props.isSelected ? props.theme.typography.bold : props.theme.typography.regular};
  font-size: 16px;
`;

const ButtonContainer = styled.View`
  flex-direction: row;
  justify-content: space-between;
  margin-top: 16px;
  gap: 12px;
`;

const TokenSelectionContainer = styled.View`
  flex-direction: row;
  margin-bottom: 16px;
  gap: 8px;
`;

const TokenOption = styled.TouchableOpacity<{ isSelected: boolean }>`
  flex: 1;
  padding: 12px;
  border-radius: 8px;
  border: 2px solid
    ${(props) => (props.isSelected ? props.theme.colors.primary : props.theme.colors.surface)};
  background-color: ${(props) =>
    props.isSelected ? props.theme.colors.primary + '20' : 'transparent'};
  align-items: center;
`;

const TokenOptionContent = styled.View`
  align-items: center;
  gap: 4px;
`;

const TokenOptionTitle = styled(Text)<{ isSelected: boolean }>`
  font-family: ${(props) =>
    props.isSelected ? props.theme.typography.bold : props.theme.typography.regular};
  font-size: 14px;
  text-align: center;
`;

const TokenOptionSubtitle = styled(Text)`
  font-size: 12px;
  color: ${(props: StyledProps) => props.theme.colors.text.secondary};
  text-align: center;
`;

const TokenIcon = styled.Image`
  width: 24px;
  height: 24px;
`;

interface Props {
  visible: boolean;
  leagues: League[];
  sendingScout: boolean;
  selectedTokenType: ScoutTokenType;
  onTokenTypeChange: (tokenType: ScoutTokenType) => void;
  onClose: () => void;
  onSelect: (leagueId: string, tokenType: ScoutTokenType) => void;
}

const LeagueSelectionModal: React.FC<Props> = ({
  visible,
  sendingScout,
  leagues,
  selectedTokenType,
  onTokenTypeChange,
  onClose,
  onSelect,
}) => {
  const [selectedLeagueId, setSelectedLeagueId] = React.useState<string | null>(null);
  const [collapsedTiers, setCollapsedTiers] = useState<Set<number>>(new Set());

  const toggleTierCollapse = (tier: number) => {
    setCollapsedTiers((prev) => {
      const newSet = new Set(prev);
      if (newSet.has(tier)) {
        newSet.delete(tier);
      } else {
        newSet.add(tier);
      }
      return newSet;
    });
  };

  const groupedLeagues = useMemo(() => {
    if (!leagues || !Array.isArray(leagues)) {
      return [];
    }

    const grouped = leagues.reduce(
      (acc, league) => {
        if (!acc[league.tier]) {
          acc[league.tier] = [];
        }
        acc[league.tier].push(league);
        return acc;
      },
      {} as Record<number, League[]>
    );

    return Object.entries(grouped)
      .sort(([tierA], [tierB]) => Number(tierA) - Number(tierB))
      .flatMap(([tier, leagueList]) => {
        const tierNumber = Number(tier);
        const isCollapsed = collapsedTiers.has(tierNumber);
        const items: any[] = [{ type: 'header', tier: tierNumber, isCollapsed }];

        if (!isCollapsed) {
          items.push(...leagueList.map((league) => ({ type: 'league', ...league })));
        }

        return items;
      });
  }, [leagues, collapsedTiers]);

  const renderItem = ({ item }: { item: any }) => {
    if (item.type === 'header') {
      return (
        <TierHeader onPress={() => toggleTierCollapse(item.tier)}>
          <TierHeaderText>Tier {item.tier}</TierHeaderText>
          <TierArrow>{item.isCollapsed ? '▶' : '▼'}</TierArrow>
        </TierHeader>
      );
    }

    return (
      <LeagueItem
        isSelected={selectedLeagueId === item.id}
        onPress={() => setSelectedLeagueId(item.id)}
      >
        <LeagueName isSelected={selectedLeagueId === item.id}>{item.name}</LeagueName>
      </LeagueItem>
    );
  };

  const handleConfirm = () => {
    if (selectedLeagueId) {
      onSelect(selectedLeagueId, selectedTokenType);
    }
  };

  return (
    <Modal visible={visible} transparent animationType="fade" onRequestClose={onClose}>
      <ModalContainer>
        <ModalContent>
          <ModalTitle>Select League to Scout</ModalTitle>

          <TokenSelectionContainer>
            <TokenOption
              isSelected={selectedTokenType === 'regular'}
              onPress={() => onTokenTypeChange('regular')}
            >
              <TokenOptionContent>
                <ScoutIcon source={require('../../../assets/scoutToken.png')} />
                <TokenOptionTitle isSelected={selectedTokenType === 'regular'}>
                  Regular Scout
                </TokenOptionTitle>
                <TokenOptionSubtitle>2-3 hour wait + £5,000</TokenOptionSubtitle>
              </TokenOptionContent>
            </TokenOption>

            <TokenOption
              isSelected={selectedTokenType === 'super'}
              onPress={() => onTokenTypeChange('super')}
            >
              <TokenOptionContent>
                <TokenIcon source={require('../../../assets/superScoutToken.png')} />
                <TokenOptionTitle isSelected={selectedTokenType === 'super'}>
                  Super Scout
                </TokenOptionTitle>
                <TokenOptionSubtitle>Almost instant report</TokenOptionSubtitle>
              </TokenOptionContent>
            </TokenOption>
          </TokenSelectionContainer>

          <FlatList
            data={groupedLeagues}
            renderItem={renderItem}
            keyExtractor={(item) => (item.type === 'header' ? `tier-${item.tier}` : item.id)}
          />
          <ButtonContainer>
            <ActionButton onPress={onClose} disabled={sendingScout}>
              <ActionButtonText>Cancel</ActionButtonText>
            </ActionButton>
            <ActionButton onPress={handleConfirm} disabled={!selectedLeagueId || sendingScout}>
              <ActionButtonText>Scout!</ActionButtonText>
            </ActionButton>
          </ButtonContainer>
        </ModalContent>
      </ModalContainer>
    </Modal>
  );
};

export default LeagueSelectionModal;
