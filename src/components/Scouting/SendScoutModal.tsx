import React, { useEffect, useState } from 'react';
import { callApi } from '../../api/client';
import { useDataCache } from '../../context/DataCacheContext';
import { useManager } from '../../context/ManagerContext';
import { League } from '../../models/league';
import { CrossPlatformAlert } from '../CrossPlatformAlert';
import LeagueSelectionModal from './LeagueSelectionModal';
import TeamSelectionModal from './TeamSelectionModal';

const SCOUT_COST = 5000;

export type ScoutTokenType = 'regular' | 'super';

interface SendScoutModalProps {
  state: 'league' | 'team' | null;
  leagues?: League[];
  onClose?: () => void;
}

interface RequestScoutingResponse {
  remainingTokens: number;
  remainingSuperTokens?: number;
  message: string;
  newBalance: number;
  error?: string;
}

export const SendScoutModal = ({ state, leagues, onClose }: SendScoutModalProps) => {
  const { manager } = useManager();
  const { setManager } = useDataCache();

  const [isLoading, setIsLoading] = useState(false);
  const [errorDetails, setErrorDetails] = useState<string | null>(null);
  const [isLeagueModalVisible, setLeagueModalVisible] = useState(false);
  const [isTeamModalVisible, setTeamModalVisible] = useState(false);
  const [showScoutingConfirmation, setShowScoutingConfirmation] = useState(false);
  const [selectedTokenType, setSelectedTokenType] = useState<ScoutTokenType>('regular');

  useEffect(() => {
    switch (state) {
      case 'league':
        setLeagueModalVisible(true);
        break;
      case 'team':
        setTeamModalVisible(true);
        break;
      default:
        setLeagueModalVisible(false);
        setTeamModalVisible(false);
    }
  }, [state]);

  const handleLeagueSelect = async (leagueId: string, tokenType: ScoutTokenType) => {
    // Validate token availability
    if (tokenType === 'regular') {
      if ((manager?.scoutTokens ?? 0) < 1) {
        setErrorDetails('You do not have enough scout tokens.');
        return;
      }
      if ((manager?.team?.balance ?? 0) < SCOUT_COST) {
        setErrorDetails('You do not have enough funds to send a scout.');
        return;
      }
    } else {
      if ((manager?.superScoutTokens ?? 0) < 1) {
        setErrorDetails('You do not have enough super scout tokens.');
        return;
      }
    }

    try {
      setIsLoading(true);
      const endpoint = tokenType === 'super' ? '/scouting/request/super' : '/scouting/request';
      const response = await callApi<RequestScoutingResponse>(endpoint, {
        method: 'POST',
        body: JSON.stringify({
          type: 'league',
          id: leagueId,
        }),
      });
      if (response.status !== 200) {
        setErrorDetails(response.error || 'An error occurred while scouting the league.');
        return;
      }
      // Update manager data on success
      if (manager) {
        if (tokenType === 'regular') {
          manager.scoutTokens = response.remainingTokens;
          manager.team!.balance = response.newBalance;
        } else {
          manager.superScoutTokens = response.remainingSuperTokens ?? manager.superScoutTokens - 1;
        }
        setManager(manager); // Update the manager in cache
      }
      setLeagueModalVisible(false);
      setShowScoutingConfirmation(true);
    } catch (e) {
      setErrorDetails((e as Error).message || 'An error occurred while scouting the league.');
    } finally {
      setIsLoading(false);
      onClose?.();
    }
  };

  const handleTeamSelect = async (teamId: string, tokenType: ScoutTokenType) => {
    // Validate token availability
    if (tokenType === 'regular') {
      if ((manager?.scoutTokens ?? 0) < 1) {
        setErrorDetails('You do not have enough scout tokens.');
        return;
      }
      if ((manager?.team?.balance ?? 0) < SCOUT_COST) {
        setErrorDetails('You do not have enough funds to send a scout.');
        return;
      }
    } else {
      if ((manager?.superScoutTokens ?? 0) < 1) {
        setErrorDetails('You do not have enough super scout tokens.');
        return;
      }
    }

    try {
      setIsLoading(true);
      const endpoint = tokenType === 'super' ? '/scouting/request/super' : '/scouting/request';
      const response = await callApi<RequestScoutingResponse>(endpoint, {
        method: 'POST',
        body: JSON.stringify({
          type: 'team',
          id: teamId,
        }),
      });
      if (response.status !== 200) {
        setErrorDetails(response.error || 'An error occurred while scouting the team.');
        return;
      }
      // Update manager data on success
      if (manager) {
        if (tokenType === 'regular') {
          manager.scoutTokens = response.remainingTokens;
          manager.team!.balance = response.newBalance;
        } else {
          manager.superScoutTokens = response.remainingSuperTokens ?? manager.superScoutTokens - 1;
        }
        setManager(manager); // Update the manager in cache
      }
      setTeamModalVisible(false);
      setShowScoutingConfirmation(true);
    } catch (e) {
      setErrorDetails((e as Error).message || 'An error occurred while scouting the team.');
    } finally {
      setIsLoading(false);
      onClose?.();
    }
  };

  return (
    <>
      <LeagueSelectionModal
        visible={isLeagueModalVisible}
        leagues={leagues || []}
        sendingScout={isLoading}
        selectedTokenType={selectedTokenType}
        onTokenTypeChange={setSelectedTokenType}
        onClose={() => {
          setLeagueModalVisible(false);
          onClose?.();
        }}
        onSelect={handleLeagueSelect}
      />

      <TeamSelectionModal
        visible={isTeamModalVisible}
        leagues={leagues || []}
        sendingScout={isLoading}
        selectedTokenType={selectedTokenType}
        onTokenTypeChange={setSelectedTokenType}
        onClose={() => {
          setTeamModalVisible(false);
          onClose?.();
        }}
        onSelect={handleTeamSelect}
      />

      <CrossPlatformAlert
        visible={showScoutingConfirmation}
        title="Success"
        message={
          selectedTokenType === 'super'
            ? "Your super scout has rushed off and will return with a report almost instantly!"
            : "Your scout has packed his bags and is off to look for players who know their left from their right."
        }
        buttons={[
          {
            text: 'OK',
            onPress: () => setShowScoutingConfirmation(false),
          },
        ]}
        onDismiss={() => setShowScoutingConfirmation(false)}
      />

      <CrossPlatformAlert
        visible={errorDetails !== null}
        title="Error"
        message={errorDetails ?? ''}
        buttons={[
          {
            text: 'OK',
            onPress: () => setErrorDetails(null),
          },
        ]}
        onDismiss={() => setErrorDetails(null)}
      />
    </>
  );
};
