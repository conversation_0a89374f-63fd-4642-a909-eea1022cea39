import React, { useEffect, useState } from 'react';
import { ActivityIndicator, FlatList, Modal } from 'react-native';
import styled, { DefaultTheme } from 'styled-components/native';
import { useLeague } from '../../hooks/useQueries';
import { League } from '../../models/league';
import { ActionButton, ActionButtonText } from '../ActionButton';
import { ScoutIcon } from '../Balance';
import { Text } from '../Text';
import { ScoutTokenType } from './SendScoutModal';

interface StyledProps {
  theme: DefaultTheme;
}

interface SelectableItemProps extends StyledProps {
  isSelected: boolean;
}

interface Team {
  teamId: string;
  teamName: string;
  leagueId: string;
}

const ModalContainer = styled.View`
  flex: 1;
  justify-content: center;
  align-items: center;
  background-color: rgba(0, 0, 0, 0.5);
  padding: 20px;
`;

const ModalContent = styled.View`
  background-color: ${(props: StyledProps) => props.theme.colors.background};
  border-radius: 12px;
  padding: 20px;
  width: 90%;
  max-height: 80%;
`;

const ModalTitle = styled(Text)`
  font-family: ${(props: StyledProps) => props.theme.typography.bold};
  font-size: 20px;
  margin-bottom: 16px;
  text-align: center;
`;

const TierHeader = styled.TouchableOpacity<StyledProps>`
  padding: 8px 16px;
  background-color: ${(props: StyledProps) => props.theme.colors.surface};
  flex-direction: row;
  justify-content: space-between;
  align-items: center;
`;

const TierHeaderText = styled(Text)`
  font-family: ${(props: StyledProps) => props.theme.typography.bold};
  font-size: 18px;
`;

const TierArrow = styled(Text)`
  font-family: ${(props: StyledProps) => props.theme.typography.bold};
  font-size: 16px;
`;

const LeagueHeader = styled(Text)`
  font-family: ${(props: StyledProps) => props.theme.typography.bold};
  font-size: 16px;
  padding: 8px 16px;
  background-color: ${(props: StyledProps) => props.theme.colors.surface + '80'};
`;

const TeamItem = styled.TouchableOpacity<SelectableItemProps>`
  padding: 12px 16px;
  background-color: ${(props) =>
    props.isSelected ? props.theme.colors.primary + '40' : 'transparent'};
`;

const TeamName = styled(Text)<SelectableItemProps>`
  font-family: ${(props) =>
    props.isSelected ? props.theme.typography.bold : props.theme.typography.regular};
  font-size: 16px;
`;

const ButtonContainer = styled.View`
  flex-direction: row;
  justify-content: space-between;
  margin-top: 16px;
  gap: 12px;
`;

const LoadingContainer = styled.View`
  padding: 20px;
  align-items: center;
`;

const TokenSelectionContainer = styled.View`
  flex-direction: row;
  margin-bottom: 16px;
  gap: 8px;
`;

const TokenOption = styled.TouchableOpacity<{ isSelected: boolean }>`
  flex: 1;
  padding: 12px;
  border-radius: 8px;
  border: 2px solid
    ${(props) => (props.isSelected ? props.theme.colors.primary : props.theme.colors.surface)};
  background-color: ${(props) =>
    props.isSelected ? props.theme.colors.primary + '20' : 'transparent'};
  align-items: center;
`;

const TokenOptionContent = styled.View`
  align-items: center;
  gap: 4px;
`;

const TokenOptionTitle = styled(Text)<{ isSelected: boolean }>`
  font-family: ${(props) =>
    props.isSelected ? props.theme.typography.bold : props.theme.typography.regular};
  font-size: 14px;
  text-align: center;
`;

const TokenOptionSubtitle = styled(Text)`
  font-size: 12px;
  color: ${(props: StyledProps) => props.theme.colors.text.secondary};
  text-align: center;
`;

const TokenIcon = styled.Image`
  width: 24px;
  height: 24px;
`;

interface Props {
  visible: boolean;
  sendingScout: boolean;
  leagues: League[];
  selectedTokenType: ScoutTokenType;
  onTokenTypeChange: (tokenType: ScoutTokenType) => void;
  onClose: () => void;
  onSelect: (teamId: string, tokenType: ScoutTokenType) => void;
}

const TeamSelectionModal: React.FC<Props> = ({
  visible,
  sendingScout,
  leagues,
  selectedTokenType,
  onTokenTypeChange,
  onClose,
  onSelect,
}) => {
  const [selectedTeamId, setSelectedTeamId] = useState<string | null>(null);
  const [selectedLeagueId, setSelectedLeagueId] = useState<string | null>(null);
  const [teams, setTeams] = useState<Team[]>([]);
  const [collapsedTiers, setCollapsedTiers] = useState<Set<number>>(new Set());
  const gameworldId = leagues[0]?.gameworld;
  const { data: league, isLoading } = useLeague(gameworldId, selectedLeagueId ?? undefined);

  // Fetch teams when a league is selected
  useEffect(() => {
    if (league && league.teams) {
      setTeams(
        league.teams.map((team: any) => ({
          teamId: team.teamId,
          teamName: team.teamName,
          leagueId: team.leagueId,
        }))
      );
    } else {
      setTeams([]);
    }
  }, [league]);

  const toggleTierCollapse = (tier: number) => {
    setCollapsedTiers((prev) => {
      const newSet = new Set(prev);
      if (newSet.has(tier)) {
        newSet.delete(tier);
      } else {
        newSet.add(tier);
      }
      return newSet;
    });
  };

  const groupedLeagues = React.useMemo(() => {
    if (!leagues || !Array.isArray(leagues)) {
      return [];
    }

    const grouped = leagues.reduce(
      (acc, l) => {
        if (!acc[l.tier]) {
          acc[l.tier] = [];
        }
        acc[l.tier].push(l);
        return acc;
      },
      {} as Record<number, League[]>
    );

    return Object.entries(grouped)
      .sort(([tierA], [tierB]) => Number(tierA) - Number(tierB))
      .flatMap(([tier, leagueList]) => {
        const tierNumber = Number(tier);
        const isCollapsed = collapsedTiers.has(tierNumber);
        const items: any[] = [{ type: 'tier', tier: tierNumber, isCollapsed }];

        if (!isCollapsed) {
          items.push(...leagueList.map((l) => ({ type: 'league', ...l })));
        }

        return items;
      });
  }, [leagues, collapsedTiers]);

  const renderItem = ({ item }: { item: any }) => {
    if (item.type === 'tier') {
      return (
        <TierHeader onPress={() => toggleTierCollapse(item.tier)}>
          <TierHeaderText>Tier {item.tier}</TierHeaderText>
          <TierArrow>{item.isCollapsed ? '▶' : '▼'}</TierArrow>
        </TierHeader>
      );
    }

    if (item.type === 'league') {
      const isExpanded = selectedLeagueId === item.id;
      return (
        <>
          <LeagueHeader
            onPress={() => {
              if (isExpanded) {
                setSelectedLeagueId(null);
                setTeams([]);
                setSelectedTeamId(null);
              } else {
                setSelectedLeagueId(item.id);
                setSelectedTeamId(null);
              }
            }}
          >
            {item.name} {isExpanded ? '▼' : '▶'}
          </LeagueHeader>
          {isExpanded &&
            (isLoading ? (
              <LoadingContainer>
                <ActivityIndicator size="large" />
              </LoadingContainer>
            ) : (
              teams.map((team) => (
                <TeamItem
                  key={team.teamId}
                  isSelected={selectedTeamId === team.teamId}
                  onPress={() => setSelectedTeamId(team.teamId)}
                >
                  <TeamName isSelected={selectedTeamId === team.teamId}>{team.teamName}</TeamName>
                </TeamItem>
              ))
            ))}
        </>
      );
    }

    return null;
  };

  const handleConfirm = () => {
    if (selectedTeamId) {
      onSelect(selectedTeamId, selectedTokenType);
    }
  };

  return (
    <Modal visible={visible} transparent animationType="fade" onRequestClose={onClose}>
      <ModalContainer>
        <ModalContent>
          <ModalTitle>Select Team to Scout</ModalTitle>

          <TokenSelectionContainer>
            <TokenOption
              isSelected={selectedTokenType === 'regular'}
              onPress={() => onTokenTypeChange('regular')}
            >
              <TokenOptionContent>
                <ScoutIcon source={require('../../../assets/scoutToken.png')} />
                <TokenOptionTitle isSelected={selectedTokenType === 'regular'}>
                  Regular Scout
                </TokenOptionTitle>
                <TokenOptionSubtitle>2-3 hour wait + £5,000</TokenOptionSubtitle>
              </TokenOptionContent>
            </TokenOption>

            <TokenOption
              isSelected={selectedTokenType === 'super'}
              onPress={() => onTokenTypeChange('super')}
            >
              <TokenOptionContent>
                <TokenIcon source={require('../../../assets/superScoutToken.png')} />
                <TokenOptionTitle isSelected={selectedTokenType === 'super'}>
                  Super Scout
                </TokenOptionTitle>
                <TokenOptionSubtitle>Almost instant report</TokenOptionSubtitle>
              </TokenOptionContent>
            </TokenOption>
          </TokenSelectionContainer>

          <FlatList
            data={groupedLeagues}
            renderItem={renderItem}
            keyExtractor={(item) => (item.type === 'tier' ? `tier-${item.tier}` : item.id)}
          />

          <ButtonContainer>
            <ActionButton onPress={onClose} disabled={sendingScout}>
              <ActionButtonText>Cancel</ActionButtonText>
            </ActionButton>
            <ActionButton
              onPress={handleConfirm}
              style={{ opacity: selectedTeamId ? 1 : 0.5 }}
              disabled={!selectedTeamId || sendingScout}
            >
              <ActionButtonText>Scout!</ActionButtonText>
            </ActionButton>
          </ButtonContainer>
        </ModalContent>
      </ModalContainer>
    </Modal>
  );
};

export default TeamSelectionModal;
