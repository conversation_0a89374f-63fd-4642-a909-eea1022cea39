import React, { useEffect, useState } from 'react';
import { ActivityIndicator, Alert, Modal, Platform } from 'react-native';
import styled, { DefaultTheme } from 'styled-components/native';
import { callApi } from '../api/client';
import { useManager } from '../context/ManagerContext';
import { logger } from '../utils/logger';
import { AD_TYPES, useRewardAd } from './Ads';
import { Text } from './Text';

interface StyledProps {
  theme: DefaultTheme;
}

interface TeamNameChangeModalProps {
  visible: boolean;
  onClose: () => void;
  onSuccess: (newName: string) => void;
}

const ModalContainer = styled.View`
  flex: 1;
  justify-content: center;
  align-items: center;
  background-color: rgba(0, 0, 0, 0.5);
  padding: 20px;
`;

const ModalContent = styled.View`
  background-color: ${(props: StyledProps) => props.theme.colors.surface};
  border-radius: 12px;
  padding: 24px;
  width: 100%;
  max-width: 400px;
  align-items: center;
`;

const ModalTitle = styled(Text)`
  font-size: 20px;
  font-family: ${(props: StyledProps) => props.theme.typography.bold};
  color: ${(props: StyledProps) => props.theme.colors.text.primary};
  margin-bottom: 16px;
  text-align: center;
`;

const DetailText = styled(Text)`
  font-size: 16px;
  color: ${(props: StyledProps) => props.theme.colors.text.secondary};
  text-align: center;
  margin-bottom: 24px;
  line-height: 22px;
`;

const InputContainer = styled.View`
  width: 100%;
  margin-bottom: 24px;
`;

const Label = styled(Text)`
  font-size: 16px;
  color: ${(props: StyledProps) => props.theme.colors.text.primary};
  margin-bottom: 8px;
  font-family: ${(props: StyledProps) => props.theme.typography.bold};
`;

const Input = styled.TextInput`
  background-color: ${(props: StyledProps) => props.theme.colors.background};
  border: 1px solid ${(props: StyledProps) => props.theme.colors.border};
  border-radius: 8px;
  padding: 12px;
  font-size: 16px;
  color: ${(props: StyledProps) => props.theme.colors.text.primary};
  font-family: ${(props: StyledProps) => props.theme.typography.regular};
`;

const ModalButtonContainer = styled.View`
  width: 100%;
  gap: 12px;
`;

const ModalButton = styled.TouchableOpacity`
  background-color: ${(props: StyledProps) => props.theme.colors.primary};
  border-radius: 8px;
  padding: 16px;
  align-items: center;
  width: 100%;
`;

const SecondaryButton = styled.TouchableOpacity`
  background-color: transparent;
  border: 1px solid ${(props: StyledProps) => props.theme.colors.border};
  border-radius: 8px;
  padding: 16px;
  align-items: center;
  width: 100%;
`;

const ButtonText = styled(Text)`
  color: white;
  font-family: ${(props: StyledProps) => props.theme.typography.bold};
  font-size: 16px;
`;

const SecondaryButtonText = styled(Text)`
  color: ${(props: StyledProps) => props.theme.colors.text.primary};
  font-family: ${(props: StyledProps) => props.theme.typography.bold};
  font-size: 16px;
`;

const WarningText = styled(Text)`
  font-size: 14px;
  color: #e3172a;
  text-align: center;
  margin-bottom: 16px;
  line-height: 18px;
`;

export const TeamNameChangeModal: React.FC<TeamNameChangeModalProps> = ({
  visible,
  onClose,
  onSuccess,
}) => {
  const { manager, team, updateManager, updateTeam } = useManager();
  const { isAdLoaded, showAd } = useRewardAd();
  const [teamName, setTeamName] = useState(team?.teamName || '');
  const [isLoading, setIsLoading] = useState(false);

  const hasChangedNameBefore = manager?.changedTeamName === true;
  const isNameChanged = teamName.trim() !== team?.teamName;
  const isValidName = teamName.trim().length >= 3 && teamName.trim().length <= 30;
  const [hasWatchedAd, setHasWatchedAd] = useState(false);

  // Reset state when modal opens/closes
  useEffect(() => {
    if (visible) {
      setTeamName(team?.teamName || '');
      setHasWatchedAd(false);
    }
  }, [visible, team?.teamName]);

  const handleNameChange = async () => {
    if (!isValidName || !isNameChanged || !manager?.gameworldId || !team?.teamId) return;

    try {
      setIsLoading(true);

      const response = await callApi(`/team/name`, {
        method: 'PUT',
        body: JSON.stringify({
          teamName: teamName.trim(),
        }),
      });

      if (response.status === 200) {
        // Update local state
        if (team) {
          updateTeam({ teamName: teamName.trim() });
        }

        // Mark that the manager has changed their team name
        if (manager && !manager.changedTeamName) {
          updateManager({ changedTeamName: true });
        }

        onSuccess(teamName.trim());
        onClose();

        if (Platform.OS === 'web') {
          alert('Team name changed successfully!');
        } else {
          Alert.alert('Success', 'Team name changed successfully!');
        }
      }
    } catch (error) {
      logger.error('Error changing team name:', error);
      const errorMessage = 'Failed to change team name. Please try again.';

      if (Platform.OS === 'web') {
        alert(errorMessage);
      } else {
        Alert.alert('Error', errorMessage);
      }
    } finally {
      setIsLoading(false);
    }
  };

  const handleWatchAd = async () => {
    await showAd(AD_TYPES.TEAM_NAME_CHANGE);
    setHasWatchedAd(true);
  };

  return (
    <Modal visible={visible} transparent animationType="fade" onRequestClose={onClose}>
      <ModalContainer>
        <ModalContent>
          <ModalTitle>Change Team Name</ModalTitle>

          {!hasChangedNameBefore ? (
            <DetailText>
              You can change your team name once for free. After that, you'll need to watch an ad to
              change it again.
            </DetailText>
          ) : (
            <DetailText>
              You've already changed your team name once. Watch an ad to change it again.
            </DetailText>
          )}

          <InputContainer>
            <Label>Team Name</Label>
            <Input
              value={teamName}
              onChangeText={setTeamName}
              placeholder="Enter new team name"
              maxLength={30}
              autoCapitalize="words"
            />
            {!isValidName && teamName.length > 0 && (
              <WarningText>Team name must be between 3 and 30 characters</WarningText>
            )}
          </InputContainer>

          <ModalButtonContainer>
            {hasChangedNameBefore && !hasWatchedAd ? (
              <ModalButton
                disabled={!isAdLoaded(AD_TYPES.TEAM_NAME_CHANGE)}
                onPress={handleWatchAd}
                style={{ opacity: !isAdLoaded(AD_TYPES.TEAM_NAME_CHANGE) ? 0.5 : 1 }}
              >
                <ButtonText>
                  {isAdLoaded(AD_TYPES.TEAM_NAME_CHANGE) ? 'Watch ad to unlock' : 'Loading ad...'}
                </ButtonText>
              </ModalButton>
            ) : (
              <ModalButton
                disabled={!isValidName || !isNameChanged || isLoading}
                onPress={handleNameChange}
                style={{ opacity: !isValidName || !isNameChanged || isLoading ? 0.5 : 1 }}
              >
                {isLoading ? (
                  <ActivityIndicator size="small" color="white" />
                ) : (
                  <ButtonText>
                    {hasChangedNameBefore ? 'Change Name' : 'Change Name (Free)'}
                  </ButtonText>
                )}
              </ModalButton>
            )}

            <SecondaryButton onPress={onClose}>
              <SecondaryButtonText>Cancel</SecondaryButtonText>
            </SecondaryButton>
          </ModalButtonContainer>
        </ModalContent>
      </ModalContainer>
    </Modal>
  );
};
