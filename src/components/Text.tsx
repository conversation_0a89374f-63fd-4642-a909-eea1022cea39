import { TextProps as RNTextProps } from 'react-native';
import styled from 'styled-components/native';

interface TextProps extends RNTextProps {
  bold?: boolean;
}

export const Text = styled.Text<TextProps>`
  font-family: ${(props) =>
    props.bold ? props.theme.typography.bold : props.theme.typography.regular};
  color: ${(props) => props.theme.colors.text.primary};
`;

export const Title = styled.Text`
  font-family: ${(props) => props.theme.typography.bold};
  font-size: 24px;
`;

export const Subtitle = styled.Text`
  font-family: ${(props) => props.theme.typography.regular};
  font-size: 18px;
`;
