import { MaterialIcons } from '@expo/vector-icons';
import React from 'react';
import { TouchableOpacity } from 'react-native';
import styled, { DefaultTheme } from 'styled-components/native';
import { Player } from '../models/player';
import { logger } from '../utils/logger';
import { formatCurrencyShort } from '../utils/utils';
import { ChevronIcon } from './PlayerRow/SharedComponents';
import { Text } from './Text';

interface StyledProps {
  theme: DefaultTheme;
}

interface TrainingSlotProps {
  slotIndex: number;
  isUnlocked: boolean;
  unlockCost?: number | string;
  assignedPlayer?: Player;
  trainingAttribute?: string;
  startValue?: number;
  currentValue?: number;
  onPress: () => void;
}

const SlotContainer = styled(TouchableOpacity)<StyledProps & { isUnlocked: boolean }>`
  background-color: ${(props) => props.theme.colors.surface};
  border-radius: 8px;
  padding: 16px;
  margin: 8px 0;
  min-height: 120px;
  flex-direction: row;
  align-items: center;
  border-width: 2px;
  border-color: ${(props) =>
    props.isUnlocked ? 'transparent' : props.theme.colors.text.secondary};
  opacity: ${(props) => (props.isUnlocked ? 1 : 0.6)};
  shadow-color: #000;
  shadow-offset: 0px 2px;
  shadow-opacity: 0.1;
  shadow-radius: 2px;
  flex: 1;
`;

const ContentContainer = styled.View`
  flex: 1;
  flex-direction: row;
  align-items: center;
`;

const LeftSection = styled.View`
  flex: 1;
`;

const SlotNumber = styled(Text)<StyledProps>`
  font-size: 14px;
  color: ${(props) => props.theme.colors.text.secondary};
  font-family: ${(props: StyledProps) => props.theme.typography.bold};
  margin-bottom: 4px;
`;

const PlayerName = styled(Text)<StyledProps>`
  font-size: 16px;
  color: ${(props) => props.theme.colors.text.primary};
  font-family: ${(props: StyledProps) => props.theme.typography.bold};
  margin-bottom: 4px;
`;

const TrainingInfo = styled(Text)<StyledProps>`
  font-size: 14px;
  color: ${(props) => props.theme.colors.text.secondary};
  font-family: 'Nunito';
`;

const TrainingImprovement = styled(Text)<StyledProps>`
  font-size: 14px;
  color: ${(props) => props.theme.colors.success};
  font-family: 'Nunito';
`;

const LockContainer = styled.View<StyledProps>`
  align-items: center;
  justify-content: center;
  flex: 1;
`;

const LockIcon = styled(MaterialIcons)<StyledProps>`
  margin-bottom: 8px;
`;

const UnlockCost = styled(Text)<StyledProps>`
  font-size: 14px;
  color: ${(props) => props.theme.colors.text.secondary};
  font-family: ${(props: StyledProps) => props.theme.typography.bold};
  text-align: center;
`;

export const ArrowContainer = styled.View`
  padding-left: 16px;
`;

const TrainingSlot: React.FC<TrainingSlotProps> = ({
  slotIndex,
  isUnlocked,
  unlockCost,
  assignedPlayer,
  trainingAttribute,
  startValue,
  currentValue,
  onPress,
}) => {
  const formatAttributeName = (attr: string) => {
    const attributeNames: Record<string, string> = {
      reflexes: 'Reflexes',
      positioning: 'Positioning',
      shotStopping: 'Shot Stopping',
      tackling: 'Tackling',
      marking: 'Marking',
      heading: 'Heading',
      finishing: 'Finishing',
      pace: 'Pace',
      crossing: 'Crossing',
      passing: 'Passing',
      vision: 'Vision',
      ballControl: 'Ball Control',
      stamina: 'Stamina',
    };
    return attributeNames[attr] || attr;
  };

  logger.log('Training slot props:', {
    slotIndex,
    isUnlocked,
    unlockCost,
    assignedPlayer,
    trainingAttribute,
    startValue,
  });

  return (
    <SlotContainer isUnlocked={isUnlocked} onPress={onPress}>
      <ContentContainer>
        <LeftSection>
          <SlotNumber>Training Slot {slotIndex + 1}</SlotNumber>

          {!isUnlocked ? (
            <LockContainer>
              <LockIcon name="lock" size={32} color="#666" />
              {typeof unlockCost === 'number' && (
                <UnlockCost>Unlock for {formatCurrencyShort(unlockCost)}</UnlockCost>
              )}
              {typeof unlockCost === 'string' && <UnlockCost>Buy for {unlockCost}</UnlockCost>}
            </LockContainer>
          ) : assignedPlayer ? (
            <>
              <PlayerName>
                {assignedPlayer.firstName} {assignedPlayer.surname}
              </PlayerName>
              {trainingAttribute && (
                <TrainingInfo>Training: {formatAttributeName(trainingAttribute)}</TrainingInfo>
              )}
              {/* Improvement display */}
              {trainingAttribute &&
                startValue !== undefined &&
                currentValue !== undefined &&
                (() => {
                  logger.log('Training attribute:', trainingAttribute);
                  logger.log('Assigned player attributes:', assignedPlayer.attributes);
                  logger.log('Start value:', startValue);
                  const improvement =
                    trainingAttribute === 'stamina'
                      ? startValue !== 0
                        ? ((currentValue - startValue) / startValue) * 100
                        : 0
                      : currentValue - startValue;
                  if (improvement > 0) {
                    return (
                      <TrainingImprovement>
                        Improvement: +{improvement.toFixed(2)}
                        {trainingAttribute === 'stamina' ? '%' : ''} 🚀
                      </TrainingImprovement>
                    );
                  }
                  return null;
                })()}
            </>
          ) : (
            <TrainingInfo>Tap to assign a player</TrainingInfo>
          )}
        </LeftSection>

        <ArrowContainer>
          <ChevronIcon isDisabled={!isUnlocked} />
        </ArrowContainer>
      </ContentContainer>
    </SlotContainer>
  );
};

export default TrainingSlot;
