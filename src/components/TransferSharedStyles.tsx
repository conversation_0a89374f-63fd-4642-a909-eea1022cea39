import { Platform } from 'react-native';
import styled, { DefaultTheme } from 'styled-components/native';
import { Text } from './Text';

interface StyledProps {
  theme: DefaultTheme;
}

export const FilterContainer = styled.View`
  margin-bottom: 16px;
  z-index: 999;
  flex-direction: row;
  align-items: center;
  gap: 12px;
`;

export const ButtonContainer = styled.View`
  flex-direction: row;
  gap: 12px;
  margin-bottom: 12px;
`;

export const Container = styled.View<StyledProps>`
  flex: 1;
  background-color: ${(props) => props.theme.colors.background};
  padding: 16px;
  ${Platform.OS === 'web' ? 'position: relative;' : ''}
`;

export const LoadingContainer = styled.View`
  flex: 1;
  justify-content: center;
  align-items: center;
`;

export const ListContainer = styled.View`
  flex: 1;
  margin-top: 16px;
  z-index: 1;
`;

export const ListHeaderText = styled(Text)`
  font-size: 18px;
  font-family: ${(props: StyledProps) => props.theme.typography.bold};
  margin-bottom: 8px;
  margin-top: 16;
`;

export const EmptyListContainer = styled.View`
  flex: 1;
  justify-content: center;
  align-items: center;
  padding: 20px;
`;

export const EmptyListText = styled(Text)<StyledProps>`
  font-size: 16px;
  text-align: center;
  color: ${(props) => props.theme.colors.text.secondary};
`;

export interface LoadMoreButtonProps extends StyledProps {
  disabled?: boolean;
}

export const LoadMoreButton = styled.TouchableOpacity<LoadMoreButtonProps>`
  background-color: ${(props) =>
    props.disabled ? props.theme.colors.primary + '80' : props.theme.colors.primary};
  padding: 12px;
  border-radius: 8px;
  align-items: center;
  justify-content: center;
  flex-direction: row;
  margin: 16px 0;
  align-self: center;
  min-width: 150px;
`;

export const ButtonText = styled(Text)`
  color: white;
  font-family: ${(props: StyledProps) => props.theme.typography.bold};
  font-size: 16px;
`;
