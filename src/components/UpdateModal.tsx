import React from 'react';
import { Linking, Modal } from 'react-native';
import styled, { DefaultTheme } from 'styled-components/native';
import { logger } from '../utils/logger';
import { ActionButton, ActionButtonText } from './ActionButton';
import { Text } from './Text';

interface StyledProps {
  theme: DefaultTheme;
}

interface UpdateModalProps {
  visible: boolean;
  versionString: string;
  forceUpdate: boolean;
  onDismiss?: () => void;
}

const ModalContainer = styled.View`
  flex: 1;
  justify-content: center;
  align-items: center;
  background-color: rgba(0, 0, 0, 0.5);
  padding: 20px;
`;

const ModalContent = styled.View`
  background-color: ${(props: StyledProps) => props.theme.colors.surface};
  border-radius: 12px;
  padding: 24px;
  width: 100%;
  max-width: 400px;
  align-items: center;
`;

const ModalTitle = styled.Text`
  font-size: 20px;
  font-family: ${(props: StyledProps) => props.theme.typography.bold};
  margin-bottom: 16px;
  text-align: center;
  color: ${(props: StyledProps) => props.theme.colors.text.primary};
`;

const ModalMessage = styled(Text)`
  font-size: 16px;
  color: ${(props: StyledProps) => props.theme.colors.text.primary};
  margin-bottom: 24px;
  text-align: center;
  line-height: 22px;
`;

const ButtonContainer = styled.View`
  flex-direction: row;
  gap: 12px;
  width: 100%;
`;

const UpdateButton = styled(ActionButton)`
  flex: 1;
  background-color: ${(props: StyledProps) => props.theme.colors.primary};
`;

const DismissButton = styled(ActionButton)`
  flex: 1;
  background-color: ${(props: StyledProps) => props.theme.colors.button.secondary};
`;

const PLAY_STORE_URL =
  'https://play.google.com/store/apps/details?id=com.rwsoftware.jumpersforgoalposts';

export const UpdateModal: React.FC<UpdateModalProps> = ({
  visible,
  versionString,
  forceUpdate,
  onDismiss,
}) => {
  const handleUpdatePress = async () => {
    try {
      await Linking.openURL(PLAY_STORE_URL);
    } catch (error) {
      logger.error('Error opening Play Store:', error);
    }
  };

  const handleDismiss = () => {
    if (!forceUpdate && onDismiss) {
      onDismiss();
    }
  };

  return (
    <Modal visible={visible} transparent animationType="fade" onRequestClose={handleDismiss}>
      <ModalContainer>
        <ModalContent>
          <ModalTitle>{forceUpdate ? 'Update Required' : 'Update Available'}</ModalTitle>

          <ModalMessage>
            {forceUpdate
              ? `A new version (${versionString}) is required to continue using the app. Please update from the Play Store.`
              : `A new version (${versionString}) is available. Update now to get the latest features and improvements.`}
          </ModalMessage>

          <ButtonContainer>
            <UpdateButton onPress={handleUpdatePress}>
              <ActionButtonText>Update Now</ActionButtonText>
            </UpdateButton>

            {!forceUpdate && (
              <DismissButton onPress={handleDismiss}>
                <ActionButtonText>Later</ActionButtonText>
              </DismissButton>
            )}
          </ButtonContainer>
        </ModalContent>
      </ModalContainer>
    </Modal>
  );
};
