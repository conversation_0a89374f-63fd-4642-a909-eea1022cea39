import AsyncStorage from '@react-native-async-storage/async-storage';
import React, { useEffect, useState } from 'react';
import { ActivityIndicator, Text } from 'react-native';
import { MANAGER_ID_KEY } from '../../context/ManagerContext';
import { useVersion } from '../../context/VersionContext';
import { logger } from '../../utils/logger';
import { MainContainer } from '../MainContainer';
import { LoadingContainer } from '../TransferSharedStyles';

interface AppLayoutGuardProps {
  children: React.ReactNode;
}

/**
 * AppLayoutGuard handles app-level checks before showing the main layout.
 * Single responsibility: Ensure app prerequisites are met.
 */
export const AppLayoutGuard: React.FC<AppLayoutGuardProps> = ({ children }) => {
  logger.log('AppLayoutGuard mounted or remounted');
  const { isVersionCheckComplete } = useVersion();
  const [hasTeamAssigned, setHasTeamAssigned] = useState<boolean | null>(null);
  const [isCheckingTeam, setIsCheckingTeam] = useState(true);

  useEffect(() => {
    checkTeamAssignment();
  }, []);

  const checkTeamAssignment = async () => {
    try {
      setIsCheckingTeam(true);
      const managerId = await AsyncStorage.getItem(MANAGER_ID_KEY);
      logger.log('Manager ID:', managerId);
      setHasTeamAssigned(managerId !== null);
    } catch (error) {
      logger.error('Error checking team assignment:', error);
      setHasTeamAssigned(false);
    } finally {
      setIsCheckingTeam(false);
    }
  };

  // Wait for version check to complete
  if (!isVersionCheckComplete) {
    logger.log('Waiting for version check...');
    return (
      <MainContainer>
        <LoadingContainer>
          <ActivityIndicator size="large" />
          <Text>Checking for updates...</Text>
        </LoadingContainer>
      </MainContainer>
    );
  }

  // Wait for team assignment check
  if (isCheckingTeam || hasTeamAssigned === null) {
    logger.log('Checking team assignment...');
    return (
      <MainContainer>
        <LoadingContainer>
          <ActivityIndicator size="large" />
          <Text>Loading your team...</Text>
        </LoadingContainer>
      </MainContainer>
    );
  }

  return <>{children}</>;
};
