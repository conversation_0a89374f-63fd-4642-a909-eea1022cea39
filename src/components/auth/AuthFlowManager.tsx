import React, { useCallback, useEffect, useState } from 'react';
import { ActivityIndicator } from 'react-native';
import { useAuth } from '../../context/AuthContext';
import { logger } from '../../utils/logger';
import { MainContainer } from '../MainContainer';
import { Text } from '../Text';
import { LoadingContainer } from '../TransferSharedStyles';
import { MigrationHandler } from './MigrationHandler';

export type AuthFlowState = 'loading' | 'unauthenticated' | 'migration_required' | 'ready';

interface AuthFlowManagerProps {
  children: React.ReactNode;
  onStateChange?: (state: AuthFlowState) => void;
}

/**
 * AuthFlowManager orchestrates the entire authentication flow.
 * Single responsibility: Determine what should be shown based on auth state.
 */
export const AuthFlowManager: React.FC<AuthFlowManagerProps> = ({ children, onStateChange }) => {
  const { authState } = useAuth();
  const [flowState, setFlowState] = useState<AuthFlowState>('loading');

  // Use useCallback to prevent unnecessary re-renders
  const handleMigrationComplete = useCallback(() => {
    logger.log('Migration completed, proceeding to app');
    setFlowState('ready');
  }, []);

  // Determine flow state based on auth state
  useEffect(() => {
    switch (authState) {
      case 'authenticated':
        // Check if migration is needed
        setFlowState('migration_required');
        break;
      case 'anonymous':
        setFlowState('ready');
        break;
      default:
        setFlowState('loading');
    }
  }, [authState]);

  // Notify parent of state changes
  useEffect(() => {
    onStateChange?.(flowState);
  }, [flowState, onStateChange]);

  // Render based on flow state
  switch (flowState) {
    case 'migration_required':
      return (
        <MainContainer key="AuthFlowManager Migration State">
          <MigrationHandler onMigrationComplete={handleMigrationComplete} />
        </MainContainer>
      );

    case 'ready':
      return <>{children}</>;

    default:
      return (
        <MainContainer key="AuthFlowManager Default State">
          <LoadingContainer>
            <ActivityIndicator size="large" />
            <Text>Initializing...</Text>
          </LoadingContainer>
        </MainContainer>
      );
  }
};
