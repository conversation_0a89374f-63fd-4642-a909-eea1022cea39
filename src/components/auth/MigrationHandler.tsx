import AsyncStorage from '@react-native-async-storage/async-storage';
import React, { useCallback, useEffect, useState } from 'react';
import { ActivityIndicator } from 'react-native';
import { callApi } from '../../api/client';
import { ANONYMOUS_USER_KEY, useAuth } from '../../context/AuthContext';
import { useManager } from '../../context/ManagerContext';
import { useErrorHandler } from '../../hooks/useErrorHandler';
import { CrossPlatformAlert } from '../CrossPlatformAlert';
import { MainContainer } from '../MainContainer';
import { MigrationConflictsModal } from '../MigrationConflictsModal';
import { Text } from '../Text';
import { LoadingContainer } from '../TransferSharedStyles';

interface TeamData {
  teamId: string;
  teamName: string;
  gameworldId: string;
  balance: number;
  played: number;
  points: number;
  wins: number;
  draws: number;
  losses: number;
  managerStats: {
    scoutTokens: number;
    superScoutTokens: number;
    magicSponges: number;
    cardAppeals: number;
    trainingBoosts: number;
    loginStreak: number;
    wins: number;
    defeats: number;
    trophies: number;
  };
}

interface MigrationConflictsResponse {
  hasConflicts: boolean;
  guestData?: TeamData;
  authenticatedData?: TeamData;
}

interface MigrationHandlerProps {
  onMigrationComplete: () => void;
}

/**
 * MigrationHandler handles only the migration logic for authenticated users.
 * Single responsibility: Check for and resolve migration conflicts.
 */
export const MigrationHandler: React.FC<MigrationHandlerProps> = ({ onMigrationComplete }) => {
  const { logout } = useAuth();
  const { refreshAllData } = useManager();
  const { errorState, showError, hideError, retry } = useErrorHandler();
  const [isLoading, setIsLoading] = useState(true);
  const [conflictsData, setConflictsData] = useState<MigrationConflictsResponse | null>(null);
  const [showConflictsModal, setShowConflictsModal] = useState(false);
  const [guestUserId, setGuestUserId] = useState<string>('');
  const [isResolvingConflict, setIsResolvingConflict] = useState(false);

  const handleAnonymousDataMigration = useCallback(
    async (anonymousUserId: string) => {
      try {
        const response = await callApi('/auth/migration/resolve', {
          method: 'POST',
          body: JSON.stringify({ guestUserId: anonymousUserId, keepGuestData: true }),
        });

        if (response.status === 200) {
          await AsyncStorage.removeItem(ANONYMOUS_USER_KEY);

          // Refresh all app data after successful migration
          await refreshAllData();

          setIsLoading(false);
          onMigrationComplete();
          return;
        } else {
          showError(response, 'Migration Failed', () =>
            handleAnonymousDataMigration(anonymousUserId)
          );
          onMigrationComplete();
        }
      } catch (error) {
        showError(error, 'Migration Error', () => handleAnonymousDataMigration(anonymousUserId));
        onMigrationComplete();
      }
    },
    [onMigrationComplete, refreshAllData, showError]
  );

  const checkMigrationNeeded = useCallback(async () => {
    try {
      setIsLoading(true);
      const hasAnonymousUser = await AsyncStorage.getItem(ANONYMOUS_USER_KEY);

      // no migration needed if we don't have a guest user
      if (!hasAnonymousUser) {
        setIsLoading(false);
        onMigrationComplete();
        return;
      }

      // For new authenticated users with guest data, check if we need to migrate the data
      const result = await callApi<MigrationConflictsResponse>(
        `/auth/migration/conflicts?guestUserId=${hasAnonymousUser}`
      );

      if (result.hasConflicts) {
        // Show conflict resolution modal
        setConflictsData(result);
        setGuestUserId(hasAnonymousUser!);
        setShowConflictsModal(true);
        setIsLoading(false);
      } else if (result.authenticatedData) {
        // User already has authenticated data, proceed
        await AsyncStorage.removeItem(ANONYMOUS_USER_KEY);
        setIsLoading(false);
        onMigrationComplete();
        return;
      } else {
        // Migrate anonymous data to authenticated account (this is the preferred path for new signups)
        await handleAnonymousDataMigration(hasAnonymousUser);
        setIsLoading(false);
        onMigrationComplete();
        return;
      }
    } catch (error) {
      showError(error, 'Migration Check Failed', () => checkMigrationNeeded());
      onMigrationComplete();
    } finally {
      setIsLoading(false);
    }
  }, [handleAnonymousDataMigration, onMigrationComplete, showError]);

  useEffect(() => {
    checkMigrationNeeded();
  }, [checkMigrationNeeded]);

  const handleConflictResolution = async (keepGuestData: boolean) => {
    if (!guestUserId || !conflictsData) return;

    setIsResolvingConflict(true);
    try {
      const response = await callApi('/auth/migration/resolve', {
        method: 'POST',
        body: JSON.stringify({
          guestUserId,
          keepGuestData,
        }),
      });

      if (response.status === 200) {
        setShowConflictsModal(false);
        setConflictsData(null);
        await AsyncStorage.removeItem(ANONYMOUS_USER_KEY);

        // Refresh all app data after successful migration
        await refreshAllData();

        setIsLoading(false);
        onMigrationComplete();
        return;
      } else {
        showError(response, 'Migration Failed');
        await logout();
      }
    } catch (error) {
      showError(error, 'Migration Error');
      await logout();
    } finally {
      setIsResolvingConflict(false);
    }
  };

  const handleCancelConflictResolution = async () => {
    setShowConflictsModal(false);
    setConflictsData(null);
    await logout();
  };

  if (isLoading) {
    return (
      <MainContainer key="MigrationHandler Loading">
        <LoadingContainer>
          <ActivityIndicator size="large" />
          <Text>Checking account data...</Text>
        </LoadingContainer>
      </MainContainer>
    );
  }

  return (
    <>
      <MigrationConflictsModal
        visible={showConflictsModal}
        guestData={conflictsData?.guestData}
        authenticatedData={conflictsData?.authenticatedData}
        guestUserId={guestUserId}
        onSelectGuest={() => handleConflictResolution(true)}
        onSelectAuthenticated={() => handleConflictResolution(false)}
        onCancel={handleCancelConflictResolution}
        isResolving={isResolvingConflict}
      />

      <CrossPlatformAlert
        visible={errorState.visible}
        title={errorState.title}
        message={errorState.message}
        buttons={[
          ...(errorState.canRetry
            ? [
                {
                  text: 'Retry',
                  onPress: retry,
                },
              ]
            : []),
          {
            text: 'OK',
            onPress: hideError,
          },
        ]}
        onDismiss={hideError}
      />
    </>
  );
};
