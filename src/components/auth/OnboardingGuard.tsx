import React, { useEffect, useState } from 'react';
import { ActivityIndicator } from 'react-native';
import { useOnboarding } from '../../hooks/useOnboarding';
import { logger } from '../../utils/logger';
import { Text } from '../Text';
import { LoadingContainer } from '../TransferSharedStyles';

interface OnboardingGuardProps {
  children: React.ReactNode;
}

/**
 * OnboardingGuard ensures new users go through the complete onboarding flow.
 * Single responsibility: Check onboarding status and redirect if needed.
 */
export const OnboardingGuard: React.FC<OnboardingGuardProps> = ({ children }) => {
  const { hasOnboarded, isLoading, navigateToCurrentStep } = useOnboarding();
  const [hasNavigated, setHasNavigated] = useState(false);

  useEffect(() => {
    if (!isLoading && !hasOnboarded && !hasNavigated) {
      logger.log('User has not completed onboarding, redirecting to current step');
      navigateToCurrentStep();
      setHasNavigated(true);
    }
  }, [isLoading, hasOnboarded, navigateToCurrentStep, hasNavigated]);

  // Show loading while checking onboarding status
  if (isLoading) {
    return (
      <LoadingContainer key={'OnboardingGuard Loading'}>
        <ActivityIndicator size="large" />
        <Text>Checking onboarding status...</Text>
      </LoadingContainer>
    );
  }

  // If user hasn't onboarded, show loading while redirecting
  /*if (!hasOnboarded) {
    return (
      <LoadingContainer key={'OnboardingGuard Redirecting'}>
        <ActivityIndicator size="large" />
        <Text>Setting up your experience...</Text>
      </LoadingContainer>
    );
  }*/

  // User has completed onboarding, show the app
  return <>{children}</>;
};
