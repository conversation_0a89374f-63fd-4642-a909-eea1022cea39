import { Hub } from '@aws-amplify/core';
import AsyncStorage from '@react-native-async-storage/async-storage';
import * as Sentry from '@sentry/react-native';
import { fetchAuthSession, getCurrentUser, signOut } from 'aws-amplify/auth';
import React, {
  createContext,
  ReactNode,
  useCallback,
  useContext,
  useEffect,
  useMemo,
  useState,
} from 'react';
import { AppState, Platform } from 'react-native';
import { logger } from '../utils/logger';
import { GAMEWORLD_ID_KEY, MANAGER_ID_KEY, TEAM_ID_KEY } from './ManagerContext';

export type AuthState = 'authenticated' | 'anonymous' | 'unauthenticated';

/**
 * AuthContextType defines the shape of the authentication context.
 */
interface AuthContextType {
  isAuthenticated: boolean;
  isAnonymous: boolean;
  authState: AuthState;
  isLoading: boolean;
  logout: () => Promise<void>;
  signInAnonymously: () => Promise<void>;
  upgradeAnonymousUser: () => Promise<void>;
}

const AuthContext = createContext<AuthContextType | undefined>(undefined);

export const ANONYMOUS_USER_KEY = 'anonymous_user_id';
export const LAST_LOGIN_TYPE = 'last_login_type';

/**
 * Helper to set authentication state in a consistent way.
 */
function setAuthStateHelpers(
  setIsAuthenticated: (v: boolean) => void,
  setIsAnonymous: (v: boolean) => void,
  setAuthState: (v: AuthState) => void
) {
  return {
    setAuthenticated: async () => {
      setIsAuthenticated(true);
      setIsAnonymous(false);
      setAuthState('authenticated');
      await AsyncStorage.setItem(LAST_LOGIN_TYPE, 'authenticated');
    },
    setAnonymous: async () => {
      setIsAuthenticated(false);
      setIsAnonymous(true);
      setAuthState('anonymous');
      await AsyncStorage.setItem(LAST_LOGIN_TYPE, 'anonymous');
    },
    setUnauthenticated: async () => {
      setIsAuthenticated(false);
      setIsAnonymous(false);
      setAuthState('unauthenticated');
      await AsyncStorage.setItem(LAST_LOGIN_TYPE, 'unauthenticated');
    },
  };
}

export function AuthProvider({ children }: { children: ReactNode }) {
  const [isAuthenticated, setIsAuthenticated] = useState(false);
  const [isAnonymous, setIsAnonymous] = useState(false);
  const [authState, setAuthState] = useState<AuthState>('unauthenticated');
  const [isLoading, setIsLoading] = useState(true);

  // Memoize the state setters to prevent unnecessary re-renders
  const { setAuthenticated, setAnonymous, setUnauthenticated } = useMemo(
    () => setAuthStateHelpers(setIsAuthenticated, setIsAnonymous, setAuthState),
    []
  );

  /**
   * Returns the anonymous user ID from storage or session.
   */
  const getAnonymousUserId = useCallback(async () => {
    try {
      const existingId = await AsyncStorage.getItem(ANONYMOUS_USER_KEY);
      if (existingId) return existingId;
      const session = await fetchAuthSession();
      logger.log('Guest session:', session);
      return session.identityId;
    } catch (error) {
      logger.error('Failed to fetch anonymous user session:', error);
      return null;
    }
  }, []);

  /**
   * Checks and sets the current authentication state.
   */
  const checkAuth = useCallback(async () => {
    try {
      const user = await getCurrentUser();
      if (user) {
        await setAuthenticated();
      } else {
        const lastLoginType = await AsyncStorage.getItem(LAST_LOGIN_TYPE);
        if (lastLoginType === 'anonymous') {
          const anonymousId = await AsyncStorage.getItem(ANONYMOUS_USER_KEY);
          if (anonymousId) {
            await setAnonymous();
          } else {
            await setUnauthenticated();
          }
        } else {
          await setUnauthenticated();
        }
      }
    } catch {
      const lastLoginType = await AsyncStorage.getItem(LAST_LOGIN_TYPE);
      if (lastLoginType === 'anonymous') {
        const anonymousId = await AsyncStorage.getItem(ANONYMOUS_USER_KEY);
        if (anonymousId) {
          await setAnonymous();
        } else {
          await setUnauthenticated();
        }
      } else {
        await setUnauthenticated();
      }
    } finally {
      setIsLoading(false);
    }
  }, [setAuthenticated, setAnonymous, setUnauthenticated]);

  // Only run checkAuth once on component mount
  useEffect(() => {
    checkAuth();
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []); // Empty dependency array - only run once on mount

  // Proactively refresh tokens on app foreground for authenticated and anonymous users
  useEffect(() => {
    const sub = AppState.addEventListener('change', async (state) => {
      if (state !== 'active') return;
      try {
        const lastLoginType = await AsyncStorage.getItem(LAST_LOGIN_TYPE);
        if (lastLoginType === 'authenticated') {
          logger.log('App foregrounded: forcing token refresh (authenticated)');
          await fetchAuthSession({ forceRefresh: true });
        } else if (lastLoginType === 'anonymous') {
          logger.log('App foregrounded: refreshing anonymous credentials');
          try {
            const session = await fetchAuthSession({ forceRefresh: true });
            const newId = (session as any)?.identityId;
            const stored = await AsyncStorage.getItem(ANONYMOUS_USER_KEY);
            if (newId && stored && newId !== stored) {
              logger.warn('Foreground refresh returned different anonymous identity; keeping stored ID');
            }
          } catch (e) {
            // swallow here; API layer will also refresh on demand
          }
        }
      } catch (e: any) {
        logger.error('Foreground token refresh failed:', e);
        const name = e?.name || e?.code;
        const message = e?.message || '';
        if (
          name === 'NotAuthorizedException' ||
          name === 'InvalidParameterException' ||
          /invalid\s*refresh\s*token/i.test(String(message)) ||
          /refresh token has expired/i.test(String(message))
        ) {
          // Refresh token invalid; sign out to reset state
          try {
            await signOut();
          } catch (signOutErr) {
            logger.error('Sign out after foreground refresh failure failed:', signOutErr);
          }
        }
      }
    });
    return () => sub.remove();
  }, []);

  useEffect(() => {
    // Listen for auth events
    const listener = Hub.listen('auth', async ({ payload }) => {
      const { event } = payload;
      logger.log('Auth event in AuthProvider:', event);

      // Use setTimeout to ensure state updates happen after render
      if (event === 'signedIn') {
        setTimeout(async () => {
          Sentry.setUser({
            id: payload.data.userId,
          });
          await setAuthenticated();
          setIsLoading(false);
        }, 0);
      }
      if (event === 'signedOut') {
        setTimeout(async () => {
          await setUnauthenticated();
          Sentry.setUser(null);
          setIsLoading(false);
        }, 0);
      }
    });
    return () => listener();
  }, [setAuthenticated, setUnauthenticated]);

  /**
   * Signs in the user anonymously and stores the anonymous user ID.
   */
  const signInAnonymously = async () => {
    try {
      logger.log('Signing in anonymously...');
      setIsLoading(true);
      const anonymousId = await getAnonymousUserId();
      if (!anonymousId) throw new Error('Failed to get anonymous user ID');
      await AsyncStorage.setItem(ANONYMOUS_USER_KEY, anonymousId);
      Sentry.setUser({
        id: anonymousId,
      });
      await setAnonymous();
      logger.log('Anonymous sign-in successful:', anonymousId);
    } catch (error) {
      logger.error('Anonymous sign-in failed:', error);
      await setUnauthenticated();
    } finally {
      setIsLoading(false);
    }
  };

  /**
   * Upgrades an anonymous user to a full account (to be implemented).
   */
  const upgradeAnonymousUser = async () => {
    try {
      logger.log('Upgrading anonymous user...');
      // TODO: Implement upgrade flow
    } catch (error) {
      logger.error('Failed to upgrade anonymous user:', error);
    }
  };

  /**
   * Logs out the current user.
   */
  const logout = async () => {
    try {
      logger.log('Logging out...');
      Sentry.setUser(null);
      setIsLoading(true);
      if (isAuthenticated) {
        // hack because of the SSO redirect causing full screen refresh meaning this doesn't happen in the ManagerContext where it should
        if (Platform.OS === 'web') {
          await AsyncStorage.multiRemove([MANAGER_ID_KEY, TEAM_ID_KEY, GAMEWORLD_ID_KEY]);
        }
        await signOut();
      }
      await setUnauthenticated();
    } catch (error) {
      logger.error('Logout failed:', error);
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <AuthContext.Provider
      value={{
        isAuthenticated,
        isAnonymous,
        authState,
        isLoading,
        logout,
        signInAnonymously,
        upgradeAnonymousUser,
      }}
    >
      {children}
    </AuthContext.Provider>
  );
}

/**
 * Custom hook to access authentication context.
 */
export const useAuth = () => {
  const context = useContext(AuthContext);
  if (!context) {
    throw new Error('useAuth must be used within AuthProvider');
  }
  return context;
};
