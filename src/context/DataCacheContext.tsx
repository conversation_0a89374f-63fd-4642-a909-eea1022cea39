import React, { createContext, useCallback, useContext, useReducer } from 'react';
import { ActiveTransfer } from '../hooks/useMyBidsPlayers';
import { League, ScoutedPlayer } from '../hooks/useQueries';
import { Fixture } from '../models/fixture';
import { Manager } from '../models/manager';
import { DetailedPlayerData, Player, TransferListPlayer } from '../models/player';
import { Team } from '../models/team';
import {
  DataCache,
  createEmptyCache,
  findPlayerInCache,
  getPlayerDetailsFromCache,
  updateManagerInCache,
  updatePlayerDetailsInCache,
  updatePlayerInCache,
  updateTeamInCache,
} from '../utils/cacheUtils';
import { logger } from '../utils/logger';

// Action types for cache reducer
type CacheAction =
  | { type: 'SET_MANAGER'; payload: Manager }
  | { type: 'UPDATE_MANAGER'; payload: Partial<Manager> }
  | { type: 'SET_TEAM'; payload: Team }
  | { type: 'UPDATE_TEAM'; payload: Partial<Team> }
  | { type: 'SET_TEAM_PLAYERS'; payload: Player[] }
  | { type: 'SET_TRANSFER_LIST_PLAYERS'; payload: TransferListPlayer[] }
  | { type: 'ADD_TRANSFER_LIST_PLAYERS'; payload: TransferListPlayer[] }
  | { type: 'SET_SCOUTED_PLAYERS'; payload: ScoutedPlayer[] }
  | { type: 'ADD_SCOUTED_PLAYERS'; payload: ScoutedPlayer[] }
  | { type: 'SET_MY_BIDS_PLAYERS'; payload: TransferListPlayer[] }
  | { type: 'SET_MY_ACTIVE_TRANSFERS'; payload: ActiveTransfer[] }
  | { type: 'SET_FIXTURES'; payload: Fixture[] }
  | { type: 'SET_LEAGUE'; payload: League }
  | { type: 'SET_PLAYER_DETAILS'; payload: DetailedPlayerData }
  | {
      type: 'UPDATE_PLAYER';
      payload: Partial<Player | TransferListPlayer | ScoutedPlayer> & { playerId: string };
    }
  | { type: 'CLEAR_CACHE' };

// Cache reducer
const cacheReducer = (state: DataCache, action: CacheAction): DataCache => {
  switch (action.type) {
    case 'SET_MANAGER':
      return {
        ...state,
        manager: action.payload,
        lastUpdated: { ...state.lastUpdated, manager: Date.now() },
      };

    case 'UPDATE_MANAGER':
      return updateManagerInCache(state, action.payload);

    case 'SET_TEAM':
      return {
        ...state,
        team: action.payload,
        lastUpdated: { ...state.lastUpdated, team: Date.now() },
      };

    case 'UPDATE_TEAM':
      return updateTeamInCache(state, action.payload);

    case 'SET_TEAM_PLAYERS':
      return {
        ...state,
        players: { ...state.players, teamPlayers: action.payload },
        lastUpdated: { ...state.lastUpdated, players: Date.now() },
      };

    case 'SET_TRANSFER_LIST_PLAYERS':
      return {
        ...state,
        players: { ...state.players, transferListPlayers: action.payload },
        lastUpdated: { ...state.lastUpdated, players: Date.now() },
      };

    case 'ADD_TRANSFER_LIST_PLAYERS':
      // Filter out duplicates before adding
      const existingTransferIds = new Set(state.players.transferListPlayers.map((p) => p.playerId));
      const newTransferPlayers = action.payload.filter((p) => !existingTransferIds.has(p.playerId));
      return {
        ...state,
        players: {
          ...state.players,
          transferListPlayers: [...state.players.transferListPlayers, ...newTransferPlayers],
        },
        lastUpdated: { ...state.lastUpdated, players: Date.now() },
      };

    case 'SET_SCOUTED_PLAYERS':
      return {
        ...state,
        players: { ...state.players, scoutedPlayers: action.payload },
        lastUpdated: { ...state.lastUpdated, players: Date.now() },
      };

    case 'ADD_SCOUTED_PLAYERS':
      // Filter out duplicates before adding
      const existingScoutedIds = new Set(state.players.scoutedPlayers.map((p) => p.playerId));
      const newScoutedPlayers = action.payload.filter((p) => !existingScoutedIds.has(p.playerId));
      return {
        ...state,
        players: {
          ...state.players,
          scoutedPlayers: [...state.players.scoutedPlayers, ...newScoutedPlayers],
        },
        lastUpdated: { ...state.lastUpdated, players: Date.now() },
      };

    case 'SET_MY_BIDS_PLAYERS':
      return {
        ...state,
        players: { ...state.players, myBidsPlayers: action.payload },
        lastUpdated: { ...state.lastUpdated, players: Date.now() },
      };

    case 'SET_MY_ACTIVE_TRANSFERS':
      return {
        ...state,
        players: { ...state.players, myActiveTransfers: action.payload },
        lastUpdated: { ...state.lastUpdated, players: Date.now() },
      };

    case 'SET_FIXTURES':
      return {
        ...state,
        fixtures: action.payload,
        lastUpdated: { ...state.lastUpdated, fixtures: Date.now() },
      };

    case 'SET_LEAGUE':
      return {
        ...state,
        league: action.payload,
        lastUpdated: { ...state.lastUpdated, league: Date.now() },
      };

    case 'SET_PLAYER_DETAILS':
      return updatePlayerDetailsInCache(state, action.payload);

    case 'UPDATE_PLAYER':
      return updatePlayerInCache(state, action.payload);

    case 'CLEAR_CACHE':
      return createEmptyCache();

    default:
      return state;
  }
};

interface DataCacheContextType {
  cache: DataCache;
  // Manager actions
  setManager: (manager: Manager) => void;
  updateManager: (updates: Partial<Manager>) => void;
  // Team actions
  setTeam: (team: Team) => void;
  updateTeam: (updates: Partial<Team>) => void;
  // Player actions
  setTeamPlayers: (players: Player[]) => void;
  setTransferListPlayers: (players: TransferListPlayer[]) => void;
  addTransferListPlayers: (players: TransferListPlayer[]) => void;
  setScoutedPlayers: (players: ScoutedPlayer[]) => void;
  addScoutedPlayers: (players: ScoutedPlayer[]) => void;
  setMyBidsPlayers: (players: TransferListPlayer[]) => void;
  setMyActiveTransfers: (transfers: ActiveTransfer[]) => void;
  updatePlayer: (
    updates: Partial<Player | TransferListPlayer | ScoutedPlayer> & { playerId: string }
  ) => void;
  // Player details actions
  setPlayerDetails: (playerDetails: DetailedPlayerData) => void;
  getPlayerDetails: (playerId: string) => DetailedPlayerData | null;
  // Fixtures actions
  setFixtures: (fixtures: Fixture[]) => void;
  // League actions
  setLeague: (league: League) => void;
  // Utility functions
  findPlayer: (playerId: string) => Player | TransferListPlayer | ScoutedPlayer | null;
  clearCache: () => void;
}

const DataCacheContext = createContext<DataCacheContextType | undefined>(undefined);

export const DataCacheProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const [cache, dispatch] = useReducer(cacheReducer, createEmptyCache());
  logger.log('DataCacheProvider mounted or remounted');
  // Manager actions
  const setManager = useCallback((manager: Manager) => {
    logger.log('Cache: Setting manager data');
    dispatch({ type: 'SET_MANAGER', payload: manager });
  }, []);

  const updateManager = useCallback((updates: Partial<Manager>) => {
    logger.log('Cache: Updating manager data', updates);
    dispatch({ type: 'UPDATE_MANAGER', payload: updates });
  }, []);

  // Team actions
  const setTeam = useCallback((team: Team) => {
    logger.log('Cache: Setting team data');
    dispatch({ type: 'SET_TEAM', payload: team });
  }, []);

  const updateTeam = useCallback((updates: Partial<Team>) => {
    logger.log('Cache: Updating team data', updates);
    dispatch({ type: 'UPDATE_TEAM', payload: updates });
  }, []);

  // Player actions
  const setTeamPlayers = useCallback((players: Player[]) => {
    logger.log('Cache: Setting team players', players.length);
    dispatch({ type: 'SET_TEAM_PLAYERS', payload: players });
  }, []);

  const setTransferListPlayers = useCallback((players: TransferListPlayer[]) => {
    logger.log('Cache: Setting transfer list players', players.length);
    dispatch({ type: 'SET_TRANSFER_LIST_PLAYERS', payload: players });
  }, []);

  const addTransferListPlayers = useCallback((players: TransferListPlayer[]) => {
    logger.log('Cache: Adding transfer list players', players.length);
    dispatch({ type: 'ADD_TRANSFER_LIST_PLAYERS', payload: players });
  }, []);

  const setScoutedPlayers = useCallback((players: ScoutedPlayer[]) => {
    logger.log('Cache: Setting scouted players', players.length);
    dispatch({ type: 'SET_SCOUTED_PLAYERS', payload: players });
  }, []);

  const addScoutedPlayers = useCallback((players: ScoutedPlayer[]) => {
    logger.log('Cache: Adding scouted players', players.length);
    dispatch({ type: 'ADD_SCOUTED_PLAYERS', payload: players });
  }, []);

  const setMyBidsPlayers = useCallback((players: TransferListPlayer[]) => {
    logger.log('Cache: Setting my bids players', players.length);
    dispatch({ type: 'SET_MY_BIDS_PLAYERS', payload: players });
  }, []);

  const setMyActiveTransfers = useCallback((transfers: ActiveTransfer[]) => {
    logger.log('Cache: Setting my active transfers', transfers.length);
    dispatch({ type: 'SET_MY_ACTIVE_TRANSFERS', payload: transfers });
  }, []);

  const setFixtures = useCallback((fixtures: Fixture[]) => {
    logger.log('Cache: Setting fixtures', fixtures.length);
    dispatch({ type: 'SET_FIXTURES', payload: fixtures });
  }, []);

  const setLeague = useCallback((league: League) => {
    logger.log('Cache: Setting league data');
    dispatch({ type: 'SET_LEAGUE', payload: league });
  }, []);

  const updatePlayer = useCallback((updates: Partial<Player> & { playerId: string }) => {
    logger.log('Cache: Updating player', updates.playerId, updates);
    dispatch({ type: 'UPDATE_PLAYER', payload: updates });
  }, []);

  // Player details actions
  const setPlayerDetails = useCallback((playerDetails: DetailedPlayerData) => {
    logger.log('Cache: Setting player details', playerDetails.playerId);
    dispatch({ type: 'SET_PLAYER_DETAILS', payload: playerDetails });
  }, []);

  const getPlayerDetails = useCallback(
    (playerId: string) => {
      return getPlayerDetailsFromCache(cache, playerId);
    },
    [cache]
  );

  // Utility functions
  const findPlayer = useCallback(
    (playerId: string) => {
      return findPlayerInCache(cache, playerId);
    },
    [cache]
  );

  const clearCache = useCallback(() => {
    logger.log('Cache: Clearing all cache data');
    dispatch({ type: 'CLEAR_CACHE' });
  }, []);

  return (
    <DataCacheContext.Provider
      value={{
        cache,
        setManager,
        updateManager,
        setTeam,
        updateTeam,
        setTeamPlayers,
        setTransferListPlayers,
        addTransferListPlayers,
        setScoutedPlayers,
        addScoutedPlayers,
        setMyBidsPlayers,
        setMyActiveTransfers,
        setFixtures,
        setLeague,
        updatePlayer,
        setPlayerDetails,
        getPlayerDetails,
        findPlayer,
        clearCache,
      }}
    >
      {children}
    </DataCacheContext.Provider>
  );
};

export const useDataCache = () => {
  const context = useContext(DataCacheContext);
  if (context === undefined) {
    throw new Error('useDataCache must be used within a DataCacheProvider');
  }
  return context;
};
