import AsyncStorage from '@react-native-async-storage/async-storage';
import { useQueryClient } from '@tanstack/react-query';
import React, { createContext, useCallback, useContext, useEffect } from 'react';
import { callApi } from '../api/client';
import { useCachedManager, useCachedTeam } from '../hooks/useCachedData';
import { Manager } from '../models/manager';
import { Team } from '../models/team';
import { logger } from '../utils/logger';
import { useDataCache } from './DataCacheContext';

// Storage keys
export const MANAGER_ID_KEY = '@manager_id';
export const TEAM_ID_KEY = '@team_id';
export const GAMEWORLD_ID_KEY = '@gameworld_id';
export const NOTIFICATION_SETTINGS_SEEN_KEY = '@notification_settings_seen';
export const CACHED_FIRST_NAME_KEY = '@cached_first_name';
export const CACHED_LAST_NAME_KEY = '@cached_last_name';
export const PROFILE_NEEDS_UPDATE_KEY = '@profile_needs_update';
export const LAST_READ_MESSAGE_ID_KEY = '@last_read_message_id';
export const LAST_READ_MESSAGE_DATE_KEY = '@last_read_message_date';
export const HAS_ONBOARDED_KEY = '@has_onboarded';

interface ManagerContextType {
  manager: Manager | null;
  team: Team | null;
  loading: boolean;
  error: Error | null;
  storedIds: {
    managerId?: string;
    teamId?: string;
    gameworldId?: string;
  };
  refreshManager: () => Promise<void>;
  refreshAllData: () => Promise<void>;
  updateManager: (updates: Partial<Manager>) => void;
  updateTeam: (updates: Partial<Team>) => void;
  logout: () => Promise<void>;
}

const ManagerContext = createContext<ManagerContextType | undefined>(undefined);

async function saveManagerProfile(manager: Manager) {
  const [cachedFirstName, cachedLastName] = await Promise.all([
    AsyncStorage.getItem(CACHED_FIRST_NAME_KEY),
    AsyncStorage.getItem(CACHED_LAST_NAME_KEY),
  ]);

  if (cachedFirstName && cachedLastName) {
    try {
      // Auto-save the cached names to the API
      await callApi('/manager/name', {
        method: 'PUT',
        body: JSON.stringify({
          firstName: cachedFirstName.trim(),
          lastName: cachedLastName.trim(),
        }),
      });

      manager.firstName = cachedFirstName;
      manager.lastName = cachedLastName;

      // Clear the flag since we've updated the profile
      await AsyncStorage.setItem(PROFILE_NEEDS_UPDATE_KEY, 'false');
    } catch (error) {
      logger.error('Failed to auto-save profile:', error);
    }

    return manager;
  }
}

export const ManagerProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const { clearCache } = useDataCache();
  const queryClient = useQueryClient();
  const [isLoggingOut, setIsLoggingOut] = React.useState(false);
  // State to store locally retrieved IDs (useful for offline functionality)
  const [storedIds, setStoredIds] = React.useState<{
    managerId?: string;
    teamId?: string;
    gameworldId?: string;
  }>({});

  const logout = useCallback(async () => {
    setIsLoggingOut(true);
    setStoredIds({});
    await AsyncStorage.multiRemove([
      MANAGER_ID_KEY,
      TEAM_ID_KEY,
      GAMEWORLD_ID_KEY,
      CACHED_FIRST_NAME_KEY,
      CACHED_LAST_NAME_KEY,
      LAST_READ_MESSAGE_ID_KEY,
      LAST_READ_MESSAGE_DATE_KEY,
    ]);
    try {
      // Cancel any in-flight queries and prevent stale writes completing during logout
      await queryClient.cancelQueries();
    } catch (e) {
      logger.warn?.('cancelQueries failed during logout', e as any);
    }
    clearCache(); // Clear all cached data
    queryClient.clear(); // Clear React Query cache
    logger.log('User logged out and cache cleared');
    // isLoggingOut stays true until unmount (auth guard removes this tree)
  }, [clearCache, queryClient]);

  // Load stored IDs from AsyncStorage on initial mount
  useEffect(() => {
    const loadStoredIds = async () => {
      try {
        const [managerId, teamId, gameworldId] = await Promise.all([
          AsyncStorage.getItem(MANAGER_ID_KEY),
          AsyncStorage.getItem(TEAM_ID_KEY),
          AsyncStorage.getItem(GAMEWORLD_ID_KEY),
        ]);

        if (managerId && teamId && gameworldId) {
          setStoredIds({ managerId, teamId, gameworldId });
          logger.log('Loaded stored IDs from AsyncStorage');
        }
      } catch (error) {
        logger.error('Error loading stored IDs from AsyncStorage:', error);
      }
    };

    loadStoredIds();
  }, []);

  // Use cached data hooks instead of direct React Query
  const {
    manager,
    isLoading: managerLoading,
    error: managerError,
    refetch,
    updateManager: updateManagerCache,
  } = useCachedManager(!isLoggingOut);

  const {
    team,
    isLoading: teamLoading,
    error: teamError,
    updateTeam: updateTeamCache,
  } = useCachedTeam(manager?.gameworldId, manager?.team?.teamId);

  // Save manager and team IDs to AsyncStorage when they are retrieved
  useEffect(() => {
    const saveManagerData = async () => {
      if (manager?.managerId && manager?.team && manager?.gameworldId) {
        try {
          await AsyncStorage.setItem(MANAGER_ID_KEY, manager.managerId);
          await AsyncStorage.setItem(TEAM_ID_KEY, manager.team.teamId);
          await AsyncStorage.setItem(GAMEWORLD_ID_KEY, manager.gameworldId);
          logger.log('Saved manager data to AsyncStorage');
        } catch (error) {
          logger.error('Error saving manager data to AsyncStorage:', error);
        }

        // save the players name if necessary
        const needsUpdate = await AsyncStorage.getItem(PROFILE_NEEDS_UPDATE_KEY);
        if (needsUpdate === 'true') {
          await saveManagerProfile(manager);
        }
      }
    };

    saveManagerData();
  }, [manager]);

  const refreshManager = async () => {
    await refetch(); // Trigger a refetch of the manager data
  };

  const refreshAllData = async () => {
    logger.log('Refreshing all app data after migration');

    // Clear all cached data
    clearCache();

    // Clear stored IDs to force fresh fetch
    setStoredIds({});
    await AsyncStorage.multiRemove([MANAGER_ID_KEY, TEAM_ID_KEY, GAMEWORLD_ID_KEY]);

    // Trigger fresh fetch of manager data
    await refetch();

    logger.log('All app data refreshed successfully');
  };

  const updateManager = (updates: Partial<Manager>) => {
    updateManagerCache(updates);
  };

  const updateTeam = (updates: Partial<Team>) => {
    updateTeamCache(updates);
  };

  return (
    <ManagerContext.Provider
      value={{
        manager: manager || null,
        team: team || null,
        loading: managerLoading || teamLoading,
        error: managerError || teamError || null,
        storedIds,
        refreshManager,
        refreshAllData,
        updateManager,
        updateTeam,
        logout,
      }}
    >
      {children}
    </ManagerContext.Provider>
  );
};

export const useManager = () => {
  const context = useContext(ManagerContext);
  if (context === undefined) {
    throw new Error('useManager must be used within a ManagerProvider');
  }
  return context;
};
