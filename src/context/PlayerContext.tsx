import React, { useContext } from 'react';
import {
  PlayerActionsActions,
  PlayerActionsState,
  usePlayerActions,
} from '../components/PlayerDetail/hooks/usePlayerActions';

interface PlayerContextType {
  playerActionsState: PlayerActionsState;
  playerActions: PlayerActionsActions;
}

const PlayerContext = React.createContext<PlayerContextType | undefined>(undefined);

export const PlayerProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const [playerActionsState, playerActions] = usePlayerActions();

  const providerValue: PlayerContextType = {
    playerActions,
    playerActionsState,
  };
  return <PlayerContext.Provider value={providerValue}>{children}</PlayerContext.Provider>;
};

export const usePlayerUtils = () => {
  const context = useContext(PlayerContext);
  if (context === undefined) {
    throw new Error('usePlayerUtils must be used within a PlayerProvider');
  }
  return context;
};
