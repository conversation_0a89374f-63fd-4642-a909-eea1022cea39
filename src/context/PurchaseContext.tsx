import React, { createContext, ReactNode, useContext, useEffect, useState } from 'react';
import { Platform } from 'react-native';
import Purchases, {
  CustomerInfo,
  PurchasesOffering,
  PurchasesPackage,
} from 'react-native-purchases';
import { logger } from '../utils/logger';
import { useManager } from './ManagerContext';

// RevenueCat API Keys - you'll need to replace these with your actual keys
const REVENUECAT_API_KEYS = {
  android: __DEV__ ? 'goog_vVSXnwAMyJuBdXZxaqsyXMhVVrQ' : 'goog_vVSXnwAMyJuBdXZxaqsyXMhVVrQ',
  ios: __DEV__ ? 'your_ios_test_key' : 'your_ios_prod_key',
};

export interface PurchaseContextType {
  offerings: PurchasesOffering | null;
  customerInfo: CustomerInfo | null;
  isLoading: boolean;
  error: string | null;
  purchasePackage: (packageToPurchase: PurchasesPackage) => Promise<boolean>;
  restorePurchases: () => Promise<boolean>;
  refreshOfferings: () => Promise<void>;
}

const PurchaseContext = createContext<PurchaseContextType | undefined>(undefined);

export const usePurchases = () => {
  const context = useContext(PurchaseContext);
  if (!context) {
    throw new Error('usePurchases must be used within a PurchaseProvider');
  }
  return context;
};

interface PurchaseProviderProps {
  children: ReactNode;
}

export const PurchaseProvider: React.FC<PurchaseProviderProps> = ({ children }) => {
  const { manager } = useManager();
  const [offerings, setOfferings] = useState<PurchasesOffering | null>(null);
  const [customerInfo, setCustomerInfo] = useState<CustomerInfo | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  // Initialize RevenueCat
  useEffect(() => {
    if (!manager?.managerId) return; // Only initialize when managerId is available

    const initializePurchases = async () => {
      try {
        setIsLoading(true);
        setError(null);

        // Configure RevenueCat
        const apiKey =
          Platform.OS === 'android' ? REVENUECAT_API_KEYS.android : REVENUECAT_API_KEYS.ios;

        if (!apiKey || apiKey.includes('your_')) {
          logger.warn('RevenueCat API key not configured');
          setError('RevenueCat not configured');
          setIsLoading(false);
          return;
        }

        Purchases.configure({ apiKey });

        // Set user ID if manager is available
        if (manager?.managerId) {
          await Purchases.logIn(manager.managerId);
          logger.log('RevenueCat user logged in:', manager.managerId);
        }

        // Get initial customer info and offerings
        const [initialCustomerInfo, initialOfferings] = await Promise.all([
          Purchases.getCustomerInfo(),
          Purchases.getOfferings(),
        ]);

        setCustomerInfo(initialCustomerInfo);
        setOfferings(initialOfferings.current);

        logger.log('RevenueCat initialized successfully');
      } catch (err) {
        const errorMessage = err instanceof Error ? err.message : 'Failed to initialize purchases';
        logger.error('RevenueCat initialization failed:', err);
        setError(errorMessage);
      } finally {
        setIsLoading(false);
      }
    };

    initializePurchases();
  }, [manager?.managerId]);

  const purchasePackage = async (packageToPurchase: PurchasesPackage): Promise<boolean> => {
    try {
      setIsLoading(true);
      setError(null);

      const { customerInfo: initialCustomerInfo } =
        await Purchases.purchasePackage(packageToPurchase);
      setCustomerInfo(initialCustomerInfo);

      logger.log('Purchase successful:', packageToPurchase.identifier);
      return true;
    } catch (err: any) {
      if (err.code === Purchases.PURCHASES_ERROR_CODE.PURCHASE_CANCELLED_ERROR) return false;
      const errorMessage = err instanceof Error ? err.message : 'Purchase failed';
      logger.error('Purchase failed:', err);
      setError(errorMessage);
      return false;
    } finally {
      setIsLoading(false);
    }
  };

  const restorePurchases = async (): Promise<boolean> => {
    try {
      setIsLoading(true);
      setError(null);

      const info = await Purchases.restorePurchases();
      setCustomerInfo(info);

      logger.log('Purchases restored successfully');
      return true;
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to restore purchases';
      logger.error('Restore purchases failed:', err);
      setError(errorMessage);
      return false;
    } finally {
      setIsLoading(false);
    }
  };

  const refreshOfferings = async (): Promise<void> => {
    try {
      setError(null);
      const purchasesOfferings = await Purchases.getOfferings();
      setOfferings(purchasesOfferings.current);
      logger.log('Offerings refreshed');
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to refresh offerings';
      logger.error('Refresh offerings failed:', err);
      setError(errorMessage);
    }
  };

  const value: PurchaseContextType = {
    offerings,
    customerInfo,
    isLoading,
    error,
    purchasePackage,
    restorePurchases,
    refreshOfferings,
  };

  return <PurchaseContext.Provider value={value}>{children}</PurchaseContext.Provider>;
};
