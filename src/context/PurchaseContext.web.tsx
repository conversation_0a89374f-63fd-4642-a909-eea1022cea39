import React, { createContext, ReactNode, useContext, useState } from 'react';

// Web fallback types
interface MockPurchasesPackage {
  identifier: string;
  product: {
    title: string;
    description: string;
    priceString: string;
  };
}

interface MockPurchasesOffering {
  availablePackages: MockPurchasesPackage[];
}

interface MockCustomerInfo {
  activeSubscriptions: string[];
}

export interface PurchaseContextType {
  offerings: MockPurchasesOffering | null;
  customerInfo: MockCustomerInfo | null;
  isLoading: boolean;
  error: string | null;
  purchasePackage: (packageToPurchase: MockPurchasesPackage) => Promise<boolean>;
  restorePurchases: () => Promise<boolean>;
  refreshOfferings: () => Promise<void>;
}

const PurchaseContext = createContext<PurchaseContextType | undefined>(undefined);

export const usePurchases = () => {
  const context = useContext(PurchaseContext);
  if (!context) {
    throw new Error('usePurchases must be used within a PurchaseProvider');
  }
  return context;
};

interface PurchaseProviderProps {
  children: ReactNode;
}

export const PurchaseProvider: React.FC<PurchaseProviderProps> = ({ children }) => {
  const [offerings] = useState<MockPurchasesOffering | null>(null);
  const [customerInfo] = useState<MockCustomerInfo | null>(null);
  const [isLoading] = useState(false);
  const [error] = useState<string | null>('In-app purchases not available on web');

  const purchasePackage = async (): Promise<boolean> => {
    return false;
  };

  const restorePurchases = async (): Promise<boolean> => {
    return false;
  };

  const refreshOfferings = async (): Promise<void> => {
    // No-op for web
  };

  const value: PurchaseContextType = {
    offerings,
    customerInfo,
    isLoading,
    error,
    purchasePackage,
    restorePurchases,
    refreshOfferings,
  };

  return <PurchaseContext.Provider value={value}>{children}</PurchaseContext.Provider>;
};
