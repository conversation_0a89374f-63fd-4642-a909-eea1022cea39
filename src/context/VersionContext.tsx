import * as Application from 'expo-application';
import Constants from 'expo-constants';
import React, {
  createContext,
  ReactNode,
  useCallback,
  useContext,
  useEffect,
  useState,
} from 'react';
import { Platform } from 'react-native';
import { checkAppVersion, VersionCheckResponse } from '../api/version';
import { UpdateModal } from '../components/UpdateModal';
import { logger } from '../utils/logger';

interface VersionContextType {
  isVersionCheckComplete: boolean;
  needsUpdate: boolean;
  forceUpdate: boolean;
  versionString?: string;
  checkVersion: () => Promise<void>;
}

const VersionContext = createContext<VersionContextType | undefined>(undefined);

interface VersionProviderProps {
  children: ReactNode;
}

export const VersionProvider: React.FC<VersionProviderProps> = ({ children }) => {
  const [isVersionCheckComplete, setIsVersionCheckComplete] = useState(false);
  const [needsUpdate, setNeedsUpdate] = useState(false);
  const [forceUpdate, setForceUpdate] = useState(false);
  const [versionString, setVersionString] = useState<string>();
  const [showUpdateModal, setShowUpdateModal] = useState(false);

  const getCurrentVersionCode = (): number => {
    // For Android, we need to get the version code (build number)
    // expo-constants provides this information
    const buildVersion = Application.nativeBuildVersion;

    if (buildVersion) {
      return Number(buildVersion);
    }

    // Fallback: try to parse from version string
    const version = Constants.expoConfig?.version || '1.0.0';
    const versionParts = version.split('.');

    // Convert version like "1.2.3" to a comparable number
    // This is a simple approach - you might want to use a more sophisticated versioning scheme
    const major = parseInt(versionParts[0] || '1', 10);
    const minor = parseInt(versionParts[1] || '0', 10);
    const patch = parseInt(versionParts[2] || '0', 10);

    return major * 10000 + minor * 100 + patch;
  };

  const checkVersion = useCallback(async (): Promise<void> => {
    // Only check version on Android
    if (Platform.OS !== 'android') {
      setIsVersionCheckComplete(true);
      return;
    }

    try {
      const currentVersion = getCurrentVersionCode();
      logger.log('Current version code:', currentVersion);

      const response: VersionCheckResponse = await checkAppVersion(currentVersion, 'android');

      logger.log('Version check response:', response);

      if (response.latest > currentVersion) {
        setNeedsUpdate(true);
        setForceUpdate(response.forceUpdate);
        setVersionString(response.versionString);
        setShowUpdateModal(true);
      } else {
        setIsVersionCheckComplete(true);
      }
    } catch (error) {
      logger.error('Version check failed:', error);
      // If version check fails, allow the app to continue
      setIsVersionCheckComplete(true);
    }
  }, []);

  const handleUpdateModalDismiss = () => {
    if (!forceUpdate) {
      setShowUpdateModal(false);
      setIsVersionCheckComplete(true);
    }
  };

  useEffect(() => {
    checkVersion();
  }, [checkVersion]);

  // If force update is required, don't render children until update
  if (needsUpdate && forceUpdate && !isVersionCheckComplete) {
    return (
      <UpdateModal
        visible={showUpdateModal}
        versionString={versionString || 'latest'}
        forceUpdate={forceUpdate}
        onDismiss={handleUpdateModalDismiss}
      />
    );
  }

  return (
    <VersionContext.Provider
      value={{
        isVersionCheckComplete,
        needsUpdate,
        forceUpdate,
        versionString,
        checkVersion,
      }}
    >
      {children}

      {/* Show update modal for optional updates */}
      {needsUpdate && !forceUpdate && (
        <UpdateModal
          visible={showUpdateModal}
          versionString={versionString || 'latest'}
          forceUpdate={forceUpdate}
          onDismiss={handleUpdateModalDismiss}
        />
      )}
    </VersionContext.Provider>
  );
};

export const useVersion = (): VersionContextType => {
  const context = useContext(VersionContext);
  if (!context) {
    throw new Error('useVersion must be used within a VersionProvider');
  }
  return context;
};
