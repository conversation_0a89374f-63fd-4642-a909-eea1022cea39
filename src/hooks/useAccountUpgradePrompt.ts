import AsyncStorage from '@react-native-async-storage/async-storage';
import { useCallback, useEffect, useState } from 'react';
import { useAuth } from '../context/AuthContext';
import { logger } from '../utils/logger';

const UPGRADE_PROMPT_KEYS = {
  LAST_SHOWN: 'upgrade_prompt_last_shown',
  SHOW_COUNT: 'upgrade_prompt_show_count',
  DISMISSED_COUNT: 'upgrade_prompt_dismissed_count',
  FIRST_LAUNCH: 'upgrade_prompt_first_launch',
};

interface UpgradePromptState {
  visible: boolean;
  trigger: 'manual' | 'achievement' | 'data_warning' | 'feature_unlock';
  title?: string;
  description?: string;
}

export const useAccountUpgradePrompt = () => {
  const { isAnonymous } = useAuth();
  const [promptState, setPromptState] = useState<UpgradePromptState>({
    visible: false,
    trigger: 'manual',
  });

  // Initialize first launch tracking
  useEffect(() => {
    const initializeTracking = async () => {
      try {
        const firstLaunch = await AsyncStorage.getItem(UPGRADE_PROMPT_KEYS.FIRST_LAUNCH);
        if (!firstLaunch) {
          await AsyncStorage.setItem(UPGRADE_PROMPT_KEYS.FIRST_LAUNCH, Date.now().toString());
        }
      } catch (error) {
        logger.error('Failed to initialize upgrade prompt tracking:', error);
      }
    };

    if (isAnonymous) {
      initializeTracking();
    }
  }, [isAnonymous]);

  // Check if we should show a periodic reminder
  useEffect(() => {
    const checkPeriodicReminder = async () => {
      if (!isAnonymous) return;

      try {
        const lastShown = await AsyncStorage.getItem(UPGRADE_PROMPT_KEYS.LAST_SHOWN);
        const showCount = await AsyncStorage.getItem(UPGRADE_PROMPT_KEYS.SHOW_COUNT);
        const dismissedCount = await AsyncStorage.getItem(UPGRADE_PROMPT_KEYS.DISMISSED_COUNT);
        const firstLaunch = await AsyncStorage.getItem(UPGRADE_PROMPT_KEYS.FIRST_LAUNCH);

        const now = Date.now();
        const lastShownTime = lastShown ? parseInt(lastShown) : 0;
        const totalShown = showCount ? parseInt(showCount) : 0;
        const totalDismissed = dismissedCount ? parseInt(dismissedCount) : 0;
        const firstLaunchTime = firstLaunch ? parseInt(firstLaunch) : now;

        // Don't show if user has dismissed too many times
        if (totalDismissed >= 3) return;

        // Show after 5 minutes of first use, then every 30 minutes
        const timeSinceFirstLaunch = now - firstLaunchTime;
        const timeSinceLastShown = now - lastShownTime;
        
        const shouldShow = 
          (timeSinceFirstLaunch > 5 * 60 * 1000 && totalShown === 0) || // 5 minutes after first launch
          (timeSinceLastShown > 30 * 60 * 1000 && totalShown < 5); // Every 30 minutes, max 5 times

        if (shouldShow) {
          showDataWarningPrompt();
        }
      } catch (error) {
        logger.error('Failed to check periodic reminder:', error);
      }
    };

    // Check after a delay to avoid showing immediately
    const timer = setTimeout(checkPeriodicReminder, 10000); // 10 seconds delay
    return () => clearTimeout(timer);
  }, [isAnonymous]);

  const trackPromptShown = useCallback(async () => {
    try {
      const now = Date.now().toString();
      const currentCount = await AsyncStorage.getItem(UPGRADE_PROMPT_KEYS.SHOW_COUNT);
      const newCount = currentCount ? (parseInt(currentCount) + 1).toString() : '1';
      
      await AsyncStorage.multiSet([
        [UPGRADE_PROMPT_KEYS.LAST_SHOWN, now],
        [UPGRADE_PROMPT_KEYS.SHOW_COUNT, newCount],
      ]);
    } catch (error) {
      logger.error('Failed to track prompt shown:', error);
    }
  }, []);

  const trackPromptDismissed = useCallback(async () => {
    try {
      const currentCount = await AsyncStorage.getItem(UPGRADE_PROMPT_KEYS.DISMISSED_COUNT);
      const newCount = currentCount ? (parseInt(currentCount) + 1).toString() : '1';
      
      await AsyncStorage.setItem(UPGRADE_PROMPT_KEYS.DISMISSED_COUNT, newCount);
    } catch (error) {
      logger.error('Failed to track prompt dismissed:', error);
    }
  }, []);

  const showPrompt = useCallback((
    trigger: UpgradePromptState['trigger'],
    title?: string,
    description?: string
  ) => {
    if (!isAnonymous) return;

    setPromptState({
      visible: true,
      trigger,
      title,
      description,
    });
    trackPromptShown();
  }, [isAnonymous, trackPromptShown]);

  const hidePrompt = useCallback(() => {
    setPromptState(prev => ({ ...prev, visible: false }));
    trackPromptDismissed();
  }, [trackPromptDismissed]);

  // Specific prompt triggers
  const showAchievementPrompt = useCallback((title?: string, description?: string) => {
    showPrompt('achievement', title, description);
  }, [showPrompt]);

  const showDataWarningPrompt = useCallback((title?: string, description?: string) => {
    showPrompt('data_warning', title, description);
  }, [showPrompt]);

  const showFeatureUnlockPrompt = useCallback((title?: string, description?: string) => {
    showPrompt('feature_unlock', title, description);
  }, [showPrompt]);

  const showManualPrompt = useCallback((title?: string, description?: string) => {
    showPrompt('manual', title, description);
  }, [showPrompt]);

  return {
    promptState,
    hidePrompt,
    showAchievementPrompt,
    showDataWarningPrompt,
    showFeatureUnlockPrompt,
    showManualPrompt,
  };
};
