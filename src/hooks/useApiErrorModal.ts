import { useEffect, useState } from 'react';

export function useApiErrorModal(error: unknown) {
  const [visible, setVisible] = useState(false);
  const [currentError, setCurrentError] = useState<unknown>(null);

  useEffect(() => {
    if (error) {
      setCurrentError(error);
      setVisible(true);
    }
  }, [error]);

  const dismiss = () => {
    setVisible(false);
    setCurrentError(null);
  };

  return { visible, error: currentError, dismiss };
}
