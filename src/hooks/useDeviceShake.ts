import { Accelerometer } from 'expo-sensors';
import { useEffect, useRef, useState } from 'react';
import { Platform } from 'react-native';

interface UseDeviceShakeOptions {
  threshold?: number;
  timeDelay?: number;
  onShake?: () => void;
}

export const useDeviceShake = ({
  threshold = 3,
  timeDelay = 1000,
  onShake,
}: UseDeviceShakeOptions = {}) => {
  const [isShaking, setIsShaking] = useState(false);
  const lastShakeTime = useRef(0);
  const subscription = useRef<any>(null);

  useEffect(() => {
    // Only enable shake detection on mobile platforms
    if (Platform.OS === 'web') {
      return;
    }

    const startListening = async () => {
      try {
        // Check if accelerometer is available
        const isAvailable = await Accelerometer.isAvailableAsync();
        if (!isAvailable) {
          console.warn('Accelerometer is not available on this device');
          return;
        }

        // Set update interval
        Accelerometer.setUpdateInterval(100);

        // Subscribe to accelerometer updates
        subscription.current = Accelerometer.addListener(({ x, y, z }) => {
          const acceleration = Math.sqrt(x * x + y * y + z * z);
          const currentTime = Date.now();

          if (acceleration > threshold && currentTime - lastShakeTime.current > timeDelay) {
            lastShakeTime.current = currentTime;
            setIsShaking(true);
            onShake?.();

            // Reset shaking state after a short delay
            setTimeout(() => setIsShaking(false), 500);
          }
        });
      } catch (error) {
        console.warn('Error setting up accelerometer:', error);
      }
    };

    startListening();

    return () => {
      if (subscription.current) {
        subscription.current.remove();
      }
    };
  }, [threshold, timeDelay, onShake]);

  return { isShaking };
};
