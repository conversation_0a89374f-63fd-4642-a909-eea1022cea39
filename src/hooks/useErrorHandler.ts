import { useCallback, useState } from 'react';
import { extractErrorMessage } from '../utils/errorUtils';
import { logger } from '../utils/logger';

interface ErrorState {
  visible: boolean;
  title: string;
  message: string;
  canRetry: boolean;
  retryAction?: () => void;
}

/**
 * Hook for consistent error handling across the app.
 * Provides toast-like error display with optional retry functionality.
 */
export const useErrorHandler = () => {
  const [errorState, setErrorState] = useState<ErrorState>({
    visible: false,
    title: 'Error',
    message: '',
    canRetry: false,
  });

  const showError = useCallback((
    error: any,
    title: string = 'Error',
    retryAction?: () => void
  ) => {
    const message = extractErrorMessage(error, 'An unexpected error occurred');
    
    logger.error(`${title}:`, error);
    
    setErrorState({
      visible: true,
      title,
      message,
      canRetry: !!retryAction,
      retryAction,
    });
  }, []);

  const hideError = useCallback(() => {
    setErrorState(prev => ({
      ...prev,
      visible: false,
    }));
  }, []);

  const retry = useCallback(() => {
    if (errorState.retryAction) {
      hideError();
      errorState.retryAction();
    }
  }, [errorState.retryAction, hideError]);

  return {
    errorState,
    showError,
    hideError,
    retry,
  };
};
