import { useQuery } from '@tanstack/react-query';
import { callApi } from '../api/client';

export interface InboxMessage {
  id: string;
  date: number;
  message: string;
  extra: {
    category: string;
    title: string;
  };
  gameworldId: string;
  teamId: string;
}

export interface InboxMessagesResponse {
  messages: InboxMessage[];
  count: number;
  teamId: string;
  gameworldId: string;
}

export function useInboxMessages() {
  return useQuery<InboxMessagesResponse>({
    queryKey: ['inboxMessages'],
    queryFn: async () => {
      return await callApi('/inbox/messages');
    },
    staleTime: 1000 * 60 * 2, // Data is considered fresh for 2 minutes
    gcTime: 1000 * 60 * 10, // Cache is kept for 10 minutes
  });
}
