import AsyncStorage from '@react-native-async-storage/async-storage';
import { useEffect } from 'react';
import { Platform } from 'react-native';
import { useManager } from '../context/ManagerContext';
import {
  cancelAllMatchNotifications,
  configureNotifications,
  scheduleAllMatchNotifications,
} from '../services/NotificationScheduler';
import { logger } from '../utils/logger';
import { useCachedFixtures } from './useCachedData';

/**
 * Hook to manage notification scheduling based on user preferences
 */
export const useNotificationScheduler = () => {
  useEffect(() => {
    // Configure notification behaviour when the hook mounts
    configureNotifications();
  }, []);

  const { manager, team } = useManager();
  const { data, isLoading, error } = useCachedFixtures(
    manager?.gameworldId,
    team?.league.id,
    team?.teamId
  );

  /**
   * Update notification scheduling based on current preferences
   */
  const updateNotificationScheduling = async () => {
    if (Platform.OS === 'web') return;

    if (isLoading || error || !data) return;

    // Cancel all notifications before rescheduling
    await cancelAllMatchNotifications();

    try {
      // Check if pre-match push notifications are enabled
      const preMatchPushEnabled = await AsyncStorage.getItem('@notification_preMatch_push');

      if (preMatchPushEnabled === 'true') {
        logger.log('Pre-match notifications enabled, scheduling notifications');
        await scheduleAllMatchNotifications(data);
      } else {
        logger.log('Pre-match notifications disabled, cancelling notifications');
        await cancelAllMatchNotifications();
      }
    } catch (e) {
      logger.error('Error updating notification scheduling:', e);
    }
  };

  return {
    updateNotificationScheduling,
  };
};
