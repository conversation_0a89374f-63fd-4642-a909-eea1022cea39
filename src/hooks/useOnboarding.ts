import AsyncStorage from '@react-native-async-storage/async-storage';
import { router, usePathname } from 'expo-router';
import { useCallback, useEffect, useState } from 'react';
import { HAS_ONBOARDED_KEY } from '../context/ManagerContext';
import { logger } from '../utils/logger';

export type OnboardingStep =
  | 'alpha-notice'
  | 'manager-profile'
  | 'notification-settings'
  | 'completed';

const ONBOARDING_CURRENT_STEP_KEY = '@onboarding_current_step';

interface OnboardingState {
  hasOnboarded: boolean;
  currentStep: OnboardingStep;
  isLoading: boolean;
}

/**
 * Hook to manage the onboarding flow for new users.
 * Simple user-controlled flow: alpha-notice → manager-profile → notification-settings → completed
 * Progress is stored in AsyncStorage and controlled by user actions, not data state.
 */
export const useOnboarding = () => {
  const pathname = usePathname();
  const [onboardingState, setOnboardingState] = useState<OnboardingState>({
    hasOnboarded: false,
    currentStep: 'alpha-notice',
    isLoading: true,
  });

  // Check onboarding status and current step on mount
  useEffect(() => {
    loadOnboardingState();
  }, []);

  const loadOnboardingState = useCallback(async () => {
    logger.log('Loading onboarding state...');
    try {
      const [hasOnboarded, currentStep] = await Promise.all([
        AsyncStorage.getItem(HAS_ONBOARDED_KEY),
        AsyncStorage.getItem(ONBOARDING_CURRENT_STEP_KEY),
      ]);

      setOnboardingState({
        hasOnboarded: hasOnboarded === 'true',
        currentStep: (currentStep as OnboardingStep) || 'alpha-notice',
        isLoading: false,
      });

      logger.log('Loaded onboarding state:', {
        hasOnboarded: hasOnboarded === 'true',
        currentStep: currentStep || 'alpha-notice',
      });
    } catch (error) {
      logger.error('Error loading onboarding state:', error);
      setOnboardingState({
        hasOnboarded: false,
        currentStep: 'alpha-notice',
        isLoading: false,
      });
    }
  }, []);

  const saveCurrentStep = useCallback(async (step: OnboardingStep) => {
    try {
      logger.log('Saving onboarding step:', step);

      // Update state immediately to prevent race conditions
      setOnboardingState((prev) => ({ ...prev, currentStep: step }));

      // Then save to storage
      await AsyncStorage.setItem(ONBOARDING_CURRENT_STEP_KEY, step);
      logger.log('Saved onboarding step:', step);
    } catch (error) {
      logger.error('Error saving onboarding step:', error);
    }
  }, []);

  const completeOnboarding = useCallback(async () => {
    try {
      logger.log('Completing onboarding...');
      await Promise.all([
        AsyncStorage.setItem(HAS_ONBOARDED_KEY, 'true'),
        AsyncStorage.removeItem(ONBOARDING_CURRENT_STEP_KEY), // Clean up step tracking
      ]);

      setOnboardingState((prev) => ({
        ...prev,
        hasOnboarded: true,
        currentStep: 'completed',
      }));

      logger.log('Onboarding completed successfully', {
        hasOnboarded: true,
        currentStep: 'completed',
      });
    } catch (error) {
      logger.error('Error completing onboarding:', error);
    }
  }, []);

  const navigateToNextStep = useCallback(async () => {
    const { currentStep } = onboardingState;
    logger.log('Navigating to next onboarding step from:', currentStep);

    switch (currentStep) {
      case 'alpha-notice':
        await saveCurrentStep('manager-profile');
        router.replace('/manager-profile');
        break;
      case 'manager-profile':
        await saveCurrentStep('notification-settings');
        router.replace('/notification-settings');
        break;
      case 'notification-settings':
        await completeOnboarding();
        router.replace('/');
        break;
      default:
        router.replace('/');
    }
  }, [onboardingState.currentStep, completeOnboarding, saveCurrentStep]);

  const navigateToCurrentStep = useCallback(() => {
    const { currentStep, hasOnboarded } = onboardingState;

    logger.log('Navigating to current onboarding step:', currentStep, 'from', pathname);
    if (hasOnboarded) {
      if (pathname !== '/') {
        logger.log('Onboarding completed, navigating to home screen');
        router.replace('/');
      }
      logger.log('Onboarding completed, no navigation needed');
      return;
    }

    // Check if we're already on the correct route to prevent unnecessary navigation
    const targetRoute =
      currentStep === 'alpha-notice'
        ? '/alpha-notice'
        : currentStep === 'manager-profile'
          ? '/manager-profile'
          : currentStep === 'notification-settings'
            ? '/notification-settings'
            : '/';

    if (pathname === targetRoute) {
      logger.log('Already on correct onboarding step:', currentStep, 'at', pathname);
      return;
    }

    logger.log(
      'Navigating to current onboarding step:',
      currentStep,
      'from',
      pathname,
      'to',
      targetRoute
    );

    switch (currentStep) {
      case 'alpha-notice':
        router.replace('/alpha-notice');
        break;
      case 'manager-profile':
        router.replace('/manager-profile');
        break;
      case 'notification-settings':
        router.replace('/notification-settings');
        break;
      default:
        router.replace('/');
    }
  }, [onboardingState, pathname]);

  const skipToStep = useCallback(
    (step: OnboardingStep) => {
      if (onboardingState.hasOnboarded) {
        logger.warn('Cannot skip to step - onboarding already completed');
        return;
      }

      setOnboardingState((prev) => ({ ...prev, currentStep: step }));
      logger.log('Skipping to onboarding step:', step);

      switch (step) {
        case 'alpha-notice':
          router.replace('/alpha-notice');
          break;
        case 'manager-profile':
          router.replace('/manager-profile');
          break;
        case 'notification-settings':
          router.replace('/notification-settings');
          break;
        case 'completed':
          completeOnboarding();
          router.replace('/');
          break;
      }
    },
    [onboardingState.hasOnboarded, completeOnboarding]
  );

  const resetOnboarding = useCallback(async () => {
    try {
      logger.log('Resetting onboarding...');
      await Promise.all([
        AsyncStorage.removeItem(HAS_ONBOARDED_KEY),
        AsyncStorage.removeItem(ONBOARDING_CURRENT_STEP_KEY),
      ]);

      setOnboardingState({
        hasOnboarded: false,
        currentStep: 'alpha-notice',
        isLoading: false,
      });

      logger.log('Onboarding reset successfully');
    } catch (error) {
      logger.error('Error resetting onboarding:', error);
    }
  }, []);

  return {
    ...onboardingState,
    navigateToNextStep,
    navigateToCurrentStep,
    completeOnboarding,
    skipToStep,
    resetOnboarding,
  };
};
