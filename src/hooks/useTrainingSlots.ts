import { useMutation, useQuery, useQueryClient } from '@tanstack/react-query';
import { callApi } from '../api/client';

export interface TrainingSlotData {
  id?: string;
  slotId: number;
  player?: any;
  attribute?: string;
  startValue?: number;
  currentValue?: number;
  locked: boolean;
  unlockCost?: number;
}

export interface TrainingSlotsResponse {
  slots: TrainingSlotData[];
}

export function useTrainingSlots(teamId?: string) {
  return useQuery<TrainingSlotsResponse>({
    queryKey: ['trainingSlots', teamId],
    queryFn: async () => {
      return await callApi<TrainingSlotsResponse>(`/training/slots`);
    },
    enabled: !!teamId,
    staleTime: 1000 * 60, // 1 minute
    // removed cacheTime, not supported in this version
  });
}

export function useUnlockTrainingSlot() {
  const queryClient = useQueryClient();
  return useMutation({
    mutationFn: async (slotIndex: number) => {
      return await callApi<{ balance: number }>('/training/slots/unlock', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ slotIndex }),
      });
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['trainingSlots'] });
    },
  });
}

export function useAssignTrainingSlot() {
  const queryClient = useQueryClient();
  return useMutation({
    mutationFn: async ({
      slotId,
      playerId,
      attribute,
      slotIndex,
    }: {
      slotId?: string;
      playerId: string;
      attribute: string;
      slotIndex: number;
    }) => {
      return await callApi(`/training/slots/${slotId || 'new'}`, {
        method: 'POST',
        body: JSON.stringify({ playerId, attribute, slotIndex }),
        headers: { 'Content-Type': 'application/json' },
      });
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['trainingSlots'] });
    },
  });
}
