import { useQuery } from '@tanstack/react-query';
import { callApi } from '../api/client';
import { TransferListPlayer } from '../models/player';

export interface TransferListPlayersResponse {
  players: TransferListPlayer[];
  pagination?: {
    lastEvaluatedKey?: string;
    nextToken?: string; // Alternative field name
    pageId?: string; // Another alternative field name
  };
  // Handle different response structures
  lastEvaluatedKey?: string;
  nextToken?: string;
  pageId?: string;
}

export interface TransferListFilters {
  limit?: string;
  lastEvaluatedKey?: string;
  sortBy?: 'auctionCurrentPrice' | 'auctionEndTime';
  sortDirection?: 'ASC' | 'DESC';
  minPrice?: string;
  maxPrice?: string;
}

export function useTransferListPlayers(gameworldId?: string, filters?: TransferListFilters) {
  return useQuery<TransferListPlayersResponse>({
    queryKey: ['transferListPlayers', gameworldId, filters],
    queryFn: async () => {
      // Build query parameters
      const queryParams = new URLSearchParams();

      if (filters?.lastEvaluatedKey) {
        queryParams.append('lastEvaluatedKey', filters.lastEvaluatedKey);
      }
      if (filters?.limit) {
        queryParams.append('limit', filters.limit);
      }
      if (filters?.sortBy) {
        queryParams.append('sortBy', filters.sortBy);
      }
      if (filters?.sortDirection) {
        queryParams.append('sortDirection', filters.sortDirection);
      }
      if (filters?.minPrice) {
        queryParams.append('minPrice', filters.minPrice);
      }
      if (filters?.maxPrice) {
        queryParams.append('maxPrice', filters.maxPrice);
      }

      const queryString = queryParams.toString();
      const endpoint = `/${gameworldId}/players/transfer-list${queryString ? `?${queryString}` : ''}`;

      try {
        return await callApi<TransferListPlayersResponse>(endpoint);
      } catch (error: any) {
        // Check if this is a "No players found" error (404)
        if (
          error.message?.includes('404') ||
          error.response?.status === 404 ||
          (typeof error === 'object' && error.error === 'No players found')
        ) {
          // Return an empty list instead of throwing an error
          return { players: [], lastEvaluatedKey: undefined };
        }

        // For other errors, rethrow
        throw error;
      }
    },
    enabled: !!gameworldId,
    // This ensures the query refetches when filters change
    refetchOnWindowFocus: false,
    // Don't retry on error - we handle 404s gracefully above
    retry: false,
  });
}
