import AsyncStorage from '@react-native-async-storage/async-storage';
import { useCallback, useEffect, useRef, useState } from 'react';
import { useManager } from '../context/ManagerContext';
import { Fixture } from '../models/fixture';
import { logger } from '../utils/logger';
import { useCachedFixtures } from './useCachedData';

const LAST_CELEBRATED_KEY = 'celebratedFixtureId';
const ALWAYS_CELEBRATE = __DEV__;

function isWin(f: Fixture, teamId?: string) {
  if (!teamId || !f.score) return false;
  const [h, a] = f.score;
  if (f.homeTeamId === teamId) return h > a;
  if (f.awayTeamId === teamId) return a > h;
  return false;
}

export interface VictoryCelebrationResult {
  openFixtureId: string | null;
  closeCelebration: () => void;
}

export function useVictoryCelebration(): VictoryCelebrationResult {
  const { manager, team } = useManager();
  const {
    data: fixtures,
    refetch,
    isLoading,
  } = useCachedFixtures(manager?.gameworldId, team?.league.id, team?.teamId);
  const [openFixtureId, setOpenFixtureId] = useState<string | null>(null);

  const processingRef = useRef(false);
  const attemptedPrefetch = useRef(false);

  // Eager prefetch: if team + league ready but fixtures not yet loaded, trigger refetch once
  useEffect(() => {
    if (
      !attemptedPrefetch.current &&
      team?.teamId &&
      manager?.gameworldId &&
      team?.league.id &&
      (!fixtures || fixtures.length === 0) &&
      !isLoading
    ) {
      attemptedPrefetch.current = true;
      logger.debug('VictoryCelebration: eager refetch fixtures');
      refetch();
    }
  }, [team?.teamId, manager?.gameworldId, team?.league.id, fixtures, isLoading, refetch]);

  const markCelebrated = useCallback(async (fixtureId: string) => {
    try {
      await AsyncStorage.setItem(LAST_CELEBRATED_KEY, fixtureId);
    } catch (e) {
      logger.warn('Failed to mark celebrated', e);
    }
  }, []);

  const hasCelebrated = useCallback(async (fixtureId: string) => {
    try {
      const stored = await AsyncStorage.getItem(LAST_CELEBRATED_KEY);
      if (!stored) return false;
      return fixtureId === stored;
    } catch {
      return false;
    }
  }, []);

  // Close handler
  const closeCelebration = useCallback(() => {
    setOpenFixtureId(null);
  }, []);

  useEffect(() => {
    if (!fixtures || fixtures.length === 0 || !team?.teamId) {
      if (__DEV__)
        logger.debug('VictoryCelebration: skip run - missing data', {
          hasFixtures: !!fixtures,
          fixtureCount: fixtures?.length,
          teamId: team?.teamId,
        });
      return;
    }
    if (processingRef.current) {
      if (__DEV__) logger.debug('VictoryCelebration: already processing, skip');
      return;
    }

    const run = async () => {
      processingRef.current = true;
      logger.debug('VictoryCelebration: evaluation start', { fixtureCount: fixtures.length });
      try {
        // Get most recent result
        const mostRecentResult = fixtures
          .filter((f) => f.played)
          .sort((a, b) => b.date - a.date)[0];
        if (!mostRecentResult) {
          logger.debug('VictoryCelebration: no most recent result');
          return;
        }

        if (!isWin(mostRecentResult, team.teamId)) {
          logger.debug('VictoryCelebration: most recent result is not a win');
          return;
        }

        const already = ALWAYS_CELEBRATE ? false : await hasCelebrated(mostRecentResult.fixtureId);

        logger.debug('VictoryCelebration: check newly win', {
          fixtureId: mostRecentResult.fixtureId,
          already,
        });
        if (!already) {
          await markCelebrated(mostRecentResult.fixtureId);

          logger.debug('VictoryCelebration: opening modal for new win', {
            fixtureId: mostRecentResult.fixtureId,
          });
          setOpenFixtureId(mostRecentResult.fixtureId);
          return;
        }
      } finally {
        logger.debug('VictoryCelebration: evaluation end');
        processingRef.current = false;
      }
    };

    run();
  }, [fixtures, team?.teamId, hasCelebrated, markCelebrated]);

  return { openFixtureId, closeCelebration };
}
