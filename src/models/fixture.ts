import { MatchStats } from '../components/MatchStatsDisplay';

export interface Scorer {
  playerId: string;
  playerName: string;
  team: number;
  goalTime: {
    minute: number;
    half: number;
  }[];
}

export interface Fixture {
  fixtureId: string;
  homeTeamId: string;
  homeTeamName: string;
  awayTeamId: string;
  awayTeamName: string;
  date: number;
  score?: [number, number];
  scorers?: Scorer[];
  played: boolean;
}

export interface PossibleEventSubstitution {
  team?: string;
  homeTeam?: string;
  oppTeam?: string;
  awayTeam?: string;
  player?: string;
  oppPlayer?: string;
  nextPlayer?: string;
  homeScore?: string;
  awayScore?: string;
}

export interface MatchEvent {
  localisationId: string;
  substitutions: PossibleEventSubstitution;
  minute: number;
  half: number;
}

export interface MatchPlayer {
  playerId: string;
  playerName: string;
  joinedMatchMinute?: number;
  leftMatchMinute?: number;
  rating?: number; // 1-10
  sentOff?: number; // the minute of the match this player was sent off
  injured?: number; // the minute of the match this player was injured
  goals?: number[]; // the minutes of the match this player scored a goal
  assists?: number[]; // the minutes of the match this player assisted a goal
  substitute?: string; // the playerid of the sub who replaced this player
}

export interface DBFixture {
  gameworldId: string;
  leagueId: string;
  fixtureId: string;
  homeTeamId: string;
  homeTeamName: string;
  awayTeamId: string;
  awayTeamName: string;
  date: number;
  stats?: MatchStats;
  events?: MatchEvent[];
  homePlayers: MatchPlayer[];
  awayPlayers: MatchPlayer[];
}
