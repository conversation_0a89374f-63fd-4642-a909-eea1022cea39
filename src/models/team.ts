import { Player } from './player';

export interface Team {
  gameworldId: string;
  league: {
    id: string;
  };
  nextFixture: {
    fixtureId: string;
    date: number;
  };
  teamId: string;
  managerId?: string;
  teamName: string;
  players: Player[];
  balance: number;
  played: number;
  wins: number;
  draws: number;
  goalsFor: number;
  goalsAgainst: number;
  losses: number;
  points: number;
  trainingLevel: number;
}
