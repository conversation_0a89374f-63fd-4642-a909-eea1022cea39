import React from 'react';
import { Linking, ScrollView } from 'react-native';
import styled, { DefaultTheme } from 'styled-components/native';
import { Text } from '../components/Text';
import { useOnboarding } from '../hooks/useOnboarding';
import { logger } from '../utils/logger';

interface StyledProps {
  theme: DefaultTheme;
}

const Container = styled.View`
  flex: 1;
  background-color: ${(props: StyledProps) => props.theme.colors.background};
  padding: 16px;
`;

const ContentContainer = styled.View`
  flex: 1;
`;

const Title = styled(Text)`
  font-size: 24px;
  font-family: ${(props: StyledProps) => props.theme.typography.bold};
  text-align: center;
  margin-bottom: 24px;
  color: ${(props: StyledProps) => props.theme.colors.text.primary};
`;

const NoticeText = styled(Text)`
  font-size: 16px;
  line-height: 24px;
  margin-bottom: 16px;
  color: ${(props: StyledProps) => props.theme.colors.text.primary};
`;

const LinkText = styled(Text)`
  font-size: 16px;
  line-height: 24px;
  margin-bottom: 16px;
  color: ${(props: StyledProps) => props.theme.colors.primary};
  text-decoration-line: underline;
`;

const SignatureText = styled(Text)`
  font-size: 16px;
  line-height: 24px;
  margin-top: 24px;
  margin-bottom: 32px;
  color: ${(props: StyledProps) => props.theme.colors.text.primary};
`;

const ButtonContainer = styled.View`
  margin-top: auto;
  padding-bottom: 16px;
`;

const ContinueButton = styled.TouchableOpacity`
  background-color: ${(props: StyledProps) => props.theme.colors.primary};
  padding: 16px;
  border-radius: 8px;
  align-items: center;
  opacity: ${(props: { disabled?: boolean }) => (props.disabled ? 0.6 : 1)};
`;

const ButtonText = styled(Text)`
  color: white;
  font-size: 18px;
  font-family: ${(props: StyledProps) => props.theme.typography.bold};
`;

const AlphaNoticeScreen = () => {
  logger.log('AlphaNoticeScreen mounted or remounted');
  const { navigateToNextStep } = useOnboarding();

  const handleContinue = () => {
    navigateToNextStep();
  };

  const handleLinkPress = async () => {
    try {
      await Linking.openURL('https://discord.com/channels/1392598895099383889/1392600133048336574');
    } catch (error) {
      logger.error('Error opening link:', error);
    }
  };

  return (
    <Container>
      <ScrollView showsVerticalScrollIndicator={false}>
        <ContentContainer>
          <Title>Welcome to Early Access!</Title>

          <NoticeText>
            Thank you for joining the Early Access test of Jumpers for Goalposts! The game is now
            much more stable, but you may still encounter the occasional bug or rough edge. Your
            feedback is still incredibly valuable as we continue to improve and polish the
            experience.
          </NoticeText>

          <NoticeText>
            The servers are now much more reliable—there is no risk of everything being wiped. At
            worst, if something goes wrong, you might lose up to a day of progress if I have to
            restore a backup. Importantly, any in-app purchases you make are safe and will not be
            lost, so, you know, feel free to spend some money! :).
          </NoticeText>

          <NoticeText>
            If you find any bugs or have ideas for improvements, please let me know! You can send a
            bug report from the feedback option in the menu, by shaking your device, or by joining
            our community.
          </NoticeText>

          <NoticeText>
            To chat with other players or share your thoughts, join us on Discord:{' '}
            <LinkText onPress={handleLinkPress}>
              https://discord.com/channels/1392598895099383889/1392600133048336574
            </LinkText>
            .
          </NoticeText>

          <SignatureText>Thanks{'\n'}Dave</SignatureText>
        </ContentContainer>
      </ScrollView>

      <ButtonContainer>
        <ContinueButton onPress={handleContinue}>
          <ButtonText>Continue</ButtonText>
        </ContinueButton>
      </ButtonContainer>
    </Container>
  );
};

export default AlphaNoticeScreen;
