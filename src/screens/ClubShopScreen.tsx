import analytics from '@react-native-firebase/analytics';
import React, { useState } from 'react';
import { ActivityIndicator, <PERSON><PERSON>, FlatList, RefreshControl } from 'react-native';
import { PurchasesPackage } from 'react-native-purchases';
import styled, { DefaultTheme } from 'styled-components/native';
import { callApi } from '../api/client';
import PurchaseItem from '../components/ClubShop/PurchaseItem';
import PurchaseModal from '../components/ClubShop/PurchaseModal';
import { Text } from '../components/Text';
import { useManager } from '../context/ManagerContext';
import { usePurchases } from '../context/PurchaseContext';
import { useTheme } from '../theme/ThemeContext';
import { logger } from '../utils/logger';

interface StyledProps {
  theme: DefaultTheme;
}

const Container = styled.View<StyledProps>`
  flex: 1;
  background-color: ${(props) => props.theme.colors.background};
`;

const HeaderContainer = styled.View<StyledProps>`
  padding: 20px;
  background-color: ${(props) => props.theme.colors.surface};
  border-bottom-width: 1px;
  border-bottom-color: ${(props) => props.theme.colors.border};
`;

const HeaderTitle = styled(Text)<StyledProps>`
  font-size: 24px;
  font-family: ${(props) => props.theme.typography.bold};
  text-align: center;
  color: ${(props) => props.theme.colors.text.primary};
  margin-bottom: 8px;
`;

const HeaderSubtitle = styled(Text)<StyledProps>`
  font-size: 16px;
  text-align: center;
  color: ${(props) => props.theme.colors.text.secondary};
`;

const ContentContainer = styled.View`
  flex: 1;
  padding: 20px;
`;

const LoadingContainer = styled.View`
  flex: 1;
  justify-content: center;
  align-items: center;
`;

const ErrorContainer = styled.View<StyledProps>`
  flex: 1;
  justify-content: center;
  align-items: center;
  padding: 20px;
`;

const ErrorText = styled(Text)<StyledProps>`
  font-size: 16px;
  color: ${(props) => props.theme.colors.error};
  text-align: center;
  margin-bottom: 20px;
`;

const RetryButton = styled.TouchableOpacity<StyledProps>`
  background-color: ${(props) => props.theme.colors.primary};
  padding: 12px 24px;
  border-radius: 8px;
`;

const RetryButtonText = styled(Text)`
  color: white;
  font-family: ${(props: StyledProps) => props.theme.typography.bold};
  font-size: 16px;
`;

const RestoreButton = styled.TouchableOpacity<StyledProps>`
  background-color: ${(props) => props.theme.colors.button.secondary};
  padding: 12px 24px;
  border-radius: 8px;
  margin-top: 20px;
  align-self: center;
`;

const RestoreButtonText = styled(Text)<StyledProps>`
  color: ${(props) => props.theme.colors.text.primary};
  font-family: NunitoBold;
  font-size: 16px;
`;

const EmptyStateContainer = styled.View`
  flex: 1;
  justify-content: center;
  align-items: center;
  padding: 40px;
`;

const EmptyStateText = styled(Text)<StyledProps>`
  font-size: 16px;
  color: ${(props) => props.theme.colors.text.secondary};
  text-align: center;
`;

const ClubShopScreen: React.FC = () => {
  const { theme } = useTheme();
  const { offerings, isLoading, error, purchasePackage, restorePurchases, refreshOfferings } =
    usePurchases();
  const { manager, updateManager } = useManager();
  const [isPurchasing, setIsPurchasing] = useState(false);
  const [isRestoring, setIsRestoring] = useState(false);
  const [selectedPackage, setSelectedPackage] = useState<PurchasesPackage | null>(null);
  const [showPurchaseModal, setShowPurchaseModal] = useState(false);

  const handlePurchasePress = (packageToPurchase: PurchasesPackage) => {
    setSelectedPackage(packageToPurchase);
    setShowPurchaseModal(true);
    analytics()
      .logEvent('purchase_modal_opened', {
        item_id: packageToPurchase.identifier,
        item_name: packageToPurchase.product.title,
        price: packageToPurchase.product.price,
        currency: packageToPurchase.product.currencyCode,
      })
      .then(() => {});
  };

  const handleConfirmPurchase = async () => {
    if (!selectedPackage) return;

    try {
      setIsPurchasing(true);
      const success = await purchasePackage(selectedPackage);

      setShowPurchaseModal(false);
      setSelectedPackage(null);

      if (success) {
        try {
          // Fetch rewards for the purchased package
          const rewardsResponse = await callApi<{
            config: {
              productId: string;
              type: string;
              rewards: {
                scoutTokens?: number;
                superScoutTokens?: number;
                magicSponges?: number;
                cardAppeals?: number;
                trainingBoosts?: number;
              };
              isRecurring: boolean;
            };
          }>(`/iap/rewards?productId=${selectedPackage.identifier}`);

          // Update manager with the rewards
          if (rewardsResponse.config.rewards && manager) {
            const currentManager = { ...manager };
            const rewards = rewardsResponse.config.rewards;

            // Apply rewards to current values
            if (rewards.scoutTokens) {
              currentManager.scoutTokens = (currentManager.scoutTokens || 0) + rewards.scoutTokens;
            }
            if (rewards.superScoutTokens) {
              currentManager.superScoutTokens =
                (currentManager.superScoutTokens || 0) + rewards.superScoutTokens;
            }
            if (rewards.magicSponges) {
              currentManager.magicSponges =
                (currentManager.magicSponges || 0) + rewards.magicSponges;
            }
            if (rewards.cardAppeals) {
              currentManager.cardAppeals = (currentManager.cardAppeals || 0) + rewards.cardAppeals;
            }
            if (rewards.trainingBoosts) {
              currentManager.trainingBoosts =
                (currentManager.trainingBoosts || 0) + rewards.trainingBoosts;
            }

            // Update the cached manager
            updateManager(currentManager);

            logger.log('Applied rewards to manager:', rewards);
          }
        } catch (rewardsError) {
          logger.error('Failed to fetch or apply rewards:', rewardsError);
          // Don't show error to user since purchase was successful
        }

        Alert.alert(
          'Purchase Successful!',
          'Thank you for your purchase. Your items have been added to your account.',
          [{ text: 'OK' }]
        );
        await analytics().logEvent('purchase_completed', {
          item_id: selectedPackage.identifier,
          item_name: selectedPackage.product.title,
          price: selectedPackage.product.price,
          currency: selectedPackage.product.currencyCode,
        });
      } else {
        Alert.alert(
          'Purchase Failed',
          'There was an issue processing your purchase. Please try again.',
          [{ text: 'OK' }]
        );
        await analytics().logEvent('purchase_failed', {
          item_id: selectedPackage.identifier,
          item_name: selectedPackage.product.title,
          price: selectedPackage.product.price,
          currency: selectedPackage.product.currencyCode,
        });
      }
    } catch (err) {
      logger.error('Purchase error:', err);
      Alert.alert('Purchase Error', 'An unexpected error occurred. Please try again later.', [
        { text: 'OK' },
      ]);
      await analytics().logEvent('purchase_error', {
        item_id: selectedPackage.identifier,
        item_name: selectedPackage.product.title,
        price: selectedPackage.product.price,
        currency: selectedPackage.product.currencyCode,
        error_message: err instanceof Error ? err.message : 'Unknown error',
      });
    } finally {
      setIsPurchasing(false);
    }
  };

  const handleCancelPurchase = () => {
    setShowPurchaseModal(false);
    if (selectedPackage) {
      analytics()
        .logEvent('purchase_cancelled', {
          item_id: selectedPackage.identifier,
          item_name: selectedPackage.product.title,
          price: selectedPackage.product.price,
          currency: selectedPackage.product.currencyCode,
        })
        .then(() => {});
    }
    setSelectedPackage(null);
  };

  const handleRestore = async () => {
    try {
      setIsRestoring(true);
      const success = await restorePurchases();

      if (success) {
        Alert.alert('Purchases Restored', 'Your previous purchases have been restored.', [
          { text: 'OK' },
        ]);
      } else {
        Alert.alert('Restore Failed', 'Unable to restore purchases. Please try again.', [
          { text: 'OK' },
        ]);
      }
    } catch (err) {
      logger.error('Restore error:', err);
      Alert.alert('Restore Error', 'An unexpected error occurred while restoring purchases.', [
        { text: 'OK' },
      ]);
    } finally {
      setIsRestoring(false);
    }
  };

  const renderPurchaseItem = ({ item }: { item: PurchasesPackage }) => (
    <PurchaseItem package={item} onPress={handlePurchasePress} disabled={isPurchasing} />
  );

  if (isLoading) {
    return (
      <Container>
        <HeaderContainer>
          <HeaderTitle>Club Shop</HeaderTitle>
          <HeaderSubtitle>Premium items and upgrades</HeaderSubtitle>
        </HeaderContainer>
        <LoadingContainer>
          <ActivityIndicator size="large" color={theme.colors.primary} />
          <Text style={{ marginTop: 16, color: theme.colors.text.secondary }}>
            Loading shop items...
          </Text>
        </LoadingContainer>
      </Container>
    );
  }

  if (error) {
    return (
      <Container>
        <HeaderContainer>
          <HeaderTitle>Club Shop</HeaderTitle>
          <HeaderSubtitle>Premium items and upgrades</HeaderSubtitle>
        </HeaderContainer>
        <ErrorContainer>
          <ErrorText>{error}</ErrorText>
          <RetryButton onPress={refreshOfferings}>
            <RetryButtonText>Retry</RetryButtonText>
          </RetryButton>
        </ErrorContainer>
      </Container>
    );
  }

  const packages = offerings?.availablePackages || [];

  return (
    <Container>
      <HeaderContainer>
        <HeaderTitle>Club Shop</HeaderTitle>
        <HeaderSubtitle>Premium items and upgrades</HeaderSubtitle>
      </HeaderContainer>

      <ContentContainer>
        {packages.length === 0 ? (
          <EmptyStateContainer>
            <EmptyStateText>
              No items available at the moment.{'\n'}
              Please check back later!
            </EmptyStateText>
          </EmptyStateContainer>
        ) : (
          <FlatList
            data={packages}
            renderItem={renderPurchaseItem}
            keyExtractor={(item) => item.identifier}
            showsVerticalScrollIndicator={false}
            refreshControl={
              <RefreshControl
                refreshing={isLoading}
                onRefresh={refreshOfferings}
                colors={[theme.colors.primary]}
                tintColor={theme.colors.primary}
              />
            }
          />
        )}

        <RestoreButton onPress={handleRestore} disabled={isRestoring}>
          <RestoreButtonText>
            {isRestoring ? 'Restoring...' : 'Restore Purchases'}
          </RestoreButtonText>
        </RestoreButton>
      </ContentContainer>

      <PurchaseModal
        visible={showPurchaseModal}
        package={selectedPackage}
        onConfirm={handleConfirmPurchase}
        onCancel={handleCancelPurchase}
        isLoading={isPurchasing}
      />
    </Container>
  );
};

export default ClubShopScreen;
