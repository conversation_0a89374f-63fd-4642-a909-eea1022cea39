import { MaterialIcons } from '@expo/vector-icons';
import React, { useCallback, useEffect, useState } from 'react';
import { ActivityIndicator, FlatList, RefreshControl } from 'react-native';
import styled, { DefaultTheme } from 'styled-components/native';
import { callApi } from '../api/client';
import { Text } from '../components/Text';
import { useManager } from '../context/ManagerContext';
import { logger } from '../utils/logger';
import { formatCurrencyLong, formatCurrencyShort } from '../utils/utils';

interface StyledProps {
  theme: DefaultTheme;
}

interface Transaction {
  id: string;
  gameworldId: string;
  team: string;
  date: number;
  amount: number;
  type: string;
  details: string;
}

interface TransactionsResponse {
  transactions: Transaction[];
}

const Container = styled.View<StyledProps>`
  flex: 1;
  background-color: ${(props) => props.theme.colors.background};
  padding: 16px;
`;

const BalanceSection = styled.View<StyledProps>`
  background-color: ${(props) => props.theme.colors.surface};
  border-radius: 12px;
  padding: 20px;
  margin-bottom: 20px;
  align-items: center;
  border: 1px solid ${(props) => props.theme.colors.border};
`;

const BalanceLabel = styled(Text)<StyledProps>`
  font-size: 16px;
  color: ${(props) => props.theme.colors.text.secondary};
  font-family: 'Nunito';
  margin-bottom: 8px;
`;

const BalanceAmount = styled(Text)<StyledProps>`
  font-size: 28px;
  color: ${(props) => props.theme.colors.text.primary};
  font-family: ${(props: StyledProps) => props.theme.typography.bold};
`;

const SectionTitle = styled(Text)<StyledProps>`
  font-size: 20px;
  color: ${(props) => props.theme.colors.text.primary};
  font-family: ${(props: StyledProps) => props.theme.typography.bold};
  margin-bottom: 16px;
`;

const DateHeader = styled.View<StyledProps>`
  background-color: ${(props) => props.theme.colors.primary};
  padding: 12px 16px;
  margin-bottom: 8px;
  border-radius: 8px;
  margin-top: 16px;
`;

const DateHeaderText = styled(Text)`
  color: white;
  font-family: ${(props: StyledProps) => props.theme.typography.bold};
  font-size: 16px;
`;

const TransactionRow = styled.View<StyledProps>`
  flex-direction: row;
  justify-content: space-between;
  align-items: center;
  padding: 12px 16px;
  margin-bottom: 4px;
  background-color: ${(props) => props.theme.colors.surface};
  border-radius: 6px;
`;

const TransactionType = styled(Text)<StyledProps>`
  font-size: 15px;
  color: ${(props) => props.theme.colors.text.primary};
  font-family: 'Nunito';
  flex: 1;
`;

const AmountContainer = styled.View`
  flex-direction: row;
  min-width: 140px;
  justify-content: flex-end;
`;

const IncomeAmount = styled(Text)<StyledProps>`
  font-size: 15px;
  color: #2e7d32;
  font-family: ${(props: StyledProps) => props.theme.typography.bold};
  width: 65px;
  text-align: right;
`;

const ExpenseAmount = styled(Text)<StyledProps>`
  font-size: 15px;
  color: #e3172a;
  font-family: ${(props: StyledProps) => props.theme.typography.bold};
  width: 65px;
  text-align: right;
  margin-left: 8px;
`;

const RunningTotalContainer = styled.View<StyledProps>`
  background-color: ${(props) => props.theme.colors.surface};
  border-radius: 8px;
  padding: 12px 16px;
  margin-top: 8px;
  border: 1px solid ${(props) => props.theme.colors.border};
`;

const RunningTotalText = styled(Text)<StyledProps>`
  font-size: 14px;
  color: ${(props) => props.theme.colors.text.secondary};
  font-family: 'Nunito';
  text-align: right;
`;

const RunningTotalAmount = styled(Text)<StyledProps>`
  font-size: 16px;
  color: ${(props) => props.theme.colors.text.primary};
  font-family: ${(props: StyledProps) => props.theme.typography.bold};
  text-align: right;
`;

const ColumnHeaderRow = styled.View`
  flex-direction: row;
  justify-content: space-between;
  align-items: center;
  padding: 8px 16px;
  margin-bottom: 8px;
`;

const ColumnHeaderText = styled(Text)<StyledProps>`
  font-size: 14px;
  color: ${(props) => props.theme.colors.text.secondary};
  font-family: ${(props: StyledProps) => props.theme.typography.bold};
`;

const ColumnHeaderContainer = styled.View`
  flex-direction: row;
  min-width: 140px;
  justify-content: flex-end;
`;

const IncomeHeader = styled(Text)<StyledProps>`
  font-size: 14px;
  color: #2e7d32;
  font-family: ${(props: StyledProps) => props.theme.typography.bold};
  width: 65px;
  text-align: right;
`;

const ExpenseHeader = styled(Text)<StyledProps>`
  font-size: 14px;
  color: #e3172a;
  font-family: ${(props: StyledProps) => props.theme.typography.bold};
  width: 65px;
  text-align: right;
  margin-left: 8px;
`;

const LoadingContainer = styled.View`
  flex: 1;
  justify-content: center;
  align-items: center;
`;

const ErrorContainer = styled.View<StyledProps>`
  background-color: ${(props) => props.theme.colors.surface};
  border-radius: 8px;
  padding: 20px;
  margin-bottom: 20px;
  align-items: center;
  border: 1px solid #e3172a;
`;

const ErrorText = styled(Text)`
  color: #e3172a;
  font-family: ${(theme: StyledProps) => theme.theme.typography.regular};
  text-align: center;
`;

const EmptyContainer = styled.View<StyledProps>`
  background-color: ${(props) => props.theme.colors.surface};
  border-radius: 8px;
  padding: 20px;
  align-items: center;
  border: 1px solid ${(props) => props.theme.colors.border};
`;

const EmptyText = styled(Text)<StyledProps>`
  color: ${(props) => props.theme.colors.text.secondary};
  font-family: 'Nunito';
  text-align: center;
`;

// Utility function to format transaction types
const formatTransactionType = (type: string): string => {
  const typeMap: Record<string, string> = {
    GROUND_MAINTENANCE: 'Ground Maintenance',
    DRINKS_INCOME: 'Drinks Income',
    MERCHANDISE_INCOME: 'Merchandise Income',
    PLAYER_WAGES: 'Player Wages',
    TICKET_INCOME: 'Ticket Income',
    PROGRAMME_INCOME: 'Programme Income',
    FOOD_INCOME: 'Food Income',
    transfer: 'Transfer',
  };

  return typeMap[type] || type.replace(/_/g, ' ').replace(/\b\w/g, (l) => l.toUpperCase());
};

// Utility function to format dates (date only for headers)
const formatTransactionDate = (timestamp: number): string => {
  const date = new Date(timestamp);
  return date.toLocaleDateString('en-GB', {
    weekday: 'long',
    day: 'numeric',
    month: 'long',
    year: 'numeric',
  });
};

// Utility function to get date key for grouping
const getDateKey = (timestamp: number): string => {
  const date = new Date(timestamp);
  return date.toLocaleDateString('en-GB', {
    day: '2-digit',
    month: '2-digit',
    year: 'numeric',
  });
};

const FinancesScreen: React.FC = () => {
  const { team, manager, loading: managerLoading } = useManager();
  const [transactions, setTransactions] = useState<Transaction[]>([]);
  const [loading, setLoading] = useState(true);
  const [refreshing, setRefreshing] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const fetchTransactions = useCallback(
    async (isRefresh = false) => {
      if (!manager?.gameworldId || !team?.teamId) {
        logger.log('Missing gameworldId or teamId for transactions fetch');
        return;
      }

      try {
        if (isRefresh) {
          setRefreshing(true);
        } else {
          setLoading(true);
        }
        setError(null);

        const response: TransactionsResponse = await callApi(
          `/${manager.gameworldId}/team/${team.teamId}/transactions?days=14`
        );

        // Sort transactions by date (newest first)
        const sortedTransactions = (response.transactions || []).sort((a, b) => b.date - a.date);
        setTransactions(sortedTransactions);
      } catch (err) {
        logger.error('Error fetching transactions:', err);
        setError('Failed to load transactions. Please try again.');
      } finally {
        setLoading(false);
        setRefreshing(false);
      }
    },
    [manager?.gameworldId, team?.teamId]
  );

  useEffect(() => {
    if (!managerLoading && manager?.gameworldId && team?.teamId) {
      fetchTransactions();
    }
  }, [manager?.gameworldId, team?.teamId, managerLoading, fetchTransactions]);

  const onRefresh = () => {
    fetchTransactions(true);
  };

  // Group transactions by date
  const groupedTransactions = transactions.reduce(
    (groups, transaction) => {
      const dateKey = getDateKey(transaction.date);
      if (!groups[dateKey]) {
        groups[dateKey] = [];
      }
      groups[dateKey].push(transaction);
      return groups;
    },
    {} as Record<string, Transaction[]>
  );

  // Calculate running totals
  const calculateRunningTotal = (transactionsUpToDate: Transaction[]): number => {
    const currentBalance = team?.balance ?? 0;
    const totalChange = transactionsUpToDate.reduce((sum, t) => sum + t.amount, 0);
    return currentBalance - totalChange;
  };

  const renderTransactionGroup = (dateKey: string, dayTransactions: Transaction[]) => {
    const transactionsUpToThisDate = transactions.filter(
      (t) => new Date(t.date) >= new Date(dayTransactions[0].date)
    );
    const runningTotal = calculateRunningTotal(transactionsUpToThisDate);

    return (
      <React.Fragment key={dateKey}>
        <DateHeader>
          <DateHeaderText>{formatTransactionDate(dayTransactions[0].date)}</DateHeaderText>
        </DateHeader>

        <ColumnHeaderRow>
          <ColumnHeaderText>Transaction</ColumnHeaderText>
          <ColumnHeaderContainer>
            <IncomeHeader>Income</IncomeHeader>
            <ExpenseHeader>Expense</ExpenseHeader>
          </ColumnHeaderContainer>
        </ColumnHeaderRow>

        {dayTransactions.map((transaction) => (
          <TransactionRow key={transaction.id}>
            <TransactionType>{formatTransactionType(transaction.type)}</TransactionType>
            <AmountContainer>
              <IncomeAmount>
                {transaction.amount > 0 ? formatCurrencyShort(transaction.amount) : ''}
              </IncomeAmount>
              <ExpenseAmount>
                {transaction.amount < 0 ? formatCurrencyShort(Math.abs(transaction.amount)) : ''}
              </ExpenseAmount>
            </AmountContainer>
          </TransactionRow>
        ))}

        <RunningTotalContainer>
          <RunningTotalText>Balance after {dateKey}</RunningTotalText>
          <RunningTotalAmount>{formatCurrencyShort(runningTotal)}</RunningTotalAmount>
        </RunningTotalContainer>
      </React.Fragment>
    );
  };

  if (managerLoading || (loading && !refreshing)) {
    return (
      <LoadingContainer>
        <ActivityIndicator size="large" />
      </LoadingContainer>
    );
  }

  return (
    <Container>
      <BalanceSection>
        <BalanceLabel>Current Balance</BalanceLabel>
        <BalanceAmount>{formatCurrencyLong(team?.balance ?? 0)}</BalanceAmount>
      </BalanceSection>

      <SectionTitle>Recent Transactions (Last 14 Days)</SectionTitle>

      {error && (
        <ErrorContainer>
          <MaterialIcons name="error" size={24} color="#e3172a" />
          <ErrorText>{error}</ErrorText>
        </ErrorContainer>
      )}

      {!error && transactions.length === 0 && !loading && (
        <EmptyContainer>
          <MaterialIcons name="receipt" size={48} color="#999" />
          <EmptyText>No transactions found for the last 14 days</EmptyText>
        </EmptyContainer>
      )}

      {!error && transactions.length > 0 && (
        <FlatList
          data={Object.entries(groupedTransactions)}
          renderItem={({ item: [dateKey, dayTransactions] }) =>
            renderTransactionGroup(dateKey, dayTransactions)
          }
          keyExtractor={([dateKey]) => dateKey}
          showsVerticalScrollIndicator={false}
          refreshControl={<RefreshControl refreshing={refreshing} onRefresh={onRefresh} />}
        />
      )}
    </Container>
  );
};

export default FinancesScreen;
