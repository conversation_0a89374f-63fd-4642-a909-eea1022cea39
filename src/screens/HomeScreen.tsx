import React from 'react';
import styled, { DefaultTheme } from 'styled-components/native';
import AccountUpgradePrompt from '../components/AccountUpgradePrompt';
import { TeamInfoWidget } from '../components/TeamInfoWidget';
import { useAccountUpgradePrompt } from '../hooks/useAccountUpgradePrompt';

interface StyledProps {
  theme: DefaultTheme;
}

const Container = styled.View`
  flex: 1;
  background-color: ${(props: StyledProps) => props.theme.colors.background};
`;

const HomeScreen = () => {
  const { promptState, hidePrompt } = useAccountUpgradePrompt();
  return (
    <Container>
      <TeamInfoWidget />
      {/* Account Upgrade Prompt for Anonymous Users */}
      <AccountUpgradePrompt
        visible={promptState.visible}
        onClose={hidePrompt}
        title={promptState.title}
        description={promptState.description}
        trigger={promptState.trigger}
      />
    </Container>
  );
};

export default HomeScreen;
