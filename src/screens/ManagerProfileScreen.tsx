import AsyncStorage from '@react-native-async-storage/async-storage';
import { router } from 'expo-router';
//import { englishDataset, englishRecommendedTransformers, RegExpMatcher } from 'obscenity';
import { Ionicons } from '@expo/vector-icons';
import React, { useEffect, useState } from 'react';
import { ActivityIndicator, Alert, Platform } from 'react-native';
import styled, { DefaultTheme } from 'styled-components/native';
import { callApi } from '../api/client';
import { Description } from '../components/Common';
import { CrossPlatformAlert } from '../components/CrossPlatformAlert';
import { Text } from '../components/Text';
import {
  CACHED_FIRST_NAME_KEY,
  CACHED_LAST_NAME_KEY,
  PROFILE_NEEDS_UPDATE_KEY,
  useManager,
} from '../context/ManagerContext';
import { useOnboarding } from '../hooks/useOnboarding';
import { useTheme } from '../theme/ThemeContext';
import { logger } from '../utils/logger';

interface StyledProps {
  theme: DefaultTheme;
}

const Container = styled.View`
  flex: 1;
  background-color: ${(props: StyledProps) => props.theme.colors.background};
  padding: 16px;
`;

const FormContainer = styled.View`
  margin-top: 24px;
`;

const InputContainer = styled.View`
  margin-bottom: 16px;
`;

const Label = styled(Text)`
  font-size: 16px;
  margin-bottom: 8px;
  font-family: ${(props: StyledProps) => props.theme.typography.bold};
`;

const Input = styled.TextInput`
  background-color: ${(props: StyledProps) => props.theme.colors.surface};
  padding: 12px 16px;
  border-radius: 8px;
  border-width: 1px;
  border-color: ${(props: StyledProps) => props.theme.colors.border};
  font-family: ${(props: StyledProps) => props.theme.typography.regular};
  color: ${(props: StyledProps) => props.theme.colors.text.primary};
  font-size: 16px;
`;

const SaveButton = styled.TouchableOpacity`
  background-color: ${(props: StyledProps) => props.theme.colors.primary};
  padding: 16px;
  border-radius: 8px;
  align-items: center;
  margin-top: 24px;
`;

const ButtonText = styled(Text)`
  color: white;
  font-size: 16px;
  font-family: ${(props: StyledProps) => props.theme.typography.bold};
`;

const ThemeToggleRow = styled.View`
  flex-direction: row;
  align-items: center;
  justify-content: space-between;
  margin-top: 32px;
  margin-bottom: 24px;
`;

const ThemeToggleCaption = styled(Text)`
  font-size: 16px;
  color: ${({ theme }) => theme.colors.text.secondary};
  font-family: ${({ theme }) => theme.typography.bold};
  margin-right: 16px;
`;

const ThemeToggleContainer = styled.View`
  flex-direction: row;
  align-items: center;
  justify-content: flex-end;
`;

const ToggleOption = styled.TouchableOpacity<{
  selected: boolean;
  first?: boolean;
  last?: boolean;
}>`
  align-items: center;
  justify-content: center;
  padding-vertical: 10px;
  border-width: 1px;
  border-color: ${({ theme }) => theme.colors.border};
  background-color: ${({ selected, theme }) =>
    selected ? theme.colors.primary : theme.colors.surface};
  border-left-width: ${({ first }) => (first ? '1px' : '0')};
  border-top-left-radius: ${({ first }) => (first ? '20px' : '0')};
  border-bottom-left-radius: ${({ first }) => (first ? '20px' : '0')};
  border-top-right-radius: ${({ last }) => (last ? '20px' : '0')};
  border-bottom-right-radius: ${({ last }) => (last ? '20px' : '0')};
  min-width: 88px;
  max-width: 88px;
`;

const ToggleText = styled(Text)<{ selected: boolean }>`
  color: ${({ selected, theme }) => (selected ? 'white' : theme.colors.text.primary)};
  text-align: center;
  font-family: ${({ theme }) => theme.typography.bold};
`;

const ManagerProfileScreen = () => {
  const { manager, refreshManager } = useManager();
  const { theme, themePreference, setThemePreference } = useTheme();
  const { navigateToNextStep, hasOnboarded } = useOnboarding();

  const [firstName, setFirstName] = useState('');
  const [lastName, setLastName] = useState('');
  const [isSaving, setIsSaving] = useState(false);
  const [showConfirmation, setShowConfirmation] = useState(false);
  const [isInitialized, setIsInitialized] = useState(false);
  const [dialogMessage, setDialogMessage] = useState<{ title: string; message: string }>({
    title: '',
    message: '',
  });

  /*  const matcher = new RegExpMatcher({
    ...englishDataset.build(),
    ...englishRecommendedTransformers,
  });*/

  // Load cached names or manager data on initial render
  useEffect(() => {
    const loadProfileData = async () => {
      try {
        // Try to load cached names first
        const [cachedFirstName, cachedLastName] = await Promise.all([
          AsyncStorage.getItem(CACHED_FIRST_NAME_KEY),
          AsyncStorage.getItem(CACHED_LAST_NAME_KEY),
        ]);

        // If we have manager data, use that (it's the source of truth)
        if (manager?.firstName || manager?.lastName) {
          setFirstName(manager.firstName || '');
          setLastName(manager.lastName || '');
        }
        // Otherwise use cached data if available
        else if (cachedFirstName || cachedLastName) {
          setFirstName(cachedFirstName || '');
          setLastName(cachedLastName || '');
        }

        setIsInitialized(true);
      } catch (error) {
        logger.error('Error loading profile data:', error);
        // Fall back to manager data if available
        if (manager) {
          setFirstName(manager.firstName || '');
          setLastName(manager.lastName || '');
        }
        setIsInitialized(true);
      }
    };

    loadProfileData();
  }, [manager]);

  const handleSave = async () => {
    if (!firstName.trim() || !lastName.trim()) {
      if (Platform.OS === 'web') {
        setShowConfirmation(true);
        setDialogMessage({ title: 'Error', message: 'Please enter both first and last name' });
      } else {
        Alert.alert('Error', 'Please enter both first and last name');
      }
      return;
    }

    /*if (matcher.hasMatch(firstName) || matcher.hasMatch(lastName)) {
      if (Platform.OS === 'web') {
        setShowConfirmation(true);
        setDialogMessage({ title: 'Error', message: 'Very rude' });
      } else {
        Alert.alert('Error', 'Very rude');
      }
      return;
    }*/

    setIsSaving(true);
    try {
      // Always cache the names regardless of manager availability
      await Promise.all([
        AsyncStorage.setItem(CACHED_FIRST_NAME_KEY, firstName.trim()),
        AsyncStorage.setItem(CACHED_LAST_NAME_KEY, lastName.trim()),
      ]);

      // If manager exists, update via API
      if (manager?.managerId) {
        const response = await callApi('/manager/name', {
          method: 'PUT',
          body: JSON.stringify({
            firstName: firstName.trim(),
            lastName: lastName.trim(),
          }),
        });

        logger.log('Profile update response:', response);

        // Invalidate the manager query to refetch the updated data
        await refreshManager();

        // Clear the update flag since we've successfully updated the profile
        await AsyncStorage.setItem(PROFILE_NEEDS_UPDATE_KEY, 'false');
      } else {
        // If manager doesn't exist yet, set flag to update later
        await AsyncStorage.setItem(PROFILE_NEEDS_UPDATE_KEY, 'true');
      }

      if (Platform.OS === 'web') {
        setShowConfirmation(true);
        setDialogMessage({ title: 'Success', message: 'Profile saved successfully' });
      } else {
        Alert.alert('Success', 'Profile saved successfully');
      }

      // Navigate based on onboarding status
      if (hasOnboarded) {
        router.replace('/');
      } else {
        await navigateToNextStep();
      }
    } catch (error) {
      logger.error('Error saving profile:', error);
      if (Platform.OS === 'web') {
        setShowConfirmation(true);
        if ((error as any).status === 406) {
          setDialogMessage({ title: 'Error', message: 'Rude!' });
        } else {
          setDialogMessage({
            title: 'Error',
            message: 'Failed to save profile. Please try again.',
          });
        }
      } else {
        Alert.alert('Error', 'Failed to save profile. Please try again.');
      }
    } finally {
      setIsSaving(false);
    }
  };

  if (!isInitialized) {
    return (
      <Container>
        <ActivityIndicator size="large" color={theme.colors.primary} testID="loading-indicator" />
      </Container>
    );
  }

  return (
    <Container>
      <Description>
        Welcome to the job center. We'll have you back doing... *checks notes*... owning and
        managing an amateur football club in no time. First things first.... who are you?!
      </Description>
      <FormContainer>
        <InputContainer>
          <Label>First Name</Label>
          <Input
            value={firstName}
            onChangeText={setFirstName}
            placeholder="Enter your first name"
            placeholderTextColor={theme.colors.text.secondary}
          />
        </InputContainer>

        <InputContainer>
          <Label>Last Name</Label>
          <Input
            value={lastName}
            onChangeText={setLastName}
            placeholder="Enter your last name"
            placeholderTextColor={theme.colors.text.secondary}
          />
        </InputContainer>

        <SaveButton onPress={handleSave} disabled={isSaving}>
          {isSaving ? (
            <ActivityIndicator size="small" color="white" />
          ) : (
            <ButtonText>Save Profile</ButtonText>
          )}
        </SaveButton>
      </FormContainer>

      <ThemeToggleRow>
        <ThemeToggleCaption>Theme</ThemeToggleCaption>
        <ThemeToggleContainer>
          {['light', 'dark', 'system'].map((option, idx, arr) => {
            let iconName: keyof typeof Ionicons.glyphMap = 'sunny-outline';
            if (option === 'dark') iconName = 'moon-outline';
            if (option === 'system') iconName = 'cog-outline';
            return (
              <ToggleOption
                key={option}
                selected={themePreference === option}
                onPress={() => setThemePreference(option as any)}
                accessibilityRole="button"
                accessibilityState={{ selected: themePreference === option }}
                first={idx === 0}
                last={idx === arr.length - 1}
              >
                <Ionicons
                  name={iconName}
                  size={22}
                  color={themePreference === option ? 'white' : theme.colors.text.primary}
                  style={{ alignSelf: 'center' }}
                />
                <ToggleText selected={themePreference === option}>
                  {option.charAt(0).toUpperCase() + option.slice(1)}
                </ToggleText>
              </ToggleOption>
            );
          })}
        </ThemeToggleContainer>
      </ThemeToggleRow>

      <CrossPlatformAlert
        visible={showConfirmation}
        title={dialogMessage.title}
        message={dialogMessage.message}
        buttons={[
          {
            text: 'OK',
            onPress: () => setShowConfirmation(false),
          },
        ]}
        onDismiss={() => setShowConfirmation(false)}
      />
    </Container>
  );
};

export default ManagerProfileScreen;
