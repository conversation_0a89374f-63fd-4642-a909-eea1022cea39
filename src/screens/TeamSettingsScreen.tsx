import { MaterialIcons } from '@expo/vector-icons';
import React, { useState } from 'react';
import { ScrollView } from 'react-native';
import styled, { DefaultTheme } from 'styled-components/native';
import { ChevronIcon } from '../components/PlayerRow/SharedComponents';
import { TeamNameChangeModal } from '../components/TeamNameChangeModal';
import { Text } from '../components/Text';
import { useManager } from '../context/ManagerContext';
import { useTheme } from '../theme/ThemeContext';

interface StyledProps {
  theme: DefaultTheme;
}

const Container = styled.View`
  flex: 1;
  background-color: ${(props: StyledProps) => props.theme.colors.background};
`;

const Header = styled.View`
  padding: 20px;
  background-color: ${(props: StyledProps) => props.theme.colors.surface};
  border-bottom-width: 1px;
  border-bottom-color: ${(props: StyledProps) => props.theme.colors.border};
`;

const HeaderTitle = styled(Text)`
  font-size: 24px;
  font-family: ${(props: StyledProps) => props.theme.typography.bold};
  color: ${(props: StyledProps) => props.theme.colors.text.primary};
  margin-bottom: 8px;
`;

const HeaderSubtitle = styled(Text)`
  font-size: 16px;
  color: ${(props: StyledProps) => props.theme.colors.text.secondary};
  line-height: 22px;
`;

const SettingsContainer = styled.View`
  padding: 20px;
`;

const SettingItem = styled.TouchableOpacity`
  flex-direction: row;
  align-items: center;
  justify-content: space-between;
  background-color: ${(props: StyledProps) => props.theme.colors.surface};
  border-radius: 12px;
  padding: 16px;
  margin-bottom: 12px;
  border: 1px solid ${(props: StyledProps) => props.theme.colors.border};
`;

const SettingItemLeft = styled.View`
  flex-direction: row;
  align-items: center;
  flex: 1;
`;

const SettingItemIcon = styled.View`
  width: 40px;
  height: 40px;
  border-radius: 20px;
  background-color: ${(props: StyledProps) => props.theme.colors.primary};
  align-items: center;
  justify-content: center;
  margin-right: 16px;
`;

const SettingItemContent = styled.View`
  flex: 1;
`;

const SettingItemTitle = styled(Text)`
  font-size: 16px;
  font-family: ${(props: StyledProps) => props.theme.typography.bold};
  color: ${(props: StyledProps) => props.theme.colors.text.primary};
  margin-bottom: 4px;
`;

const SettingItemDescription = styled(Text)`
  font-size: 14px;
  color: ${(props: StyledProps) => props.theme.colors.text.secondary};
  line-height: 18px;
`;

const SettingItemRight = styled.View`
  align-items: center;
  justify-content: center;
`;

const TeamInfoContainer = styled.View`
  background-color: ${(props: StyledProps) => props.theme.colors.surface};
  border-radius: 12px;
  padding: 16px;
  margin-bottom: 24px;
  border: 1px solid ${(props: StyledProps) => props.theme.colors.border};
`;

const TeamInfoRow = styled.View`
  flex-direction: row;
  align-items: center;
  margin-bottom: 8px;
`;

const TeamInfoLabel = styled(Text)`
  font-size: 14px;
  color: ${(props: StyledProps) => props.theme.colors.text.secondary};
  margin-right: 8px;
  min-width: 80px;
`;

const TeamInfoValue = styled(Text)`
  font-size: 16px;
  font-family: ${(props: StyledProps) => props.theme.typography.bold};
  color: ${(props: StyledProps) => props.theme.colors.text.primary};
  flex: 1;
`;

const StatusBadge = styled.View<{ isChanged: boolean }>`
  background-color: ${(props) => (props.isChanged ? '#e3172a' : '#4CAF50')};
  border-radius: 12px;
  padding: 4px 8px;
  margin-left: 8px;
`;

const StatusBadgeText = styled(Text)`
  color: white;
  font-size: 12px;
  font-family: ${(props: StyledProps) => props.theme.typography.bold};
`;

const TeamSettingsScreen: React.FC = () => {
  const { theme } = useTheme();
  const { manager, team } = useManager();
  const [showNameChangeModal, setShowNameChangeModal] = useState(false);

  const hasChangedNameBefore = manager?.changedTeamName === true;

  const handleNameChangeSuccess = () => {
    // The modal handles the state updates, so we just need to close it
    setShowNameChangeModal(false);
  };

  return (
    <Container>
      <ScrollView showsVerticalScrollIndicator={false}>
        <Header>
          <HeaderTitle>Team Settings</HeaderTitle>
          <HeaderSubtitle>Manage your team's identity and preferences</HeaderSubtitle>
        </Header>

        <SettingsContainer>
          {/* Team Information */}
          <TeamInfoContainer>
            <TeamInfoRow>
              <TeamInfoLabel>Team Name:</TeamInfoLabel>
              <TeamInfoValue>{team?.teamName || 'Unknown Team'}</TeamInfoValue>
            </TeamInfoRow>
            <TeamInfoRow>
              <TeamInfoLabel>League:</TeamInfoLabel>
              <TeamInfoValue>{team?.league?.id || 'Unknown League'}</TeamInfoValue>
            </TeamInfoRow>
            <TeamInfoRow>
              <TeamInfoLabel>Players:</TeamInfoLabel>
              <TeamInfoValue>{team?.players?.length || 0}</TeamInfoValue>
            </TeamInfoRow>
          </TeamInfoContainer>

          {/* Team Name Change Setting */}
          <SettingItem onPress={() => setShowNameChangeModal(true)}>
            <SettingItemLeft>
              <SettingItemIcon>
                <MaterialIcons name="edit" size={20} color="white" />
              </SettingItemIcon>
              <SettingItemContent>
                <SettingItemTitle>Change Team Name</SettingItemTitle>
                <SettingItemDescription>
                  {hasChangedNameBefore
                    ? 'Watch an ad to change your team name again'
                    : 'Change your team name once for free'}
                </SettingItemDescription>
              </SettingItemContent>
            </SettingItemLeft>
            <SettingItemRight>
              <StatusBadge isChanged={hasChangedNameBefore}>
                <StatusBadgeText>{hasChangedNameBefore ? 'Used' : 'Free'}</StatusBadgeText>
              </StatusBadge>
              <ChevronIcon />
            </SettingItemRight>
          </SettingItem>
        </SettingsContainer>
      </ScrollView>

      <TeamNameChangeModal
        visible={showNameChangeModal}
        onClose={() => setShowNameChangeModal(false)}
        onSuccess={handleNameChangeSuccess}
      />
    </Container>
  );
};

export default TeamSettingsScreen;
