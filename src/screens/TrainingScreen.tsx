import { MaterialIcons } from '@expo/vector-icons';
import analytics from '@react-native-firebase/analytics';
import { Skeleton } from 'moti/skeleton';
import React, { useEffect, useState } from 'react';
import { Alert, ScrollView, TouchableOpacity, View } from 'react-native';
import styled, { DefaultTheme } from 'styled-components/native';
import { callApi } from '../api/client';
import { CrossPlatformAlert } from '../components/CrossPlatformAlert';
import { Text } from '../components/Text';
import TrainingModal from '../components/TrainingModal';
import TrainingSlot, { ArrowContainer } from '../components/TrainingSlot';
import { useManager } from '../context/ManagerContext';
import { usePurchases } from '../context/PurchaseContext';
import { useTeam } from '../hooks/useQueries';
import {
  TrainingSlotData,
  TrainingSlotsResponse,
  useAssignTrainingSlot,
  useTrainingSlots,
  useUnlockTrainingSlot,
} from '../hooks/useTrainingSlots';
import { Player } from '../models/player';
import { UpgradeTrainingResponse } from '../models/training';
import { logger } from '../utils/logger';
import { formatCurrencyShort } from '../utils/utils';
import { ChevronIcon } from '../components/PlayerRow/SharedComponents';

const Spacer = ({ height = 16 }) => <View style={{ height }} />;

interface StyledProps {
  theme: DefaultTheme;
}

const Container = styled.View<StyledProps>`
  flex: 1;
  background-color: ${(props) => props.theme.colors.background};
`;
const Header = styled.View<StyledProps>`
  background-color: ${(props) => props.theme.colors.surface};
  padding: 20px;
  margin: 16px;
  border-radius: 12px;
  shadow-color: #000;
  shadow-offset: 0px 2px;
  shadow-opacity: 0.1;
  shadow-radius: 2px;
`;

const HeaderTitle = styled(Text)<StyledProps>`
  font-size: 20px;
  font-family: ${(props) => props.theme.typography.bold};
  color: ${(props) => props.theme.colors.text.primary};
  text-align: center;
  margin-bottom: 16px;
`;

const QualityContainer = styled.View`
  flex-direction: row;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
`;

const QualityInfo = styled.View`
  flex: 1;
`;

const QualityLabel = styled(Text)<StyledProps>`
  font-size: 14px;
  color: ${(props) => props.theme.colors.text.secondary};
  font-family: 'Nunito';
`;

const QualityValue = styled(Text)<StyledProps>`
  font-size: 18px;
  color: ${(props) => props.theme.colors.text.primary};
  font-family: ${(props) => props.theme.typography.bold};
`;

const UpgradeButton = styled(TouchableOpacity)<StyledProps>`
  background-color: ${(props) => props.theme.colors.primary};
  padding: 12px 20px;
  border-radius: 8px;
  flex-direction: row;
  align-items: center;
`;

const UpgradeButtonText = styled(Text)<StyledProps>`
  color: ${(props) => props.theme.colors.surface};
  font-family: ${(props) => props.theme.typography.bold};
  margin-left: 8px;
`;

const SlotsContainer = styled.View`
  flex: 1;
`;

const UnlockButtonsContainer = styled.View`
  flex-direction: row;
  gap: 8px;
  width: 100%;
  padding: 0 16px;
  align-items: center;
`;

const OrText = styled.Text`
  font-family: ${(props: StyledProps) => props.theme.typography.bold};
  font-size: 16px;
`;

const SectionTitle = styled(Text)<StyledProps>`
  font-size: 18px;
  font-family: ${(props) => props.theme.typography.bold};
  color: ${(props) => props.theme.colors.text.primary};
  margin: 16px 16px 8px 16px;
`;

const SkeletonSlotContainer = styled.View`
  flex-direction: row;
  align-items: center;
  background-color: ${(props) => props.theme.colors.surface};

  border-radius: 8px;
  padding: 16px;
  margin: 8px 16px;
  min-height: 120px;

  border-color: ${(props) => props.theme.colors.text.secondary};
  opacity: ${() => 0.6};
  shadow-color: #000;
  shadow-offset: 0px 2px;
  shadow-opacity: 0.1;
  shadow-radius: 2px;
`;
styled(Skeleton)`
  border-radius: 50px;
`;
const SkeletonSlotContent = styled.View`
  flex: 1;
`;

const TrainingScreen = () => {
  const { team, loading, manager } = useManager();
  const { data: teamData } = useTeam(manager?.gameworldId, manager?.team?.teamId);
  const { offerings, purchasePackage } = usePurchases();

  // Training ground state
  const [trainingGroundQuality, setTrainingGroundQuality] = useState(
    manager?.team?.trainingLevel || 1
  );
  const [teamBalance, setTeamBalance] = useState(team?.balance || 0);
  useEffect(() => {
    setTeamBalance(team?.balance || 0);
  }, [team?.balance]);

  const [showUpgradeAlert, setShowUpgradeAlert] = useState(false);
  const [showTrainingModal, setShowTrainingModal] = useState(false);
  const [selectedSlotIndex, setSelectedSlotIndex] = useState<number | null>(null);
  const [showUnlockAlert, setShowUnlockAlert] = useState(false);
  const [slotToUnlock, setSlotToUnlock] = useState<number | null>(null);
  const [errorDialog, setErrorDialog] = useState<string | null>(null);

  // Use new hook for training slots
  const { data: trainingSlots, isLoading: slotsLoading } = useTrainingSlots(teamData?.teamId);
  const unlockSlotMutation = useUnlockTrainingSlot();
  const assignSlotMutation = useAssignTrainingSlot();

  // Local state for optimistic updates
  const [localTrainingSlots, setLocalTrainingSlots] = useState<TrainingSlotsResponse | null>(null);

  // Sync local state with React Query data
  useEffect(() => {
    if (trainingSlots) {
      setLocalTrainingSlots(trainingSlots);
    }
  }, [trainingSlots]);

  // Use local state if available, fallback to React Query data
  const currentTrainingSlots = localTrainingSlots || trainingSlots;

  const upgradeQualityCost = trainingGroundQuality * 100000; // £100k per level

  // Get IAP package for slot 5
  const slot5IAPPackage = offerings?.availablePackages?.find(
    (pkg) => pkg.identifier === 'jfg_training_slot_5'
  );

  const handleSlotPress = (slotIndex: number) => {
    const slot = currentTrainingSlots?.slots[slotIndex];
    if (!slot) return;
    if (slot.locked) {
      // For slot 5, only show alert for in-game currency unlock
      if (slotIndex === 4) {
        setSlotToUnlock(slotIndex);
        setShowUnlockAlert(true);
      } else {
        setSlotToUnlock(slotIndex);
        setShowUnlockAlert(true);
      }
    } else {
      setSelectedSlotIndex(slotIndex);
      setShowTrainingModal(true);
    }
  };

  const handleIAPUnlockSlot = async () => {
    if (!slot5IAPPackage || !currentTrainingSlots) {
      Alert.alert('Error', 'Training slot 5 purchase is not available at the moment.');
      return;
    }

    // Store original state for potential revert

    try {
      const success = await purchasePackage(slot5IAPPackage);
      if (success) {
        // Optimistic update - immediately unlock slot 5 (index 4) in local state
        const updatedSlots = [...currentTrainingSlots.slots];
        if (updatedSlots[4]) {
          updatedSlots[4] = { ...updatedSlots[4], locked: false };
          setLocalTrainingSlots({ slots: updatedSlots });
        }

        Alert.alert(
          'Purchase Successful!',
          'Training slot 5 has been unlocked with your purchase.',
          [{ text: 'OK' }]
        );
        await analytics().logEvent('purchase_completed', {
          product_id: slot5IAPPackage.identifier,
          price: slot5IAPPackage.product.priceString,
        });
      } else {
        Alert.alert(
          'Purchase Failed',
          'There was an issue processing your purchase. Please try again.',
          [{ text: 'OK' }]
        );
      }
    } catch (error) {
      // Revert optimistic update on error
      setLocalTrainingSlots(currentTrainingSlots);
      logger.error('IAP purchase error:', error);
      Alert.alert('Purchase Error', 'An unexpected error occurred. Please try again later.', [
        { text: 'OK' },
      ]);
    }
  };

  const handleUnlockSlot = async () => {
    if (slotToUnlock !== null && currentTrainingSlots) {
      const slot = currentTrainingSlots.slots[slotToUnlock];
      if (!slot) return;
      const cost = slot.unlockCost || 0;
      if ((teamBalance || 0) >= cost) {
        // Optimistic update - immediately unlock the slot in local state
        const updatedSlots = [...currentTrainingSlots.slots];
        updatedSlots[slotToUnlock] = { ...slot, locked: false };
        setLocalTrainingSlots({ slots: updatedSlots });

        // Update balance optimistically
        const newBalance = (teamBalance || 0) - cost;
        setTeamBalance(newBalance);

        try {
          const res = await unlockSlotMutation.mutateAsync(slot.slotId);
          if (res.status === 200) {
            // Update balance with actual server response
            setTeamBalance(res.balance);
            await analytics().logEvent('slot_unlocked', {
              slot_id: slot.slotId,
              cost,
            });
          } else {
            // Revert optimistic updates on failure
            setLocalTrainingSlots(currentTrainingSlots);
            setTeamBalance(teamBalance || 0);
            setErrorDialog('Failed to unlock slot.');
          }
        } catch (error: any) {
          // Revert optimistic updates on error
          setLocalTrainingSlots(currentTrainingSlots);
          setTeamBalance(teamBalance || 0);
          setErrorDialog(error?.message || 'Failed to unlock slot.');
        }
      } else {
        setErrorDialog('Insufficient funds');
      }
    }
    setSlotToUnlock(null);
    setShowUnlockAlert(false);
  };

  const handleAssignPlayer = async (player: Player, attribute: string) => {
    if (selectedSlotIndex !== null && currentTrainingSlots) {
      const slot = currentTrainingSlots.slots[selectedSlotIndex];
      if (!slot) return;

      // Store original state for potential revert
      const originalSlots = currentTrainingSlots;

      // Optimistic update - immediately assign the player in local state
      const attributeValue = player.attributes[attribute as keyof typeof player.attributes];
      const numericValue = typeof attributeValue === 'number' ? attributeValue : 0;

      const updatedSlots = [...currentTrainingSlots.slots];
      updatedSlots[selectedSlotIndex] = {
        ...slot,
        player,
        attribute,
        startValue: numericValue,
        currentValue: numericValue,
      };
      setLocalTrainingSlots({ slots: updatedSlots });

      try {
        await assignSlotMutation.mutateAsync({
          slotId: slot.id,
          playerId: player.playerId,
          attribute,
          slotIndex: slot.slotId,
        });
        await analytics().logEvent('player_assigned_to_slot', {
          slot_id: slot.slotId,
          player_id: player.playerId,
          attribute,
        });
      } catch {
        // Revert optimistic update on error
        setLocalTrainingSlots(originalSlots);
        setErrorDialog('Failed to assign player to slot.');
      }
    }
    setShowTrainingModal(false);
    setSelectedSlotIndex(null);
  };

  const handleUpgradeQuality = async () => {
    setShowUpgradeAlert(false);
    if (!manager?.gameworldId || !teamData?.teamId) return;
    try {
      const res = await callApi<UpgradeTrainingResponse>(
        `/${manager.gameworldId}/team/${teamData.teamId}/upgrade-training`,
        {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify({}),
        }
      );
      if (res.status === 200) {
        if (res.trainingLevel) setTrainingGroundQuality(res.trainingLevel);
        if (typeof res?.balance === 'number') setTeamBalance(res.balance);
        await analytics().logEvent('training_ground_upgraded', {
          new_level: res.trainingLevel,
          cost: upgradeQualityCost,
        });
      }
    } catch (error: any) {
      setErrorDialog(error?.message || 'Failed to upgrade training ground.');
      logger.error('Failed to upgrade training ground:', error);
    }
  };

  if (loading || slotsLoading) {
    return (
      <Container>
        <ScrollView>
          <Header>
            <Skeleton height={28} width={180} radius={8} colorMode="light" />
            <Spacer height={12} />
            <QualityContainer>
              <QualityInfo>
                <Skeleton height={18} width={120} radius={6} colorMode="light" />
                <Spacer height={8} />
                <Skeleton height={22} width={80} radius={6} colorMode="light" />
                <Spacer height={8} />
                <Skeleton height={18} width={100} radius={6} colorMode="light" />
              </QualityInfo>
              <Skeleton height={40} width={120} radius={20} colorMode="light" />
            </QualityContainer>
          </Header>
          <SlotsContainer>
            <SectionTitle>Training Slots</SectionTitle>
            {[...Array(3)].map((_, i) => (
              <SkeletonSlotContainer key={i}>
                <SkeletonSlotContent>
                  <Skeleton height={16} width={100} radius={6} colorMode="light" />
                  <Spacer height={8} />
                  <Skeleton height={18} width={140} radius={6} colorMode="light" />
                  <Spacer height={8} />
                  <Skeleton height={14} width={90} radius={6} colorMode="light" />
                </SkeletonSlotContent>
                <ArrowContainer>
                  <ChevronIcon isDisabled />
                </ArrowContainer>
              </SkeletonSlotContainer>
            ))}
          </SlotsContainer>
        </ScrollView>
      </Container>
    );
  }

  const availablePlayers = teamData?.players || team?.players || [];

  return (
    <Container>
      <ScrollView>
        <Header>
          <HeaderTitle>Training Ground</HeaderTitle>

          <QualityContainer>
            <QualityInfo>
              <QualityLabel>Training Ground Quality</QualityLabel>
              <QualityValue>Level {trainingGroundQuality}</QualityValue>
              <QualityLabel>Balance: {formatCurrencyShort(teamBalance)}</QualityLabel>
            </QualityInfo>

            <UpgradeButton onPress={() => setShowUpgradeAlert(true)}>
              <MaterialIcons name="upgrade" size={20} color="white" />
              <UpgradeButtonText>
                Upgrade ({formatCurrencyShort(upgradeQualityCost)})
              </UpgradeButtonText>
            </UpgradeButton>
          </QualityContainer>
        </Header>

        <SlotsContainer>
          <SectionTitle>Training Slots</SectionTitle>

          {currentTrainingSlots?.slots.map((slot: TrainingSlotData, index: number) => {
            return (
              <UnlockButtonsContainer>
                <TrainingSlot
                  key={index}
                  slotIndex={index}
                  isUnlocked={!slot.locked}
                  unlockCost={slot.unlockCost}
                  assignedPlayer={slot.player}
                  trainingAttribute={slot.attribute}
                  startValue={slot.startValue}
                  currentValue={slot.currentValue}
                  onPress={() => handleSlotPress(index)}
                />
                {index === 4 && slot.locked && (
                  <>
                    <OrText>OR</OrText>
                    <TrainingSlot
                      key={index}
                      slotIndex={index}
                      isUnlocked={!slot.locked}
                      unlockCost={slot5IAPPackage?.product.priceString}
                      assignedPlayer={slot.player}
                      trainingAttribute={slot.attribute}
                      startValue={slot.startValue}
                      currentValue={slot.currentValue}
                      onPress={() => handleIAPUnlockSlot()}
                    />
                  </>
                )}
              </UnlockButtonsContainer>
            );
          })}
        </SlotsContainer>
      </ScrollView>

      {/* Training Assignment Modal */}
      <TrainingModal
        visible={showTrainingModal}
        players={availablePlayers}
        onClose={() => {
          setShowTrainingModal(false);
          setSelectedSlotIndex(null);
        }}
        onAssign={handleAssignPlayer}
      />

      {/* Unlock Slot Confirmation */}
      <CrossPlatformAlert
        visible={showUnlockAlert}
        title="Unlock Training Slot"
        message={`Unlock training slot ${(slotToUnlock || 0) + 1} for ${
          slotToUnlock !== null
            ? formatCurrencyShort(currentTrainingSlots?.slots[slotToUnlock]?.unlockCost || 0)
            : ''
        }?`}
        buttons={[
          {
            text: 'Cancel',
            style: 'cancel',
            onPress: () => {
              setShowUnlockAlert(false);
              setSlotToUnlock(null);
            },
          },
          {
            text: 'Unlock',
            onPress: handleUnlockSlot,
          },
        ]}
        onDismiss={() => {
          setShowUnlockAlert(false);
          setSlotToUnlock(null);
        }}
      />

      {/* Upgrade Quality Confirmation */}
      <CrossPlatformAlert
        visible={showUpgradeAlert}
        title="Upgrade Training Ground"
        message={`Upgrade training ground to level ${trainingGroundQuality + 1} for ${formatCurrencyShort(upgradeQualityCost)}?`}
        buttons={[
          {
            text: 'Cancel',
            style: 'cancel',
            onPress: () => setShowUpgradeAlert(false),
          },
          {
            text: 'Upgrade',
            onPress: handleUpgradeQuality,
          },
        ]}
        onDismiss={() => setShowUpgradeAlert(false)}
      />

      {/* Error Dialog */}
      <CrossPlatformAlert
        visible={!!errorDialog}
        title="Error"
        message={errorDialog || ''}
        buttons={[
          {
            text: 'OK',
            onPress: () => setErrorDialog(null),
          },
        ]}
        onDismiss={() => setErrorDialog(null)}
      />
    </Container>
  );
};

export default TrainingScreen;
