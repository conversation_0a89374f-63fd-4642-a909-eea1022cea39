import { MaterialIcons } from '@expo/vector-icons';
import React from 'react';
import { ActivityIndicator, ScrollView } from 'react-native';
import styled, { DefaultTheme } from 'styled-components/native';
import { Text } from '../components/Text';
import { useManager } from '../context/ManagerContext';
import { formatCurrencyLong } from '../utils/utils';

interface StyledProps {
  theme: DefaultTheme;
}

const Container = styled.View`
  flex: 1;
  background-color: ${(props: StyledProps) => props.theme.colors.background};
  padding: 16px;
`;

const Title = styled(Text)`
  font-size: 28px;
  color: ${(props: StyledProps) => props.theme.colors.text.primary};
  font-family: ${(props: StyledProps) => props.theme.typography.bold};
  text-align: center;
  margin-bottom: 24px;
`;

const StatsGrid = styled.View`
  gap: 16px;
`;

const StatCard = styled.View`
  background-color: ${(props: StyledProps) => props.theme.colors.surface};
  border-radius: 12px;
  padding: 20px;
  border-left-width: 4px;
  border-left-color: ${(props: StyledProps) => props.theme.colors.primary};
`;

const StatHeader = styled.View`
  flex-direction: row;
  align-items: center;
  margin-bottom: 12px;
`;

const StatIcon = styled.View`
  margin-right: 12px;
`;

const StatTitle = styled(Text)`
  font-size: 18px;
  color: ${(props: StyledProps) => props.theme.colors.text.primary};
  font-family: ${(props: StyledProps) => props.theme.typography.bold};
`;

const StatRow = styled.View`
  flex-direction: row;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
`;

const StatLabel = styled(Text)`
  font-size: 16px;
  color: ${(props: StyledProps) => props.theme.colors.text.secondary};
  font-family: ${(props: StyledProps) => props.theme.typography.regular};
`;

const StatValue = styled(Text)`
  font-size: 18px;
  color: ${(props: StyledProps) => props.theme.colors.text.primary};
  font-family: ${(props: StyledProps) => props.theme.typography.bold};
`;

const HighlightValue = styled(StatValue)`
  color: ${(props: StyledProps) => props.theme.colors.primary};
`;

const TrophyCard = styled(StatCard)`
  border-left-color: #ffd700;
  background-color: ${(props: StyledProps) => props.theme.colors.surface};
`;

const TrophyCount = styled(Text)`
  font-size: 48px;
  color: #ffd700;
  font-family: ${(props: StyledProps) => props.theme.typography.bold};
  text-align: center;
  margin: 16px 0;
`;

const TrophyRoomScreen = () => {
  const { manager, loading } = useManager();

  if (loading || !manager) {
    return (
      <Container>
        <ActivityIndicator size="large" />
      </Container>
    );
  }

  const totalGames = manager.wins + manager.draws + manager.defeats;
  const winPercentage = totalGames > 0 ? ((manager.wins / totalGames) * 100).toFixed(1) : '0.0';
  const goalDifference = manager.goalsScored - manager.goalsConceded;

  return (
    <Container>
      <Title>🏆 Trophy Room 🏆</Title>
      
      <ScrollView showsVerticalScrollIndicator={false}>
        <StatsGrid>
          {/* Trophies Section */}
          <TrophyCard>
            <StatHeader>
              <StatIcon>
                <MaterialIcons name="emoji-events" size={24} color="#ffd700" />
              </StatIcon>
              <StatTitle>Trophies Won</StatTitle>
            </StatHeader>
            <TrophyCount>{manager.trophies}</TrophyCount>
            {manager.trophies > 0 ? (
              <StatLabel style={{ textAlign: 'center' }}>
                🎉 Congratulations on your achievements! 🎉
              </StatLabel>
            ) : (
              <StatLabel style={{ textAlign: 'center' }}>
                Your first trophy awaits! Keep pushing forward! 💪
              </StatLabel>
            )}
          </TrophyCard>

          {/* Match Record */}
          <StatCard>
            <StatHeader>
              <StatIcon>
                <MaterialIcons name="sports-soccer" size={24} color="#4CAF50" />
              </StatIcon>
              <StatTitle>Match Record</StatTitle>
            </StatHeader>
            <StatRow>
              <StatLabel>Games Played</StatLabel>
              <HighlightValue>{totalGames}</HighlightValue>
            </StatRow>
            <StatRow>
              <StatLabel>Wins</StatLabel>
              <HighlightValue>{manager.wins}</HighlightValue>
            </StatRow>
            <StatRow>
              <StatLabel>Draws</StatLabel>
              <StatValue>{manager.draws}</StatValue>
            </StatRow>
            <StatRow>
              <StatLabel>Defeats</StatLabel>
              <StatValue>{manager.defeats}</StatValue>
            </StatRow>
            <StatRow>
              <StatLabel>Win Rate</StatLabel>
              <HighlightValue>{winPercentage}%</HighlightValue>
            </StatRow>
          </StatCard>

          {/* Goal Statistics */}
          <StatCard>
            <StatHeader>
              <StatIcon>
                <MaterialIcons name="sports-soccer" size={24} color="#FF9800" />
              </StatIcon>
              <StatTitle>Goal Statistics</StatTitle>
            </StatHeader>
            <StatRow>
              <StatLabel>Goals Scored</StatLabel>
              <HighlightValue>{manager.goalsScored}</HighlightValue>
            </StatRow>
            <StatRow>
              <StatLabel>Goals Conceded</StatLabel>
              <StatValue>{manager.goalsConceded}</StatValue>
            </StatRow>
            <StatRow>
              <StatLabel>Goal Difference</StatLabel>
              <HighlightValue style={{ color: goalDifference >= 0 ? '#4CAF50' : '#f44336' }}>
                {goalDifference >= 0 ? '+' : ''}{goalDifference}
              </HighlightValue>
            </StatRow>
            {totalGames > 0 && (
              <>
                <StatRow>
                  <StatLabel>Goals per Game</StatLabel>
                  <StatValue>{(manager.goalsScored / totalGames).toFixed(2)}</StatValue>
                </StatRow>
                <StatRow>
                  <StatLabel>Conceded per Game</StatLabel>
                  <StatValue>{(manager.goalsConceded / totalGames).toFixed(2)}</StatValue>
                </StatRow>
              </>
            )}
          </StatCard>

          {/* Transfer Records */}
          <StatCard>
            <StatHeader>
              <StatIcon>
                <MaterialIcons name="swap-horiz" size={24} color="#9C27B0" />
              </StatIcon>
              <StatTitle>Transfer Records</StatTitle>
            </StatHeader>
            <StatRow>
              <StatLabel>Highest Transfer Paid</StatLabel>
              <HighlightValue>{formatCurrencyLong(manager.highestTransferPaid)}</HighlightValue>
            </StatRow>
            <StatRow>
              <StatLabel>Highest Transfer Received</StatLabel>
              <HighlightValue>{formatCurrencyLong(manager.highestTransferReceived)}</HighlightValue>
            </StatRow>
            {manager.highestTransferReceived > manager.highestTransferPaid && (
              <StatLabel style={{ textAlign: 'center', marginTop: 8, fontStyle: 'italic' }}>
                💰 Great business! You're making profit on transfers! 💰
              </StatLabel>
            )}
          </StatCard>

          {/* Achievement Messages */}
          {manager.wins > 0 && (
            <StatCard>
              <StatHeader>
                <StatIcon>
                  <MaterialIcons name="celebration" size={24} color="#4CAF50" />
                </StatIcon>
                <StatTitle>Achievements</StatTitle>
              </StatHeader>
              {manager.wins >= 10 && (
                <StatLabel>🎯 Double Digit Wins - You're on fire!</StatLabel>
              )}
              {manager.wins >= 50 && (
                <StatLabel>🔥 Half Century of Wins - Legendary!</StatLabel>
              )}
              {manager.wins >= 100 && (
                <StatLabel>👑 Century Maker - True Champion!</StatLabel>
              )}
              {parseFloat(winPercentage) >= 75 && totalGames >= 10 && (
                <StatLabel>⭐ Elite Manager - 75%+ Win Rate!</StatLabel>
              )}
              {goalDifference >= 50 && (
                <StatLabel>⚽ Goal Machine - Outstanding Attack!</StatLabel>
              )}
              {manager.trophies >= 5 && (
                <StatLabel>🏆 Trophy Collector - Hall of Fame!</StatLabel>
              )}
            </StatCard>
          )}
        </StatsGrid>
      </ScrollView>
    </Container>
  );
};

export default TrophyRoomScreen;
