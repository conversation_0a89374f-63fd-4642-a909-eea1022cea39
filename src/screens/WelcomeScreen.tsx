import AsyncStorage from '@react-native-async-storage/async-storage';
import axios from 'axios';
import { router } from 'expo-router';
import React, { useEffect, useState } from 'react';
import { ActivityIndicator, Image } from 'react-native';
import styled, { DefaultTheme } from 'styled-components/native';
import { callApi } from '../api/client';
import { Text } from '../components/Text';
import { ANONYMOUS_USER_KEY, useAuth } from '../context/AuthContext';
import { HAS_ONBOARDED_KEY, TEAM_ID_KEY } from '../context/ManagerContext';
import { useTheme } from '../theme/ThemeContext';
import { logger } from '../utils/logger';

interface StyledProps {
  theme: DefaultTheme;
}

const Container = styled.View<StyledProps>`
  flex: 1;
  background-color: ${({ theme }) => theme.colors.background};
  padding: 20px;
  justify-content: center;
  align-items: center;
`;

const LogoContainer = styled.View`
  align-items: center;
  margin-bottom: 40px;
`;

const Title = styled(Text)<StyledProps>`
  font-size: 28px;
  font-weight: bold;
  color: ${({ theme }) => theme.colors.text.primary};
  text-align: center;
  margin-bottom: 10px;
  font-family: ${({ theme }) => theme.typography.bold};
`;

const Subtitle = styled(Text)<StyledProps>`
  font-size: 16px;
  color: ${({ theme }) => theme.colors.text.secondary};
  text-align: center;
  margin-bottom: 40px;
  line-height: 24px;
`;

const ButtonContainer = styled.View`
  width: 100%;
  max-width: 300px;
  gap: 16px;
`;

const PrimaryButton = styled.TouchableOpacity<StyledProps>`
  background-color: ${({ theme }) => theme.colors.primary};
  padding: 16px 24px;
  border-radius: 8px;
  align-items: center;
  shadow-color: #000;
  shadow-offset: 0px 2px;
  shadow-opacity: 0.1;
  shadow-radius: 4px;
  elevation: 3;
`;

const SecondaryButton = styled.TouchableOpacity<StyledProps>`
  background-color: transparent;
  border: 2px solid ${({ theme }) => theme.colors.primary};
  padding: 14px 24px;
  border-radius: 8px;
  align-items: center;
`;

const TertiaryButton = styled.TouchableOpacity`
  padding: 12px 24px;
  align-items: center;
`;

const PrimaryButtonText = styled(Text)<StyledProps>`
  color: ${({ theme }) => theme.colors.background};
  font-size: 16px;
  font-weight: bold;
  font-family: ${({ theme }) => theme.typography.bold};
`;

const SecondaryButtonText = styled(Text)<StyledProps>`
  color: ${({ theme }) => theme.colors.primary};
  font-size: 16px;
  font-weight: bold;
  font-family: ${({ theme }) => theme.typography.bold};
`;

const TertiaryButtonText = styled(Text)<StyledProps>`
  color: ${({ theme }) => theme.colors.text.secondary};
  font-size: 14px;
  text-decoration: underline;
`;

const FullTestMessage = styled(Text)<StyledProps>`
  color: ${({ theme }) => theme.colors.text.secondary};
  font-size: 16px;
  text-align: center;
  margin-bottom: 20px;
  line-height: 24px;
  padding: 0 20px;
`;

const LoadingContainer = styled.View<StyledProps>`
  flex: 1;
  justify-content: center;
  align-items: center;
  background-color: ${({ theme }) => theme.colors.background};
`;

const Strong = styled(Text)`
  font-family: ${(props: StyledProps) => props.theme.typography.bold};
`;

const WelcomeScreen = () => {
  const { theme } = useTheme();
  const { signInAnonymously, isLoading: authLoading } = useAuth();
  const [isLoading, setIsLoading] = useState(false);
  const [remaining, setRemaining] = React.useState(0);
  const [loadingCount, setLoadingCount] = React.useState(true);
  const [anonymousUserExists, setAnonymousUserExists] = useState(false);

  useEffect(() => {
    async function getTeamCount() {
      try {
        const res = await axios.get<{ remaining: number }>(
          `${process.env.EXPO_PUBLIC_API_URL}/available-teams`
        );
        setRemaining(res.data.remaining);
        setLoadingCount(false);
      } catch (error) {
        console.log('Error fetching team count:', error);
      }
    }
    getTeamCount();
  }, []);

  useEffect(() => {
    async function checkAnonymousUser() {
      const user = await AsyncStorage.getItem(ANONYMOUS_USER_KEY);
      setAnonymousUserExists(!!user);
    }
    checkAnonymousUser();
  }, []);

  const handlePlayAsGuest = async () => {
    try {
      setIsLoading(true);
      logger.log('User chose to play as guest');
      await signInAnonymously();
      const teamId = await AsyncStorage.getItem(TEAM_ID_KEY);
      const hasOnboarded = await AsyncStorage.getItem(HAS_ONBOARDED_KEY);
      if (teamId) {
        if (hasOnboarded === 'true') {
          router.replace('/');
        } else {
          router.replace('/alpha-notice');
        }
      } else {
        // Create guest user and assign a team
        await callApi('/auth/guest', {
          method: 'POST',
          body: JSON.stringify({}),
        });
        if (hasOnboarded === 'true') {
          router.replace('/');
        } else {
          router.replace('/alpha-notice');
        }
      }
    } catch (error) {
      logger.error('Failed to sign in anonymously:', error);
      setIsLoading(false);
    }
  };

  const handleSignIn = () => {
    logger.log('User chose to sign in');
    router.push('/login');
  };

  const handleSignUp = () => {
    logger.log('User chose to sign up');
    router.push('/signup');
  };

  if (isLoading || authLoading || loadingCount) {
    return (
      <LoadingContainer theme={theme}>
        <ActivityIndicator size="large" color={theme.colors.primary} />
        <Text style={{ marginTop: 16, color: theme.colors.text.secondary }}>
          {isLoading ? 'Setting up your game...' : 'Loading...'}
        </Text>
      </LoadingContainer>
    );
  }

  return (
    <Container theme={theme}>
      <LogoContainer>
        <Image
          source={require('../../assets/logo.png')}
          style={{
            width: 300,
            height: 225,
            resizeMode: 'contain',
            marginBottom: 20,
          }}
        />
        <Title theme={theme}>Welcome to Jumpers for Goalposts</Title>
        <Subtitle>
          There are currently <Strong>{remaining}</Strong> spaces left in the early access
          programme.
        </Subtitle>
      </LogoContainer>

      {remaining === 0 && !anonymousUserExists ? (
        <>
          <FullTestMessage theme={theme}>
            The test is currently full. You can sign in to an existing account, but new account
            creation is currently disabled at this time.
          </FullTestMessage>
          <ButtonContainer>
            <SecondaryButton theme={theme} onPress={handleSignIn}>
              <SecondaryButtonText theme={theme}>Sign In</SecondaryButtonText>
            </SecondaryButton>
          </ButtonContainer>
        </>
      ) : (
        <ButtonContainer>
          {(remaining > 0 || anonymousUserExists) && (
            <PrimaryButton theme={theme} onPress={handlePlayAsGuest}>
              <PrimaryButtonText theme={theme}>Play as Guest</PrimaryButtonText>
            </PrimaryButton>
          )}

          <SecondaryButton theme={theme} onPress={handleSignIn}>
            <SecondaryButtonText theme={theme}>Sign In</SecondaryButtonText>
          </SecondaryButton>

          {remaining > 0 && (
            <TertiaryButton onPress={handleSignUp}>
              <TertiaryButtonText theme={theme}>Create Account</TertiaryButtonText>
            </TertiaryButton>
          )}
        </ButtonContainer>
      )}
    </Container>
  );
};

export default WelcomeScreen;
