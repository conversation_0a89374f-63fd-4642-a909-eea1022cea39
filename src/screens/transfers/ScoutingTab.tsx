import React, { useCallback, useState } from 'react';
import { ActivityIndicator, FlatList, Platform } from 'react-native';
import ActiveTransferCard from '../../components/ActiveTransferCard';
import { ApiErrorModal } from '../../components/ApiErrorModal';
import { CrossPlatformAlert } from '../../components/CrossPlatformAlert';
import PlayerDetailView from '../../components/PlayerDetailView';
import ScoutingPlayerRow from '../../components/PlayerRow/ScoutingPlayerRow';
import { SendScoutModal } from '../../components/Scouting/SendScoutModal';
import {
  Container,
  EmptyListContainer,
  EmptyListText,
  ListContainer,
  ListHeaderText,
  LoadingContainer,
} from '../../components/TransferSharedStyles';
import { useDataCache } from '../../context/DataCacheContext';
import { useManager } from '../../context/ManagerContext';
import { PlayerProvider } from '../../context/PlayerContext';
import { useApiErrorModal } from '../../hooks/useApiErrorModal';
import {
  useCachedMyActiveTransfers,
  useCachedScoutedPlayers,
  useCachedTeam,
} from '../../hooks/useCachedData';
import { ActiveTransfer } from '../../hooks/useMyBidsPlayers';
import { ScoutedPlayer, useLeagues } from '../../hooks/useQueries';
import { LoadMoreButton } from './components/LoadMoreButton';
import { NegotiateModal } from './components/NegotiateModal';
import { ScoutingTabHeader } from './components/ScoutingTabHeader';
import { TransferDetailsModal } from './components/TransferDetailsModal';
import { useScoutingTabLogic } from './hooks/useScoutingTabLogic';
import { useTeamAverages } from './hooks/useTeamAverages';
import { useTransferActions } from './hooks/useTransferActions';

// Helper types for FlatList data
interface ListHeaderItem {
  isHeader: true;
  title: string;
  type: 'myActiveTransfers' | 'scoutedPlayers';
}
interface ListEmptyItem {
  isEmpty: true;
  type: 'myActiveTransfers' | 'scoutedPlayers';
}
type ListItem = ScoutedPlayer | ActiveTransfer | ListHeaderItem | ListEmptyItem;

const ScoutingTab = () => {
  const { manager } = useManager();
  const { findPlayer } = useDataCache();
  const [isLoadingMore, setIsLoadingMore] = useState(false);
  const [nextToken, setNextToken] = useState<string | undefined>(undefined);
  const [sendScoutModalState, setSendScoutModalState] = useState<'league' | 'team' | null>(null);
  const { team } = useCachedTeam(manager?.gameworldId, manager?.team?.teamId);

  // Local state for active transfers to enable immediate updates
  const [localActiveTransfers, setLocalActiveTransfers] = useState<ActiveTransfer[]>([]);

  // Reference to the dropdown container for click-outside handling
  const dropdownRef = React.useRef<any>(null);

  // Data fetching hooks
  const {
    data: leagues,
    isLoading: isLoadingLeagues,
    error: leaguesError,
  } = useLeagues(manager?.gameworldId);
  const {
    data: scoutedPlayersData,
    isLoading: isLoadingScoutedPlayers,
    error: scoutedPlayersError,
  } = useCachedScoutedPlayers(manager?.gameworldId, manager?.team?.teamId, nextToken);
  const {
    data: myActiveTransfersData,
    isLoading: isLoadingMyActiveTransfers,
    error: myActiveTransfersError,
  } = useCachedMyActiveTransfers(manager?.gameworldId);

  // Custom hooks for business logic
  const scoutingLogic = useScoutingTabLogic({
    scoutedPlayersData,
    isLoadingMore,
    nextToken,
  });

  const teamAverages = useTeamAverages(team?.players);

  const transferActions = useTransferActions();

  const { visible, error, dismiss } = useApiErrorModal(
    scoutedPlayersError || myActiveTransfersError || leaguesError
  );

  // Loading state management
  const isLoading = isLoadingScoutedPlayers || isLoadingMyActiveTransfers || isLoadingLeagues;

  const loadMorePlayers = () => {
    // Don't try to load more if we've reached the end
    if (scoutingLogic.reachedEnd) {
      return;
    }

    if (scoutedPlayersData?.pagination?.hasMore && scoutedPlayersData?.pagination?.nextToken) {
      setIsLoadingMore(true);
      setNextToken(scoutedPlayersData.pagination.nextToken);
    }
  };

  // Web-specific dropdown handling
  React.useEffect(() => {
    if (Platform.OS === 'web' && typeof document !== 'undefined') {
      const handleClickOutside = (event: any) => {
        if (
          dropdownRef.current &&
          // @ts-ignore - contains is available in web but not in RN types
          !dropdownRef.current.contains(event.target) &&
          scoutingLogic.showPositionDropdown
        ) {
          scoutingLogic.togglePositionDropdown();
        }
      };

      if (scoutingLogic.showPositionDropdown) {
        document.addEventListener('mousedown', handleClickOutside);
      }

      return () => {
        document.removeEventListener('mousedown', handleClickOutside);
      };
    }
  }, [scoutingLogic]);

  // Reset loading more state when loading finishes
  React.useEffect(() => {
    if (isLoadingMore && !isLoadingScoutedPlayers) {
      setIsLoadingMore(false);
    }
  }, [isLoadingMore, isLoadingScoutedPlayers]);

  // Sync API active transfers data with local state
  React.useEffect(() => {
    if (myActiveTransfersData?.transfers) {
      setLocalActiveTransfers(myActiveTransfersData.transfers);
    }
  }, [myActiveTransfersData]);

  // Function to add a new transfer to local state immediately
  const addNewTransferToLocal = useCallback((newTransfer: ActiveTransfer) => {
    setLocalActiveTransfers((prev) => {
      // Check if transfer already exists to avoid duplicates
      const exists = prev.some((transfer) => transfer.id === newTransfer.id);
      if (exists) {
        return prev;
      }
      // Add new transfer at the beginning of the list
      return [newTransfer, ...prev];
    });
  }, []);

  const renderPlayer = ({ item }: { item: ScoutedPlayer; index: number }) => {
    // Get the current player data from cache to ensure we show updated information
    const currentPlayer = findPlayer(item.playerId) as ScoutedPlayer | null;
    const playerToRender = currentPlayer || item; // Fallback to original item if not found in cache

    return (
      <ScoutingPlayerRow
        player={playerToRender}
        onSelect={scoutingLogic.handlePlayerSelect}
        isSelected={scoutingLogic.selectedPlayerId === item.playerId}
        positionFilter={scoutingLogic.positionFilter}
        teamAverages={teamAverages[scoutingLogic.positionFilter]}
      />
    );
  };

  const renderEmptyList = () => (
    <EmptyListContainer>
      <EmptyListText>
        No scouted players found. Use the buttons above to scout players.
      </EmptyListText>
    </EmptyListContainer>
  );

  const renderEmptyActiveTransfersList = () => (
    <EmptyListContainer>
      <EmptyListText>You have no active transfer offers.</EmptyListText>
    </EmptyListContainer>
  );

  const renderActiveTransfer = ({ item }: { item: ActiveTransfer }) => (
    <ActiveTransferCard transfer={item} onSelect={transferActions.handleTransferSelect} />
  );

  if (isLoading && !isLoadingMore) {
    return (
      <LoadingContainer>
        <ActivityIndicator size="large" />
      </LoadingContainer>
    );
  }

  return (
    <Container>
      <ScoutingTabHeader
        manager={manager}
        positionFilter={scoutingLogic.positionFilter}
        showPositionDropdown={scoutingLogic.showPositionDropdown}
        onScoutLeague={() => setSendScoutModalState('league')}
        onScoutTeam={() => setSendScoutModalState('team')}
        onTogglePositionDropdown={scoutingLogic.togglePositionDropdown}
        onPositionSelect={scoutingLogic.handlePositionSelect}
        dropdownRef={dropdownRef}
      />

      <ListContainer>
        <FlatList<ListItem>
          data={(() => {
            const combinedData: ListItem[] = [];
            if (localActiveTransfers && localActiveTransfers.length > 0) {
              combinedData.push({
                isHeader: true,
                title: 'Your Active Transfer Offers',
                type: 'myActiveTransfers',
              });
              combinedData.push(...localActiveTransfers);
            }
            combinedData.push({ isHeader: true, title: 'Scouted Players', type: 'scoutedPlayers' });
            if (scoutingLogic.scoutedPlayers.length > 0) {
              combinedData.push(...scoutingLogic.scoutedPlayers);
            } else {
              combinedData.push({ isEmpty: true, type: 'scoutedPlayers' });
            }
            return combinedData;
          })()}
          renderItem={({ item }) => {
            if ('isHeader' in item && item.isHeader) {
              return (
                <ListHeaderText style={{ marginTop: 16, marginBottom: 8 }}>
                  {item.title}
                </ListHeaderText>
              );
            }
            if ('isEmpty' in item && item.isEmpty) {
              return item.type === 'myActiveTransfers'
                ? renderEmptyActiveTransfersList()
                : renderEmptyList();
            }
            if ('id' in item && 'player' in item && 'seller' in item) {
              return renderActiveTransfer({ item: item as ActiveTransfer });
            }
            return renderPlayer({ item: item as ScoutedPlayer, index: 0 });
          }}
          keyExtractor={(item, index) => {
            if ('isHeader' in item && item.isHeader) return `${item.type}-header-${index}`;
            if ('isEmpty' in item && item.isEmpty) return `${item.type}-empty-${index}`;
            if ('id' in item && 'player' in item && 'seller' in item) {
              return `transfer-${(item as ActiveTransfer).id}`;
            }
            return (item as ScoutedPlayer).playerId;
          }}
          contentContainerStyle={{ flexGrow: 1, paddingBottom: 16 }}
          ListFooterComponent={() =>
            scoutedPlayersData?.pagination?.hasMore && !scoutingLogic.reachedEnd ? (
              <LoadMoreButton onPress={loadMorePlayers} isLoading={isLoadingMore} />
            ) : null
          }
        />
      </ListContainer>

      {/* Player detail view */}
      {scoutingLogic.selectedPlayer && (
        <PlayerProvider>
          <PlayerDetailView
            player={scoutingLogic.selectedPlayer}
            onClose={scoutingLogic.clearSelectedPlayer}
            onNewTransfer={addNewTransferToLocal}
            onNegotiate={transferActions.handleNegotiate}
          />
        </PlayerProvider>
      )}

      {/* Transfer Details Modal */}
      <TransferDetailsModal
        visible={!!transferActions.selectedTransfer}
        transfer={transferActions.selectedTransfer}
        onClose={transferActions.clearSelectedTransfer}
        onAcceptCounterOffer={transferActions.handleAcceptCounterOffer}
        onNegotiate={transferActions.handleNegotiate}
        onCancelTransfer={transferActions.handleCancelTransfer}
      />

      {/* Negotiate Modal */}
      <NegotiateModal
        visible={transferActions.isNegotiateModalVisible}
        transfer={transferActions.negotiatingTransfer}
        negotiateAmount={transferActions.negotiateAmount}
        isSubmitting={transferActions.isSubmitting}
        onClose={transferActions.closeNegotiateModal}
        onAmountChange={transferActions.setNegotiateAmount}
        onSubmit={transferActions.handleSubmitNegotiation}
      />

      <SendScoutModal
        state={sendScoutModalState}
        leagues={leagues}
        onClose={() => setSendScoutModalState(null)}
      />

      <CrossPlatformAlert
        visible={transferActions.showConfirmation}
        title="Success"
        message="Transfer offer submitted successfully"
        buttons={[
          {
            text: 'OK',
            onPress: () => transferActions.setShowConfirmation(false),
          },
        ]}
        onDismiss={() => transferActions.setShowConfirmation(false)}
      />

      <CrossPlatformAlert
        visible={transferActions.showError}
        title="Error"
        message="There was an error processing your request"
        buttons={[
          {
            text: 'OK',
            onPress: () => transferActions.setShowError(false),
          },
        ]}
        onDismiss={() => transferActions.setShowError(false)}
      />

      <ApiErrorModal visible={visible} error={error} onDismiss={dismiss} />
    </Container>
  );
};

export default ScoutingTab;
