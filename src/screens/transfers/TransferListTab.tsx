import React, { useState } from 'react';
import { ActivityIndicator, FlatList } from 'react-native';
import styled from 'styled-components/native';
import { BalanceText } from '../../components/Balance';
import PlayerDetailView from '../../components/PlayerDetailView';
import TransferListPlayerRow from '../../components/PlayerRow/TransferListPlayerRow';
import { Text } from '../../components/Text';
import {
  ButtonText,
  Container,
  EmptyListContainer,
  FilterContainer,
  ListContainer,
  ListHeaderText,
  LoadingContainer,
  LoadMoreButton,
} from '../../components/TransferSharedStyles';
import { useDataCache } from '../../context/DataCacheContext';
import { useManager } from '../../context/ManagerContext';
import { PlayerProvider } from '../../context/PlayerContext';
import {
  useCachedMyBidsPlayers,
  useCachedTeam,
  useCachedTransferListPlayers,
} from '../../hooks/useCachedData';
import { TransferListFilters } from '../../hooks/useTransferListPlayers';
import { CorePlayer, TransferListPlayer } from '../../models/player';
import { Theme } from '../../theme/theme';
import { formatCurrencyLong } from '../../utils/utils';
import { SharedPositionFilter } from './components/SharedPositionFilter';
import { TokenCountContainer, TokenTypeContainer } from './components/TokenCountContainer';
import { TransferListFilterModal } from './components/TransferListFilterModal';
import { useTeamAverages } from './hooks/useTeamAverages';
import { useTransferListLogic } from './hooks/useTransferListLogic';
interface StyledProps {
  theme: Theme;
}

const FilterButton = styled.TouchableOpacity<StyledProps>`
  align-items: center;
  justify-content: center;
  width: 44px;
  height: 44px;
  background-color: ${(props) => props.theme.colors.surface};
  border-radius: 8px;
  border-width: 1px;
  border-color: ${(props) => props.theme.colors.border};
`;

const FilterIcon = styled(Text)<StyledProps>`
  font-size: 18px;
  color: ${(props) => props.theme.colors.text.secondary};
`;

styled(EmptyListContainer)`
  padding: 20px;
`;

type ListItem = {
  type: 'header' | 'empty' | 'myBids' | 'transferList';
  data: TransferListPlayer | string;
};

const TransferListTab = () => {
  const { manager } = useManager();
  const { findPlayer } = useDataCache();
  const [isLoadingMore, setIsLoadingMore] = useState(false);
  const [lastEvaluatedKey, setLastEvaluatedKey] = useState<string | undefined>(undefined);
  const [filters, setFilters] = useState<Partial<TransferListFilters>>({});
  const [showFilterModal, setShowFilterModal] = useState(false);

  // Build the complete filters object including pagination
  const completeFilters: TransferListFilters = {
    ...filters,
    lastEvaluatedKey,
  };

  // Data fetching hooks
  const { data: transferListData, isLoading: isLoadingTransferList } = useCachedTransferListPlayers(
    manager?.gameworldId,
    completeFilters
  );

  const { data: myBidsData, isLoading: isLoadingMyBids } = useCachedMyBidsPlayers(
    manager?.gameworldId
  );

  const { team: teamData, isLoading: isLoadingTeam } = useCachedTeam(
    manager?.gameworldId,
    manager?.team?.teamId
  );

  // Custom hooks for business logic
  const transferListLogic = useTransferListLogic({
    transferListData,
    myBidsData,
    isLoadingMore,
    lastEvaluatedKey,
  });

  const teamAverages = useTeamAverages(teamData?.players);

  // Loading state management
  const isLoading = isLoadingTransferList || isLoadingMyBids || isLoadingTeam;

  const loadMorePlayers = () => {
    // Don't try to load more if we've reached the end
    if (transferListLogic.reachedEnd) {
      return;
    }

    const paginationKey = transferListLogic.getPaginationKey(transferListData);

    if (paginationKey && paginationKey !== lastEvaluatedKey) {
      setIsLoadingMore(true);
      setLastEvaluatedKey(paginationKey);
    }
  };

  const handleApplyFilters = (newFilters: Partial<TransferListFilters>) => {
    setFilters(newFilters);
    // Reset pagination when filters change
    setLastEvaluatedKey(undefined);
  };

  // Function to check if the user's team is the highest bidder
  const isUserHighestBidder = (player: TransferListPlayer): boolean => {
    if (!manager?.team || !player.bidHistory || player.bidHistory.length === 0) {
      return false;
    }

    // Sort bid history by maximum bid (highest first)
    const sortedBids = [...player.bidHistory].sort((a, b) => b.maximumBid - a.maximumBid);

    // Check if the user's team is the highest bidder
    return sortedBids[0]?.teamId === manager.team.teamId;
  };

  const renderPlayer = ({ item }: { item: TransferListPlayer; index: number }) => {
    // Get the current player data from cache to ensure we show updated information
    const currentPlayer = findPlayer(item.playerId) as TransferListPlayer | null;
    const playerToRender = currentPlayer || item; // Fallback to original item if not found in cache

    return (
      <TransferListPlayerRow
        player={playerToRender}
        onSelect={transferListLogic.handlePlayerSelect}
        isSelected={transferListLogic.selectedPlayerId === item.playerId}
        positionFilter={transferListLogic.positionFilter}
        teamAverages={teamAverages[transferListLogic.positionFilter]}
        isHighestBidder={isUserHighestBidder(playerToRender)}
      />
    );
  };

  // Reset loading more state when loading finishes
  React.useEffect(() => {
    if (isLoadingMore && !isLoadingTransferList) {
      setIsLoadingMore(false);
    }
  }, [isLoadingMore, isLoadingTransferList]);

  if (isLoading && !isLoadingMore) {
    return (
      <LoadingContainer>
        <ActivityIndicator size="large" />
      </LoadingContainer>
    );
  }

  // Create a combined data structure with sections
  const combinedData: ListItem[] = [];

  // Add my bids section if there are any bids
  if (transferListLogic.myBidsPlayers.length > 0) {
    combinedData.push({
      data: "Players You've Bid On",
      type: 'header',
    });
    combinedData.push(
      ...transferListLogic.myBidsPlayers.map((player) => ({
        data: player,
        type: 'myBids' as const,
      }))
    );
  }

  // Add transfer list section
  combinedData.push({
    data: 'Players Available for Transfer',
    type: 'header',
  });
  combinedData.push(
    ...transferListLogic.transferListPlayers.map((player) => ({
      data: player,
      type: 'transferList' as const,
    }))
  );

  return (
    <Container>
      <TokenCountContainer>
        <TokenTypeContainer>
          <BalanceText>{formatCurrencyLong(manager?.team?.balance ?? 0)}</BalanceText>
        </TokenTypeContainer>
      </TokenCountContainer>

      <FilterContainer>
        <SharedPositionFilter
          positionFilter={transferListLogic.positionFilter}
          onPositionSelect={transferListLogic.handlePositionSelect}
        />
        <FilterButton onPress={() => setShowFilterModal(true)}>
          <FilterIcon>⚙</FilterIcon>
        </FilterButton>
      </FilterContainer>

      {/* Combined List with Sections */}
      <ListContainer>
        <FlatList<ListItem>
          data={combinedData}
          renderItem={({ item }) => {
            if (item.type === 'header') {
              return <ListHeaderText>{item.data as string}</ListHeaderText>;
            }

            return renderPlayer({ item: item.data as TransferListPlayer, index: 0 });
          }}
          keyExtractor={(item, index) => {
            if (item.type === 'header') return `${item.type}-${index}`;
            return `${(item.data as TransferListPlayer).playerId}-${item.type}`;
          }}
          contentContainerStyle={{ flexGrow: 1, paddingBottom: 16 }}
          ListFooterComponent={() =>
            transferListLogic.getPaginationKey(transferListData) ? (
              <LoadMoreButton onPress={loadMorePlayers} disabled={isLoadingMore}>
                {isLoadingMore ? (
                  <>
                    <ActivityIndicator size="small" color="white" style={{ marginRight: 8 }} />
                    <ButtonText>Loading...</ButtonText>
                  </>
                ) : (
                  <ButtonText>Load More</ButtonText>
                )}
              </LoadMoreButton>
            ) : null
          }
        />
      </ListContainer>

      {/* Filter Modal */}
      <TransferListFilterModal
        visible={showFilterModal}
        onClose={() => setShowFilterModal(false)}
        filters={filters}
        onApplyFilters={handleApplyFilters}
      />

      {/* Player detail view */}
      {transferListLogic.selectedPlayer && (
        <PlayerProvider>
          <PlayerDetailView
            player={transferListLogic.selectedPlayer as unknown as CorePlayer}
            onClose={transferListLogic.clearSelectedPlayer}
            onNewTransfer={() => {}}
            onNegotiate={() => {}}
          />
        </PlayerProvider>
      )}
    </Container>
  );
};

export default TransferListTab;
