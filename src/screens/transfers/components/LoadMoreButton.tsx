import React from 'react';
import { ActivityIndicator } from 'react-native';
import styled, { DefaultTheme } from 'styled-components/native';
import { ButtonText } from '../../../components/TransferSharedStyles';

interface StyledProps {
  theme: DefaultTheme;
}

interface LoadMoreButtonProps extends StyledProps {
  disabled?: boolean;
}

const LoadMoreButtonStyled = styled.TouchableOpacity<LoadMoreButtonProps>`
  background-color: ${(props) =>
    props.disabled ? props.theme.colors.surface + '80' : props.theme.colors.surface};
  padding: 12px;
  border-radius: 8px;
  align-items: center;
  justify-content: center;
  flex-direction: row;
  margin: 16px 0;
  align-self: center;
  min-width: 150px;
`;

interface LoadMoreButtonComponentProps {
  onPress: () => void;
  isLoading: boolean;
  disabled?: boolean;
}

export const LoadMoreButton: React.FC<LoadMoreButtonComponentProps> = ({
  onPress,
  isLoading,
  disabled = false,
}) => {
  return (
    <LoadMoreButtonStyled onPress={onPress} disabled={disabled || isLoading}>
      {isLoading ? (
        <>
          <ActivityIndicator size="small" color="white" style={{ marginRight: 8 }} />
          <ButtonText>Loading...</ButtonText>
        </>
      ) : (
        <ButtonText>Load More</ButtonText>
      )}
    </LoadMoreButtonStyled>
  );
};
