import React from 'react';
import { Modal, TextInput } from 'react-native';
import styled from 'styled-components/native';
import { Text } from '../../../components/Text';
import { ActiveTransfer } from '../../../hooks/useMyBidsPlayers';

interface StyledProps {
  theme: any;
}

const ModalContainer = styled.View`
  flex: 1;
  justify-content: center;
  align-items: center;
  background-color: rgba(0, 0, 0, 0.5);
  padding: 20px;
`;

const ModalContent = styled.View`
  background-color: ${(props: StyledProps) => props.theme.colors.background};
  border-radius: 12px;
  padding: 20px;
  width: 90%;
  max-width: 400px;
`;

const ModalTitle = styled(Text)`
  font-family: ${(props: StyledProps) => props.theme.typography.bold};
  font-size: 20px;
  margin-bottom: 16px;
  text-align: center;
`;

const InputContainer = styled.View`
  margin-bottom: 20px;
`;

const Label = styled(Text)`
  font-size: 14px;
  margin-bottom: 8px;
  color: ${(props: StyledProps) => props.theme.colors.text.primary};
`;

const Input = styled(TextInput)`
  border: 1px solid ${(props: StyledProps) => props.theme.colors.border || '#ccc'};
  border-radius: 8px;
  padding: 12px;
  font-size: 16px;
  background-color: ${(props: StyledProps) => props.theme.colors.surface};
  color: ${(props: StyledProps) => props.theme.colors.text.primary};
`;

const ModalButtonContainer = styled.View`
  flex-direction: row;
  justify-content: space-between;
  gap: 12px;
`;

const ActionButton = styled.TouchableOpacity<{
  variant?: 'primary' | 'secondary';
  disabled?: boolean;
}>`
  flex: 1;
  padding: 12px;
  border-radius: 8px;
  align-items: center;
  justify-content: center;
  background-color: ${(props) => {
    if (props.disabled) return '#888';
    switch (props.variant) {
      case 'secondary':
        return '#888';
      default:
        return props.theme.colors.primary;
    }
  }};
`;

const ActionButtonText = styled(Text)`
  color: white;
  font-family: ${(props: StyledProps) => props.theme.typography.bold};
  font-size: 14px;
  text-align: center;
`;

interface NegotiateModalProps {
  visible: boolean;
  transfer: ActiveTransfer | null;
  negotiateAmount: string;
  isSubmitting: boolean;
  onClose: () => void;
  onAmountChange: (amount: string) => void;
  onSubmit: () => void;
}

export const NegotiateModal: React.FC<NegotiateModalProps> = ({
  visible,
  transfer,
  negotiateAmount,
  isSubmitting,
  onClose,
  onAmountChange,
  onSubmit,
}) => {
  return (
    <Modal visible={visible} transparent animationType="fade" onRequestClose={onClose}>
      <ModalContainer>
        <ModalContent>
          <ModalTitle>Update Transfer Offer</ModalTitle>

          <InputContainer>
            <Label>
              Player: {transfer?.player.firstName} {transfer?.player.surname}
            </Label>
            <Label>Player Value: £{transfer?.player.value.toLocaleString()}</Label>
            <Label>Your Last Offer: £{transfer?.value.toLocaleString()}</Label>
            {transfer?.counterOfferValue !== '0' && (
              <Label>
                Their Counter Offer: £{Number(transfer?.counterOfferValue).toLocaleString()}
              </Label>
            )}
            <Label>New Offer Amount:</Label>

            <Input
              value={negotiateAmount}
              onChangeText={onAmountChange}
              placeholder="Enter amount"
              keyboardType="numeric"
            />
          </InputContainer>

          <ModalButtonContainer>
            <ActionButton variant="secondary" onPress={onClose}>
              <ActionButtonText>Cancel</ActionButtonText>
            </ActionButton>
            <ActionButton variant="primary" onPress={onSubmit} disabled={isSubmitting}>
              <ActionButtonText>
                {isSubmitting ? 'Submitting...' : 'Submit Offer!'}
              </ActionButtonText>
            </ActionButton>
          </ModalButtonContainer>
        </ModalContent>
      </ModalContainer>
    </Modal>
  );
};
