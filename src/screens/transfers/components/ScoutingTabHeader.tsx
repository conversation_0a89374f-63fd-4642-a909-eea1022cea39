import React from 'react';
import { ActionButton, ActionButtonText } from '../../../components/ActionButton';
import { BalanceText, ScoutIcon } from '../../../components/Balance';
import { ButtonContainer, FilterContainer } from '../../../components/TransferSharedStyles';
import { Manager } from '../../../models/manager';
import { formatCurrencyLong } from '../../../utils/utils';
import { PositionFilter } from '../hooks/useScoutingTabLogic';
import { SharedPositionFilter } from './SharedPositionFilter';
import { TokenCountContainer, TokenIcon, TokenTypeContainer } from './TokenCountContainer';

interface ScoutingTabHeaderProps {
  manager?: Manager | null;
  positionFilter: PositionFilter;
  showPositionDropdown: boolean;
  onScoutLeague: () => void;
  onScoutTeam: () => void;
  onTogglePositionDropdown: () => void;
  onPositionSelect: (position: PositionFilter) => void;
  dropdownRef: React.RefObject<any>;
}

export const ScoutingTabHeader: React.FC<ScoutingTabHeaderProps> = ({
  manager,
  positionFilter,
  onScoutLeague,
  onScoutTeam,
  onPositionSelect,
}) => {
  return (
    <>
      <TokenCountContainer>
        <TokenTypeContainer>
          <ScoutIcon source={require('../../../../assets/scoutToken.png')} />
          <BalanceText>{manager?.scoutTokens ?? 0}</BalanceText>
        </TokenTypeContainer>
        <TokenTypeContainer>
          <TokenIcon source={require('../../../../assets/superScoutToken.png')} />
          <BalanceText>{manager?.superScoutTokens ?? 0}</BalanceText>
        </TokenTypeContainer>
        <TokenTypeContainer>
          <BalanceText>{formatCurrencyLong(manager?.team?.balance ?? 0)}</BalanceText>
        </TokenTypeContainer>
      </TokenCountContainer>

      <ButtonContainer>
        <ActionButton variant="secondary" onPress={onScoutLeague}>
          <ActionButtonText>Scout League</ActionButtonText>
        </ActionButton>
        <ActionButton variant="secondary" onPress={onScoutTeam}>
          <ActionButtonText>Scout Team</ActionButtonText>
        </ActionButton>
      </ButtonContainer>

      <FilterContainer>
        <SharedPositionFilter positionFilter={positionFilter} onPositionSelect={onPositionSelect} />
      </FilterContainer>
    </>
  );
};
