import React from 'react';
import PositionFilter, {
  PositionFilter as PositionFilterType,
} from '../../../components/PositionFilter/PositionFilter';

interface SharedPositionFilterProps {
  positionFilter: PositionFilterType;
  onPositionSelect: (position: PositionFilterType) => void;
}

export const SharedPositionFilter: React.FC<SharedPositionFilterProps> = ({
  positionFilter,
  onPositionSelect,
}) => {
  return (
    <PositionFilter positionFilter={positionFilter} onPositionSelect={onPositionSelect} />
  );
};
