import { Image } from 'react-native';
import styled from 'styled-components/native';
import { StyledProps } from '../../../components/Common';
import { Text } from '../../../components/Text';

export const TokenCountContainer = styled.View`
  flex-direction: row;
  align-items: center;
  justify-content: flex-end;
  margin-bottom: 12px;
  gap: 16px;
`;
export const TokenTypeContainer = styled.View`
  flex-direction: row;
  align-items: center;
  margin-right: 8px;
`;
export const TokenIcon = styled(Image)`
  width: 24px;
  height: 24px;
  margin-right: 4px;
`;
export const TokenCountText = styled(Text)<StyledProps>`
  color: ${(props) => props.theme.colors.text.primary};
  font-family: ${(props: StyledProps) => props.theme.typography.bold};
  font-size: 16px;
`;
