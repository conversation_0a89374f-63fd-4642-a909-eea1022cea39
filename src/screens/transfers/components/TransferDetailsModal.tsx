import React from 'react';
import { Modal } from 'react-native';
import styled from 'styled-components/native';
import { ActionButton, ActionButtonText } from '../../../components/ActionButton';
import { StyledProps } from '../../../components/Common';
import {
  <PERSON>dalContainer,
  ModalContent,
} from '../../../components/PlayerDetail/ModalSharedComponents';
import { Text } from '../../../components/Text';
import { ActiveTransfer } from '../../../hooks/useMyBidsPlayers';
import { useTheme } from '../../../theme/ThemeContext';
import { formatCurrencyLong } from '../../../utils/utils';

const ModalTitle = styled(Text)`
  font-family: ${(props: StyledProps) => props.theme.typography.bold};
  font-size: 20px;
  margin-bottom: 16px;
  text-align: center;
`;

const InputContainer = styled.View`
  margin-bottom: 20px;
`;

const Label = styled(Text)`
  font-size: 14px;
  margin-bottom: 8px;
  color: ${(props: StyledProps) => props.theme.colors.text.primary};
`;

const ModalButtonContainer = styled.View`
  flex-direction: row;
  justify-content: space-between;
  gap: 12px;
`;

const CloseButton = styled.TouchableOpacity`
  position: absolute;
  top: 16px;
  right: 16px;
  padding: 8px;
`;

interface TransferDetailsModalProps {
  visible: boolean;
  transfer: ActiveTransfer | null;
  onClose: () => void;
  onAcceptCounterOffer: (transfer: ActiveTransfer) => void;
  onNegotiate: (transfer: ActiveTransfer) => void;
  onCancelTransfer: (transfer: ActiveTransfer) => void;
}

export const TransferDetailsModal: React.FC<TransferDetailsModalProps> = ({
  visible,
  transfer,
  onClose,
  onAcceptCounterOffer,
  onNegotiate,
  onCancelTransfer,
}) => {
  const hasCounterOffer = transfer
    ? transfer.counterOfferValue !== '0' &&
      transfer.counterOfferTime !== '0' &&
      Number(transfer.counterOfferTime) > transfer.date
    : false;
  const { theme } = useTheme();

  const getActionButtons = () => {
    if (hasCounterOffer) {
      return (
        <ModalButtonContainer>
          <ActionButton
            variant="primary"
            onPress={() => {
              onAcceptCounterOffer(transfer!);
              onClose();
            }}
          >
            <ActionButtonText>Accept</ActionButtonText>
          </ActionButton>
          <ActionButton
            variant="secondary"
            onPress={() => {
              onNegotiate(transfer!);
              onClose();
            }}
          >
            <ActionButtonText>Negotiate</ActionButtonText>
          </ActionButton>
          <ActionButton
            variant="danger"
            onPress={() => {
              onCancelTransfer(transfer!);
              onClose();
            }}
          >
            <ActionButtonText>You're having a laugh</ActionButtonText>
          </ActionButton>
        </ModalButtonContainer>
      );
    } else {
      return (
        <ModalButtonContainer>
          <ActionButton
            variant="primary"
            onPress={() => {
              onNegotiate(transfer!);
              onClose();
            }}
          >
            <ActionButtonText>Amend Offer</ActionButtonText>
          </ActionButton>
          <ActionButton
            variant="danger"
            onPress={() => {
              onCancelTransfer(transfer!);
              onClose();
            }}
          >
            <ActionButtonText>Withdraw Offer</ActionButtonText>
          </ActionButton>
        </ModalButtonContainer>
      );
    }
  };

  return (
    <Modal visible={visible} transparent animationType={'fade'} onRequestClose={onClose}>
      <ModalContainer>
        <ModalContent>
          <ModalTitle>Transfer Details</ModalTitle>
          <CloseButton onPress={onClose}>
            <Text style={{ fontSize: 18 }}>×</Text>
          </CloseButton>
          <InputContainer>
            <Label>
              Player: {transfer?.player.firstName} {transfer?.player.surname}
            </Label>
            <Label>Your Offer: {formatCurrencyLong(transfer?.value ?? 0)}</Label>
            <Label>To: {transfer?.seller.teamName}</Label>
            {hasCounterOffer && (
              <Label style={{ color: '#e3172a', fontFamily: theme.typography.bold }}>
                Counter Offer: {formatCurrencyLong(Number(transfer?.counterOfferValue))}
              </Label>
            )}
          </InputContainer>

          {getActionButtons()}
        </ModalContent>
      </ModalContainer>
    </Modal>
  );
};
