import React, { useEffect, useState } from 'react';
import { Modal, Platform, TextInput, TouchableOpacity } from 'react-native';
import styled from 'styled-components/native';
import { Text } from '../../../components/Text';
import { TransferListFilters } from '../../../hooks/useTransferListPlayers';
import { Theme } from '../../../theme/theme';

interface StyledProps {
  theme: Theme;
}

interface OptionItemProps extends StyledProps {
  isSelected: boolean;
}

const ModalOverlay = styled.TouchableOpacity`
  flex: 1;
  background-color: rgba(0, 0, 0, 0.5);
  justify-content: center;
  align-items: center;
  padding: 20px;
`;

const ModalContent = styled.View<StyledProps>`
  background-color: ${(props) => props.theme.colors.surface};
  border-radius: 12px;
  padding: 24px;
  width: 100%;
  max-width: 400px;
  z-index: 99999;
  position: relative;
  overflow: visible;
  align-self: center;
`;

const ModalHeader = styled.View`
  flex-direction: row;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24px;
`;

const ModalTitle = styled(Text)<StyledProps>`
  font-size: 20px;
  font-family: ${(props) => props.theme.typography.bold};
  color: ${(props) => props.theme.colors.text.primary};
`;

const CloseButton = styled.TouchableOpacity`
  padding: 4px;
`;

const CloseButtonText = styled(Text)<StyledProps>`
  font-size: 18px;
  color: ${(props) => props.theme.colors.text.secondary};
`;

const SectionContainer = styled.View`
  margin-bottom: 20px;
  ${Platform.OS === 'web' ? 'z-index: auto;' : ''}
`;

const SectionTitle = styled(Text)<StyledProps>`
  font-size: 16px;
  font-family: ${(props) => props.theme.typography.bold};
  color: ${(props) => props.theme.colors.text.primary};
  margin-bottom: 12px;
`;

const DropdownContainer = styled.View<{ zIndex?: number }>`
  position: relative;
  margin-bottom: 12px;
  z-index: ${(props) => props.zIndex};
`;

const DropdownButton = styled.TouchableOpacity<StyledProps>`
  flex-direction: row;
  justify-content: space-between;
  align-items: center;
  padding: 12px 16px;
  background-color: ${(props) => props.theme.colors.background};
  border-radius: 8px;
  border-width: 1px;
  border-color: ${(props) => props.theme.colors.border};
`;

const DropdownText = styled(Text)<StyledProps>`
  font-size: 14px;
  color: ${(props) => props.theme.colors.text.primary};
`;

const DropdownOptions = styled.View<StyledProps>`
  position: absolute;
  top: 100%;
  left: 0;
  right: 0;
  background-color: ${(props) => props.theme.colors.surface};
  border-radius: 8px;
  border-width: 1px;
  border-color: ${(props) => props.theme.colors.border};
  margin-top: 4px;
  overflow: hidden;
  ${Platform.select({
    ios: `
      shadow-color: #000;
      shadow-offset: 0px 2px;
      shadow-opacity: 0.25;
      shadow-radius: 3.84px;
    `,
    android: `
      elevation: 30;
    `,
    web: `
      box-shadow: 0px 2px 4px rgba(0, 0, 0, 0.25);
    `,
  })}
`;

const OptionItem = styled.TouchableOpacity<OptionItemProps>`
  padding: 12px 16px;
  background-color: ${(props) =>
    props.isSelected ? props.theme.colors.primary + '20' : props.theme.colors.surface};
`;

const OptionText = styled(Text)<OptionItemProps>`
  font-size: 14px;
  color: ${(props) =>
    props.isSelected ? props.theme.colors.primary : props.theme.colors.text.primary};
`;

const PriceInputContainer = styled.View`
  flex-direction: row;
  gap: 12px;
`;

const PriceInputWrapper = styled.View`
  flex: 1;
`;

const PriceInputLabel = styled(Text)<StyledProps>`
  font-size: 14px;
  color: ${(props) => props.theme.colors.text.secondary};
  margin-bottom: 6px;
`;

const PriceInput = styled(TextInput)<StyledProps>`
  padding: 12px 16px;
  background-color: ${(props) => props.theme.colors.background};
  border-radius: 8px;
  border-width: 1px;
  border-color: ${(props) => props.theme.colors.border};
  font-size: 14px;
  color: ${(props) => props.theme.colors.text.primary};
`;

const ButtonContainer = styled.View`
  flex-direction: row;
  gap: 12px;
  margin-top: 24px;
`;

const Button = styled.TouchableOpacity<StyledProps & { variant?: 'primary' | 'secondary' }>`
  flex: 1;
  padding: 12px;
  border-radius: 8px;
  align-items: center;
  background-color: ${(props) =>
    props.variant === 'primary' ? props.theme.colors.primary : 'transparent'};
  border-width: ${(props) => (props.variant === 'primary' ? '0' : '1px')};
  border-color: ${(props) => props.theme.colors.border};
`;

const ButtonText = styled(Text)<StyledProps & { variant?: 'primary' | 'secondary' }>`
  font-size: 16px;
  font-family: ${(props) => props.theme.typography.bold};
  color: ${(props) => (props.variant === 'primary' ? 'white' : props.theme.colors.text.primary)};
`;

interface TransferListFilterModalProps {
  visible: boolean;
  onClose: () => void;
  filters: Partial<TransferListFilters>;
  onApplyFilters: (filters: Partial<TransferListFilters>) => void;
}

const sortOptions = [
  { value: undefined, label: 'Default' },
  { value: 'auctionCurrentPrice', label: 'Current Price' },
  { value: 'auctionEndTime', label: 'Auction End Time' },
] as const;

const sortDirectionOptions = [
  { value: 'ASC', label: 'Ascending' },
  { value: 'DESC', label: 'Descending' },
] as const;

export const TransferListFilterModal: React.FC<TransferListFilterModalProps> = ({
  visible,
  onClose,
  filters,
  onApplyFilters,
}) => {
  const [localFilters, setLocalFilters] = useState<Partial<TransferListFilters>>(filters);

  // Force modal to be on top for web platforms
  useEffect(() => {
    if (Platform.OS === 'web' && visible) {
      const style = document.createElement('style');
      style.textContent = `
        .rn-modal {
          z-index: 999999 !important;
        }
        .rn-modal > div {
          z-index: 999999 !important;
        }
      `;
      document.head.appendChild(style);
      return () => {
        document.head.removeChild(style);
      };
    }
  }, [visible]);
  const [showSortByDropdown, setShowSortByDropdown] = useState(false);
  const [showSortDirectionDropdown, setShowSortDirectionDropdown] = useState(false);

  const handleApply = () => {
    onApplyFilters(localFilters);
    onClose();
  };

  const handleReset = () => {
    const resetFilters = {};
    setLocalFilters(resetFilters);
    onApplyFilters(resetFilters);
    onClose();
  };

  const closeAllDropdowns = () => {
    setShowSortByDropdown(false);
    setShowSortDirectionDropdown(false);
  };

  const getSortByLabel = () => {
    const option = sortOptions.find((opt) => opt.value === localFilters.sortBy);
    return option?.label || 'Default';
  };

  const getSortDirectionLabel = () => {
    const option = sortDirectionOptions.find((opt) => opt.value === localFilters.sortDirection);
    return option?.label || 'Ascending';
  };

  const modalStyle = Platform.select({
    web: {
      zIndex: 999999,
    },
    default: {
      zIndex: 999999,
    },
  });

  return (
    <Modal
      visible={visible}
      transparent
      animationType="fade"
      onRequestClose={onClose}
      style={modalStyle}
    >
      <ModalOverlay onPress={closeAllDropdowns} activeOpacity={1}>
        <ModalContent>
          <TouchableOpacity activeOpacity={1} onPress={(e) => e.stopPropagation()}>
            <ModalHeader>
              <ModalTitle>Filter & Sort</ModalTitle>
              <CloseButton onPress={onClose}>
                <CloseButtonText>✕</CloseButtonText>
              </CloseButton>
            </ModalHeader>

            <SectionContainer>
              <SectionTitle>Sort Options</SectionTitle>

              <DropdownContainer zIndex={999}>
                <DropdownButton
                  onPress={() => {
                    setShowSortByDropdown(!showSortByDropdown);
                    setShowSortDirectionDropdown(false);
                  }}
                >
                  <DropdownText>Sort by: {getSortByLabel()}</DropdownText>
                  <DropdownText>{showSortByDropdown ? '▲' : '▼'}</DropdownText>
                </DropdownButton>
                {showSortByDropdown && (
                  <DropdownOptions>
                    {sortOptions.map((option) => (
                      <OptionItem
                        key={option.label}
                        onPress={() => {
                          setLocalFilters({ ...localFilters, sortBy: option.value });
                          setShowSortByDropdown(false);
                        }}
                        isSelected={localFilters.sortBy === option.value}
                      >
                        <OptionText isSelected={localFilters.sortBy === option.value}>
                          {option.label}
                        </OptionText>
                      </OptionItem>
                    ))}
                  </DropdownOptions>
                )}
              </DropdownContainer>

              <DropdownContainer zIndex={998}>
                <DropdownButton
                  onPress={() => {
                    setShowSortDirectionDropdown(!showSortDirectionDropdown);
                    setShowSortByDropdown(false);
                  }}
                >
                  <DropdownText>Direction: {getSortDirectionLabel()}</DropdownText>
                  <DropdownText>{showSortDirectionDropdown ? '▲' : '▼'}</DropdownText>
                </DropdownButton>
                {showSortDirectionDropdown && (
                  <DropdownOptions>
                    {sortDirectionOptions.map((option) => (
                      <OptionItem
                        key={option.value}
                        onPress={() => {
                          setLocalFilters({ ...localFilters, sortDirection: option.value });
                          setShowSortDirectionDropdown(false);
                        }}
                        isSelected={localFilters.sortDirection === option.value}
                      >
                        <OptionText isSelected={localFilters.sortDirection === option.value}>
                          {option.label}
                        </OptionText>
                      </OptionItem>
                    ))}
                  </DropdownOptions>
                )}
              </DropdownContainer>
            </SectionContainer>

            <SectionContainer>
              <SectionTitle>Price Range</SectionTitle>
              <PriceInputContainer>
                <PriceInputWrapper>
                  <PriceInputLabel>Min Price</PriceInputLabel>
                  <PriceInput
                    value={localFilters.minPrice || ''}
                    onChangeText={(text) => setLocalFilters({ ...localFilters, minPrice: text })}
                    placeholder="0"
                    keyboardType="numeric"
                  />
                </PriceInputWrapper>
                <PriceInputWrapper>
                  <PriceInputLabel>Max Price</PriceInputLabel>
                  <PriceInput
                    value={localFilters.maxPrice || ''}
                    onChangeText={(text) => setLocalFilters({ ...localFilters, maxPrice: text })}
                    placeholder="No limit"
                    keyboardType="numeric"
                  />
                </PriceInputWrapper>
              </PriceInputContainer>
            </SectionContainer>

            <ButtonContainer>
              <Button variant="secondary" onPress={handleReset}>
                <ButtonText variant="secondary">Reset</ButtonText>
              </Button>
              <Button variant="primary" onPress={handleApply}>
                <ButtonText variant="primary">Apply</ButtonText>
              </Button>
            </ButtonContainer>
          </TouchableOpacity>
        </ModalContent>
      </ModalOverlay>
    </Modal>
  );
};
