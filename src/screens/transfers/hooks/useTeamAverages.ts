import { useEffect, useState } from 'react';
import { Player } from '../../../models/player';
import { PositionFilter } from './useScoutingTabLogic';

export const useTeamAverages = (teamPlayers?: Player[]) => {
  const [teamAverages, setTeamAverages] = useState<Record<PositionFilter, Record<string, number>>>({
    All: {},
    Goalkeeper: {},
    Defender: {},
    Midfielder: {},
    Attacker: {},
  });

  // Calculate team average attributes for each position category
  useEffect(() => {
    if (teamPlayers && teamPlayers.length > 0) {
      const calculatePositionAverages = (players: Player[], attributeKeys: string[]) => {
        const sum = attributeKeys.reduce(
          (acc, key) => {
            acc[key] = 0;
            return acc;
          },
          {} as Record<string, number>
        );

        let count = 0;

        players.forEach((player) => {
          attributeKeys.forEach((key) => {
            // @ts-ignore - we know these keys exist in the attributes
            sum[key] += player.attributes[key];
          });
          count++;
        });

        return attributeKeys.reduce(
          (acc, key) => {
            acc[key] = count > 0 ? sum[key] / count : 0;
            return acc;
          },
          {} as Record<string, number>
        );
      };

      const averages: Record<PositionFilter, Record<string, number>> = {
        All: calculatePositionAverages(teamPlayers, [
          'reflexes',
          'positioning',
          'shotStopping',
          'tackling',
          'marking',
          'heading',
          'passing',
          'vision',
          'ballControl',
          'finishing',
          'pace',
          'crossing',
        ]),
        Goalkeeper: calculatePositionAverages(teamPlayers, [
          'reflexes',
          'positioning',
          'shotStopping',
        ]),
        Defender: calculatePositionAverages(teamPlayers, ['tackling', 'marking', 'heading']),
        Midfielder: calculatePositionAverages(teamPlayers, [
          'passing',
          'vision',
          'ballControl',
        ]),
        Attacker: calculatePositionAverages(teamPlayers, ['finishing', 'pace', 'crossing']),
      };

      setTeamAverages(averages);
    }
  }, [teamPlayers]);

  return teamAverages;
};
