import { useCallback, useEffect, useRef, useState } from 'react';
import { useDataCache } from '../../../context/DataCacheContext';
import { TransferListPlayer } from '../../../models/player';
import { logger } from '../../../utils/logger';

export type PositionFilterType = 'All' | 'Goalkeeper' | 'Defender' | 'Midfielder' | 'Attacker';

interface UseTransferListLogicProps {
  transferListData?: {
    players: TransferListPlayer[];
    pagination?: any;
  };
  myBidsData?: {
    players: TransferListPlayer[];
  };
  isLoadingMore: boolean;
  lastEvaluatedKey?: string;
}

export const useTransferListLogic = ({
  transferListData,
  /*myBidsData,*/
  isLoadingMore,
  lastEvaluatedKey,
}: UseTransferListLogicProps) => {
  const [transferListPlayers, setTransferListPlayers] = useState<TransferListPlayer[]>([]);
  const [selectedPlayerId, setSelectedPlayerId] = useState<string | null>(null);
  const [positionFilter, setPositionFilter] = useState<PositionFilterType>('All');
  const [reachedEnd, setReachedEnd] = useState(false);
  const { cache, findPlayer /*, setMyBidsPlayers */ } = useDataCache();

  // Track if this is the initial load or a pagination load
  const isInitialLoad = useRef(true);

  // Helper function to get the pagination key from various possible locations
  const getPaginationKey = useCallback(
    (data: any): string | undefined => {
      if (!data) return undefined;
      if (reachedEnd) return undefined; // Don't return a key if we've reached the end

      // Check in pagination object
      if (data.pagination) {
        if (data.pagination.lastEvaluatedKey) return data.pagination.lastEvaluatedKey;
        if (data.pagination.nextToken) return data.pagination.nextToken;
        if (data.pagination.pageId) return data.pagination.pageId;
      }

      // Check at root level
      if (data.lastEvaluatedKey) return data.lastEvaluatedKey;
      if (data.nextToken) return data.nextToken;
      if (data.pageId) return data.pageId;

      return undefined;
    },
    [reachedEnd]
  );

  /*  // Effect for handling my bids data
  useEffect(() => {
    if (myBidsData) {
      const players = myBidsData.players || [];
      setMyBidsPlayers(players);
    }
  }, [myBidsData]);*/

  // Effect for handling transfer list data
  useEffect(() => {
    if (transferListData) {
      // Check if players property exists, if not, try to handle different response structures
      const players = transferListData.players || [];

      // Check if we've reached the end of the list
      if (isLoadingMore && players.length === 0) {
        logger.log('Received empty page, reached end of list');
        setReachedEnd(true);
        return;
      }

      if (isInitialLoad.current || (!lastEvaluatedKey && !isLoadingMore)) {
        // This is the initial load
        logger.log('Setting initial players:', players.length);
        setTransferListPlayers(players);
        isInitialLoad.current = false;

        // Reset reached end flag on initial load
        setReachedEnd(false);
      } else if (isLoadingMore || lastEvaluatedKey) {
        // This is a pagination load
        logger.log('Adding more players:', players.length);
        setTransferListPlayers((prev) => {
          if (!Array.isArray(prev)) {
            logger.warn('Previous players is not an array:', prev);
            return players;
          }
          if (!Array.isArray(players)) {
            logger.warn('New players is not an array:', players);
            return prev;
          }

          // Filter out duplicates before combining
          const existingPlayerIds = new Set(prev.map((player) => player.playerId));
          const uniqueNewPlayers = players.filter(
            (player) => !existingPlayerIds.has(player.playerId)
          );

          // If we got no new unique players, we've reached the end
          if (uniqueNewPlayers.length === 0 && isLoadingMore) {
            logger.log('No new unique players, reached end of list');
            setReachedEnd(true);
          }

          // Combine previous and unique new players
          const combinedPlayers = [...prev, ...uniqueNewPlayers];
          logger.log('Combined players count:', combinedPlayers.length);
          return combinedPlayers;
        });
      }
    }
  }, [transferListData, isLoadingMore, lastEvaluatedKey, getPaginationKey]);

  const handlePositionSelect = (position: PositionFilterType) => {
    setPositionFilter(position);
  };

  const handlePlayerSelect = (player: TransferListPlayer) => {
    setSelectedPlayerId(player.playerId);
  };

  const clearSelectedPlayer = () => {
    setSelectedPlayerId(null);
  };

  // Get the current player data from cache
  const selectedPlayer = selectedPlayerId ? findPlayer(selectedPlayerId) as TransferListPlayer | null : null;

  return {
    transferListPlayers,
    myBidsPlayers: cache.players.myBidsPlayers,
    selectedPlayer,
    selectedPlayerId,
    positionFilter,
    reachedEnd,
    getPaginationKey,
    handlePositionSelect,
    handlePlayerSelect,
    clearSelectedPlayer,
  };
};
