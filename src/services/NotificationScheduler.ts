import AsyncStorage from '@react-native-async-storage/async-storage';
import * as Notifications from 'expo-notifications';
import { SchedulableTriggerInputTypes } from 'expo-notifications/build/Notifications.types';
import { Platform } from 'react-native';
import { Fixture } from '../models/fixture';
import { logger } from '../utils/logger';

/**
 * Configure notification behaviour
 */
export const configureNotifications = () => {
  Notifications.setNotificationHandler({
    handleNotification: async () => ({
      shouldShowAlert: true,
      shouldPlaySound: true,
      shouldSetBadge: false,
      shouldShowBanner: true,
      shouldShowList: false,
    }),
  });
};

/**
 * Schedule notifications for all future fixtures
 */
const scheduleFixtureNotification = async (fixture: Fixture): Promise<void> => {
  try {
    const { status } = await Notifications.getPermissionsAsync();
    if (status !== 'granted') {
      logger.log('Notification permissions not granted');
      return;
    }

    const fixtureDate = new Date(fixture.date);
    const now = new Date();

    // Schedule notification 30 minutes before fixture
    const notificationTime = new Date(fixtureDate.getTime() - 30 * 60 * 1000);

    if (notificationTime > now) {
      await Notifications.scheduleNotificationAsync({
        content: {
          title: `Upcoming Match: ${fixture.homeTeamName} vs ${fixture.awayTeamName}`,
          body: "Don't forget to set your lineup!",
          sound: true,
          priority: Notifications.AndroidNotificationPriority.HIGH,
        },
        trigger: {
          date: notificationTime,
          type: SchedulableTriggerInputTypes.DATE,
        },
      });
    }
  } catch (error) {
    logger.error('Error scheduling fixture notification:', error);
  }
};

/**
 * Cancel all scheduled match notifications
 */
export const cancelAllMatchNotifications = async (): Promise<void> => {
  await Notifications.cancelAllScheduledNotificationsAsync();

  console.log(
    'Cancelled all match notifications',
    Notifications.getAllScheduledNotificationsAsync()
  );
};

/**
 * Schedule all match notifications based on user preferences
 */
export const scheduleAllMatchNotifications = async (data: Fixture[]): Promise<void> => {
  try {
    if (Platform.OS === 'web') return;

    const preMatchPushEnabled = await AsyncStorage.getItem('@notification_preMatch_push');

    if (preMatchPushEnabled === 'true') {
      logger.log('Scheduling match notifications...');
      await cancelAllMatchNotifications();
      await Promise.all(data.map(scheduleFixtureNotification));
    } else {
      logger.log('Pre-match push notifications disabled, not scheduling');
      await cancelAllMatchNotifications();
    }
  } catch (error) {
    logger.error('Error managing match notifications:', error);
  }
};

/**
 * Schedule a test notification (for debugging - triggers in 10 seconds)
 */
export const scheduleTestNotification = async (): Promise<void> => {
  try {
    const { status } = await Notifications.getPermissionsAsync();
    if (status !== 'granted') {
      logger.log('Notification permissions not granted');
      return;
    }

    const notificationId = await Notifications.scheduleNotificationAsync({
      content: {
        title: 'Test Match Reminder',
        body: 'This is a test notification. The next match starts in 30 minutes. Make sure you pick your best team!',
        sound: true,
        priority: Notifications.AndroidNotificationPriority.HIGH,
      },
      trigger: {
        seconds: 10,
        type: SchedulableTriggerInputTypes.TIME_INTERVAL,
      },
    });

    logger.log(`Scheduled test notification with ID: ${notificationId}`);
  } catch (error) {
    logger.error('Error scheduling test notification:', error);
  }
};

/**
 * Get all scheduled notifications (for debugging)
 */
export const getScheduledNotifications = async () => {
  try {
    const notifications = await Notifications.getAllScheduledNotificationsAsync();
    logger.log('Scheduled notifications:', notifications);
    return notifications;
  } catch (error) {
    logger.error('Error getting scheduled notifications:', error);
    return [];
  }
};
