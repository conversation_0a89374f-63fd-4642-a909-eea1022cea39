export interface Theme {
  colors: {
    background: string;
    surface: string;
    text: {
      primary: string;
      secondary: string;
      tertiary: string;
    };
    button: {
      primary: string;
      secondary: string;
      success: string;
      warning: string;
      error: string;
    };
    buttonBorder: {
      primary: string;
      secondary: string;
      success: string;
      warning: string;
      error: string;
    };
    primary: string;
    shadow: string;
    error: string;
    warning: string;
    success: string;
    border: string;
    starPrimary: string;
    starSecondary: string;
  };
  typography: {
    regular: string;
    bold: string;
  };
}

export const lightTheme: Theme = {
  colors: {
    background: '#FFFFFF',
    surface: '#F5F5F5',
    text: {
      primary: '#242424',
      secondary: 'rgba(0, 0, 0, 0.87)',
      tertiary: 'rgba(255,255,255)',
    },
    button: {
      primary: '#2196F3',
      secondary: '#cdcdcd',
      success: '#4CAF50',
      warning: '#FFD700',
      error: '#F44336',
    },
    buttonBorder: {
      primary: '#2196F3',
      secondary: '#cdcdcd',
      success: '#4CAF50',
      warning: '#FFD700',
      error: '#F44336',
    },
    primary: '#2196F3',
    shadow: 'rgba(0, 0, 0, 0.1)',
    error: '#F44336',
    success: '#4CAF50',
    border: '#BDBDBD',
    warning: '#FFA500',
    starPrimary: '#FFD700',
    starSecondary: '#616161',
  },
  typography: {
    regular: 'Nunito',
    bold: 'NunitoBold',
  },
};

export const darkTheme: Theme = {
  colors: {
    background: '#242424',
    surface: '#1e1e1e',
    text: {
      primary: 'rgba(255, 255, 255, 0.87)',
      secondary: 'rgba(255, 255, 255, 0.60)',
      tertiary: 'rgb(0,0,0)',
    },
    button: {
      primary: '#64B5F6',
      secondary: '#1e1e1e',
      success: '#4CAF50',
      warning: '#FFD700',
      error: '#F44336',
    },
    buttonBorder: {
      primary: '#64B5F6',
      secondary: '#404040',
      success: '#4CAF50',
      warning: '#FFD700',
      error: '#F44336',
    },
    primary: '#64B5F6',
    shadow: 'rgba(0, 0, 0, 0.3)',
    error: '#FF5252',
    success: '#81C784',
    border: '#424242',
    warning: '#FFA500',
    starPrimary: '#FFD700',
    starSecondary: '#808080',
  },
  typography: {
    regular: 'Nunito',
    bold: 'NunitoBold',
  },
};
