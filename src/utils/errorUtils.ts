/**
 * Utility functions for handling and formatting API errors
 */

/**
 * Extract a user-friendly error message from various error formats
 */
export function extractErrorMessage(error: any, defaultMessage: string = 'An unexpected error occurred'): string {
  // Handle axios errors
  if (error?.response?.data?.message) {
    return error.response.data.message;
  }
  
  // Handle axios errors with different structure
  if (error?.response?.data?.error) {
    return error.response.data.error;
  }
  
  // Handle standard Error objects
  if (error?.message) {
    return error.message;
  }
  
  // Handle string errors
  if (typeof error === 'string') {
    return error;
  }
  
  // Handle network errors
  if (error?.code === 'NETWORK_ERROR' || error?.message?.includes('Network Error')) {
    return 'Network error. Please check your internet connection and try again.';
  }
  
  // Handle timeout errors
  if (error?.code === 'ECONNABORTED' || error?.message?.includes('timeout')) {
    return 'Request timed out. Please try again.';
  }
  
  // Handle 401 unauthorized
  if (error?.response?.status === 401) {
    return 'Authentication failed. Please log in again.';
  }
  
  // Handle 403 forbidden
  if (error?.response?.status === 403) {
    return 'You do not have permission to perform this action.';
  }
  
  // Handle 404 not found
  if (error?.response?.status === 404) {
    return 'The requested resource was not found.';
  }
  
  // Handle 500 server errors
  if (error?.response?.status >= 500) {
    return 'Server error. Please try again later.';
  }
  
  return defaultMessage;
}

/**
 * Check if an error is a network-related error
 */
export function isNetworkError(error: any): boolean {
  return (
    error?.code === 'NETWORK_ERROR' ||
    error?.message?.includes('Network Error') ||
    error?.code === 'ECONNABORTED' ||
    !navigator.onLine
  );
}

/**
 * Check if an error is a server error (5xx)
 */
export function isServerError(error: any): boolean {
  return error?.response?.status >= 500;
}

/**
 * Check if an error is an authentication error
 */
export function isAuthError(error: any): boolean {
  return error?.response?.status === 401;
}
