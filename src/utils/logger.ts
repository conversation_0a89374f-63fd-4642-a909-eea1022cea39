import * as Sentry from '@sentry/react-native';

class Logger {
  debug(...args: any[]) {
    if (__DEV__) {
      console.log(...args);
    }
  }

  log(...args: any[]) {
    console.log(...args);
    Sentry.addBreadcrumb({
      message: args.map(String).join(' '),
      level: 'log',
    });
  }

  info(...args: any[]) {
    console.info(...args);
    Sentry.addBreadcrumb({
      message: args.map(String).join(' '),
      level: 'info',
    });
  }

  warn(...args: any[]) {
    console.warn(...args);
    Sentry.addBreadcrumb({
      message: args.map(String).join(' '),
      level: 'warning',
    });
  }

  error(...args: any[]) {
    console.error(...args);
    // If the first argument is an Error, use captureException
    if (args[0] instanceof Error) {
      Sentry.captureException(args[0]);
    } else {
      Sentry.captureException(new Error(args.map(String).join(' ')));
    }
  }
}

export const logger = new Logger();
