<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <title>Jumpers for Goalposts - Request Data Deletion</title>
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <link href="https://fonts.googleapis.com/css?family=Nunito:400,700&display=swap" rel="stylesheet">
    <style>
        body {
            font-family: 'Nunito', sans-serif;
            background: #fafbfc;
            color: #222;
            margin: 0;
            padding: 0;
        }
        .container {
            max-width: 700px;
            margin: 40px auto;
            background: #fff;
            border-radius: 10px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.05);
            padding: 32px 24px 24px 24px;
        }
        .logo {
            display: flex;
            justify-content: center;
            margin-bottom: 24px;
        }
        .logo img {
            max-width: 180px;
            height: auto;
        }
        h1 {
            text-align: center;
            font-weight: 700;
            margin-bottom: 24px;
        }
        h2 {
            font-weight: 700;
            margin-top: 32px;
        }
        p, li {
            font-size: 1.08em;
            line-height: 1.7;
        }
        ul {
            margin-left: 20px;
        }
    </style>
</head>
<body>
<div class="container">
    <div class="logo">
        <img src="logo.png" alt="Logo">
    </div>
    <h1>Request Data Deletion</h1>
    <p>Thank you for your interest in deleting your data from our platform. We understand that you may have concerns about the data we collect and how it is used.</p>
    <p>Please note that deleting your data is a permanent action and cannot be undone. Once your data is deleted, it cannot be recovered.</p>
    <p>Deletion will remove all personal details from the game, your team may be deleted or reset and replaced by AI. All purchases made with real money will not be refunded.</p>
    <p>If you still wish to proceed with deleting your data, please contact us at <a href="mailto:<EMAIL>"><EMAIL></a>. Please include your account details (email address used to create your account and user id from the game settings screen) in your request.</p>
</div>
</body>
</html>